package service

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"payment-gateway/internal/gateway/dto"
	"payment-gateway/internal/gateway/model"
	"payment-gateway/internal/gateway/repository"
	"payment-gateway/internal/shared/constants"
	"payment-gateway/internal/shared/errors"
	"payment-gateway/internal/shared/utils"
)

// AggregateCodeService 聚合码服务接口
type AggregateCodeService interface {
	// CreateAggregateCode 创建聚合码
	CreateAggregateCode(ctx context.Context, req *dto.CreateAggregateCodeRequest) (*dto.CreateAggregateCodeResponse, error)

	// GetAggregateCode 获取聚合码信息
	GetAggregateCode(ctx context.Context, codeID string) (*dto.GetAggregateCodeResponse, error)

	// UpdateAggregateCode 更新聚合码
	UpdateAggregateCode(ctx context.Context, req *dto.UpdateAggregateCodeRequest) error

	// ListAggregateCodes 获取聚合码列表
	ListAggregateCodes(ctx context.Context, req *dto.ListAggregateCodesRequest) (*dto.ListAggregateCodesResponse, error)

	// DeleteAggregateCode 删除聚合码
	DeleteAggregateCode(ctx context.Context, codeID string) error

	// SmartRoute 智能路由 - 根据用户代理推荐最佳支付渠道
	SmartRoute(ctx context.Context, req *dto.SmartRouteRequest) (*dto.SmartRouteResponse, error)

	// GetPaymentPageData 获取支付页面数据
	GetPaymentPageData(ctx context.Context, codeID, userAgent string) (*dto.PaymentPageData, error)

	// UpdateScanCount 更新扫码次数
	UpdateScanCount(ctx context.Context, codeID string) error

	// ProcessExpiredCodes 处理过期的聚合码
	ProcessExpiredCodes(ctx context.Context) error
}

// aggregateCodeService 聚合码服务实现
type aggregateCodeService struct {
	aggregateCodeRepo  repository.AggregateCodeRepository
	channelConfigRepo  repository.PaymentChannelConfigRepository
	paymentChannelRepo repository.PaymentChannelRepository
}

// NewAggregateCodeService 创建聚合码服务实例
func NewAggregateCodeService(
	aggregateCodeRepo repository.AggregateCodeRepository,
	channelConfigRepo repository.PaymentChannelConfigRepository,
	paymentChannelRepo repository.PaymentChannelRepository,
) AggregateCodeService {
	return &aggregateCodeService{
		aggregateCodeRepo:  aggregateCodeRepo,
		channelConfigRepo:  channelConfigRepo,
		paymentChannelRepo: paymentChannelRepo,
	}
}

// CreateAggregateCode 创建聚合码
func (s *aggregateCodeService) CreateAggregateCode(ctx context.Context, req *dto.CreateAggregateCodeRequest) (*dto.CreateAggregateCodeResponse, error) {
	// 验证支付渠道是否有效
	if err := s.validateSupportedChannels(ctx, req.MerchantID, req.SupportedChannels); err != nil {
		return nil, err
	}

	// 生成聚合码ID
	codeID := utils.GenerateID("AGG")

	// 设置默认值
	if req.Currency == "" {
		req.Currency = "CNY"
	}
	if req.ExpireMinutes <= 0 {
		req.ExpireMinutes = 30 // 默认30分钟过期
	}

	// 序列化支持的渠道
	channelsJSON, err := json.Marshal(req.SupportedChannels)
	if err != nil {
		return nil, errors.NewBusinessError(constants.ErrCodeInternalError, "支付渠道序列化失败")
	}

	// 序列化品牌配置
	var brandConfigJSON string
	if req.BrandConfig != nil {
		brandConfigBytes, err := json.Marshal(req.BrandConfig)
		if err != nil {
			return nil, errors.NewBusinessError(constants.ErrCodeInternalError, "品牌配置序列化失败")
		}
		brandConfigJSON = string(brandConfigBytes)
	}

	// 计算过期时间
	expireTime := time.Now().Add(time.Duration(req.ExpireMinutes) * time.Minute)

	// 生成支付页面URL
	payURL := fmt.Sprintf("/pay/%s", codeID)

	// 创建聚合码模型
	aggregateCode := &model.AggregateCode{
		CodeID:            codeID,
		MerchantID:        req.MerchantID,
		AppID:             req.AppID,
		Subject:           req.Subject,
		Body:              req.Body,
		Amount:            req.Amount,
		Currency:          req.Currency,
		SupportedChannels: string(channelsJSON),
		BrandConfig:       brandConfigJSON,
		NotifyURL:         req.NotifyURL,
		ReturnURL:         req.ReturnURL,
		ExpireTime:        expireTime,
		Status:            model.AggregateCodeStatusEnabled,
		PayURL:            payURL,
		ExtraData:         req.ExtraData,
		Remark:            req.Remark,
	}

	// 保存到数据库
	if err := s.aggregateCodeRepo.Create(ctx, aggregateCode); err != nil {
		return nil, err
	}

	// 生成二维码（这里可以集成二维码生成服务）
	qrCodeURL := s.generateQRCodeURL(payURL)

	// 更新二维码URL
	aggregateCode.QRCodeURL = qrCodeURL
	if err := s.aggregateCodeRepo.Update(ctx, aggregateCode); err != nil {
		// 记录日志，但不影响主流程
		// logger.Error("更新二维码URL失败", err)
	}

	return &dto.CreateAggregateCodeResponse{
		CodeID:     codeID,
		PayURL:     payURL,
		QRCodeURL:  qrCodeURL,
		ExpireTime: expireTime,
	}, nil
}

// GetAggregateCode 获取聚合码信息
func (s *aggregateCodeService) GetAggregateCode(ctx context.Context, codeID string) (*dto.GetAggregateCodeResponse, error) {
	aggregateCode, err := s.aggregateCodeRepo.GetByCodeID(ctx, codeID)
	if err != nil {
		return nil, err
	}

	// 反序列化支持的渠道
	var supportedChannels []string
	if err := json.Unmarshal([]byte(aggregateCode.SupportedChannels), &supportedChannels); err != nil {
		return nil, errors.NewBusinessError(constants.ErrCodeInternalError, "支付渠道反序列化失败")
	}

	// 反序列化品牌配置
	var brandConfig *dto.BrandConfig
	if aggregateCode.BrandConfig != "" {
		brandConfig = &dto.BrandConfig{}
		if err := json.Unmarshal([]byte(aggregateCode.BrandConfig), brandConfig); err != nil {
			return nil, errors.NewBusinessError(constants.ErrCodeInternalError, "品牌配置反序列化失败")
		}
	}

	return &dto.GetAggregateCodeResponse{
		CodeID:            aggregateCode.CodeID,
		MerchantID:        aggregateCode.MerchantID,
		AppID:             aggregateCode.AppID,
		Subject:           aggregateCode.Subject,
		Body:              aggregateCode.Body,
		Amount:            aggregateCode.Amount,
		Currency:          aggregateCode.Currency,
		SupportedChannels: supportedChannels,
		BrandConfig:       brandConfig,
		PayURL:            aggregateCode.PayURL,
		QRCodeURL:         aggregateCode.QRCodeURL,
		Status:            aggregateCode.Status,
		ExpireTime:        aggregateCode.ExpireTime,
		ScanCount:         aggregateCode.ScanCount,
		PayCount:          aggregateCode.PayCount,
		TotalAmount:       aggregateCode.TotalAmount,
		CreatedAt:         aggregateCode.CreatedAt,
	}, nil
}

// SmartRoute 智能路由 - 根据用户代理推荐最佳支付渠道
func (s *aggregateCodeService) SmartRoute(ctx context.Context, req *dto.SmartRouteRequest) (*dto.SmartRouteResponse, error) {
	// 获取聚合码信息
	aggregateCode, err := s.aggregateCodeRepo.GetByCodeID(ctx, req.CodeID)
	if err != nil {
		return nil, err
	}

	// 检查是否过期
	if time.Now().After(aggregateCode.ExpireTime) {
		return nil, errors.NewBusinessError(constants.ErrCodeValidation, "聚合码已过期")
	}

	// 反序列化支持的渠道
	var supportedChannels []string
	if err := json.Unmarshal([]byte(aggregateCode.SupportedChannels), &supportedChannels); err != nil {
		return nil, errors.NewBusinessError(constants.ErrCodeInternalError, "支付渠道反序列化失败")
	}

	// 根据User-Agent进行智能路由
	recommendedChannel, routeReason := s.detectPaymentChannel(req.UserAgent, supportedChannels)

	return &dto.SmartRouteResponse{
		RecommendedChannel: recommendedChannel,
		AvailableChannels:  supportedChannels,
		RouteReason:        routeReason,
	}, nil
}

// validateSupportedChannels 验证支持的支付渠道
func (s *aggregateCodeService) validateSupportedChannels(ctx context.Context, merchantID uint, channels []string) error {
	if len(channels) == 0 {
		return errors.NewValidationError("至少需要支持一种支付渠道")
	}

	// 获取商户启用的支付渠道配置
	enabledConfigs, err := s.channelConfigRepo.GetEnabledByMerchant(ctx, merchantID)
	if err != nil {
		return err
	}

	// 创建已启用渠道的映射
	enabledChannels := make(map[string]bool)
	for _, config := range enabledConfigs {
		enabledChannels[config.ChannelCode] = true
	}

	// 验证每个请求的渠道是否已启用
	for _, channel := range channels {
		if !enabledChannels[channel] {
			return errors.NewValidationError(fmt.Sprintf("支付渠道 %s 未启用或配置不存在", channel))
		}
	}

	return nil
}

// detectPaymentChannel 检测推荐的支付渠道
func (s *aggregateCodeService) detectPaymentChannel(userAgent string, supportedChannels []string) (string, string) {
	userAgent = strings.ToLower(userAgent)

	// 创建支持渠道的映射，便于快速查找
	channelMap := make(map[string]bool)
	for _, channel := range supportedChannels {
		channelMap[channel] = true
	}

	// 微信客户端检测
	if strings.Contains(userAgent, strings.ToLower(model.UserAgentWechat)) && channelMap[model.ChannelWechat] {
		return model.ChannelWechat, "检测到微信客户端，推荐使用微信支付"
	}

	// 支付宝客户端检测
	if strings.Contains(userAgent, strings.ToLower(model.UserAgentAlipay)) && channelMap[model.ChannelAlipay] {
		return model.ChannelAlipay, "检测到支付宝客户端，推荐使用支付宝"
	}

	// QQ客户端检测
	if strings.Contains(userAgent, strings.ToLower(model.UserAgentQQ)) && channelMap[model.ChannelQQPay] {
		return model.ChannelQQPay, "检测到QQ客户端，推荐使用QQ钱包"
	}

	// 移动端浏览器检测
	mobilePattern := regexp.MustCompile(`(?i)(mobile|android|iphone|ipad)`)
	if mobilePattern.MatchString(userAgent) {
		// 优先推荐微信支付（移动端使用最广泛）
		if channelMap[model.ChannelWechat] {
			return model.ChannelWechat, "检测到移动端浏览器，推荐使用微信支付"
		}
		if channelMap[model.ChannelAlipay] {
			return model.ChannelAlipay, "检测到移动端浏览器，推荐使用支付宝"
		}
	}

	// 默认推荐第一个支持的渠道
	if len(supportedChannels) > 0 {
		return supportedChannels[0], "使用默认支付渠道"
	}

	return "", "无可用支付渠道"
}

// generateQRCodeURL 生成二维码URL（这里是模拟实现）
func (s *aggregateCodeService) generateQRCodeURL(payURL string) string {
	// 实际项目中，这里应该调用二维码生成服务
	// 例如：qrcode.GenerateQRCode(payURL)
	return fmt.Sprintf("/qrcode?url=%s", payURL)
}

// UpdateScanCount 更新扫码次数
func (s *aggregateCodeService) UpdateScanCount(ctx context.Context, codeID string) error {
	return s.aggregateCodeRepo.UpdateScanCount(ctx, codeID)
}

// UpdateAggregateCode 更新聚合码
func (s *aggregateCodeService) UpdateAggregateCode(ctx context.Context, req *dto.UpdateAggregateCodeRequest) error {
	// 获取现有聚合码
	aggregateCode, err := s.aggregateCodeRepo.GetByCodeID(ctx, req.CodeID)
	if err != nil {
		return err
	}

	// 更新字段
	if req.Subject != "" {
		aggregateCode.Subject = req.Subject
	}
	if req.Body != "" {
		aggregateCode.Body = req.Body
	}
	if len(req.SupportedChannels) > 0 {
		// 验证支付渠道
		if err := s.validateSupportedChannels(ctx, aggregateCode.MerchantID, req.SupportedChannels); err != nil {
			return err
		}

		channelsJSON, err := json.Marshal(req.SupportedChannels)
		if err != nil {
			return errors.NewBusinessError(constants.ErrCodeInternalError, "支付渠道序列化失败")
		}
		aggregateCode.SupportedChannels = string(channelsJSON)
	}
	if req.BrandConfig != nil {
		brandConfigBytes, err := json.Marshal(req.BrandConfig)
		if err != nil {
			return errors.NewBusinessError(constants.ErrCodeInternalError, "品牌配置序列化失败")
		}
		aggregateCode.BrandConfig = string(brandConfigBytes)
	}
	if req.NotifyURL != "" {
		aggregateCode.NotifyURL = req.NotifyURL
	}
	if req.ReturnURL != "" {
		aggregateCode.ReturnURL = req.ReturnURL
	}
	if req.Status != nil {
		aggregateCode.Status = *req.Status
	}
	if req.Remark != "" {
		aggregateCode.Remark = req.Remark
	}

	return s.aggregateCodeRepo.Update(ctx, aggregateCode)
}

// ListAggregateCodes 获取聚合码列表
func (s *aggregateCodeService) ListAggregateCodes(ctx context.Context, req *dto.ListAggregateCodesRequest) (*dto.ListAggregateCodesResponse, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 获取聚合码列表
	aggregateCodes, total, err := s.aggregateCodeRepo.List(ctx, req.MerchantID, req.AppID, req.Status, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	list := make([]dto.GetAggregateCodeResponse, len(aggregateCodes))
	for i, aggregateCode := range aggregateCodes {
		// 反序列化支持的渠道
		var supportedChannels []string
		if err := json.Unmarshal([]byte(aggregateCode.SupportedChannels), &supportedChannels); err != nil {
			return nil, errors.NewBusinessError(constants.ErrCodeInternalError, "支付渠道反序列化失败")
		}

		// 反序列化品牌配置
		var brandConfig *dto.BrandConfig
		if aggregateCode.BrandConfig != "" {
			brandConfig = &dto.BrandConfig{}
			if err := json.Unmarshal([]byte(aggregateCode.BrandConfig), brandConfig); err != nil {
				return nil, errors.NewBusinessError(constants.ErrCodeInternalError, "品牌配置反序列化失败")
			}
		}

		list[i] = dto.GetAggregateCodeResponse{
			CodeID:            aggregateCode.CodeID,
			MerchantID:        aggregateCode.MerchantID,
			AppID:             aggregateCode.AppID,
			Subject:           aggregateCode.Subject,
			Body:              aggregateCode.Body,
			Amount:            aggregateCode.Amount,
			Currency:          aggregateCode.Currency,
			SupportedChannels: supportedChannels,
			BrandConfig:       brandConfig,
			PayURL:            aggregateCode.PayURL,
			QRCodeURL:         aggregateCode.QRCodeURL,
			Status:            aggregateCode.Status,
			ExpireTime:        aggregateCode.ExpireTime,
			ScanCount:         aggregateCode.ScanCount,
			PayCount:          aggregateCode.PayCount,
			TotalAmount:       aggregateCode.TotalAmount,
			CreatedAt:         aggregateCode.CreatedAt,
		}
	}

	return &dto.ListAggregateCodesResponse{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// DeleteAggregateCode 删除聚合码
func (s *aggregateCodeService) DeleteAggregateCode(ctx context.Context, codeID string) error {
	return s.aggregateCodeRepo.Delete(ctx, codeID)
}

// GetPaymentPageData 获取支付页面数据
func (s *aggregateCodeService) GetPaymentPageData(ctx context.Context, codeID, userAgent string) (*dto.PaymentPageData, error) {
	// 获取聚合码信息
	aggregateCode, err := s.aggregateCodeRepo.GetByCodeID(ctx, codeID)
	if err != nil {
		return nil, err
	}

	// 检查状态
	if aggregateCode.Status != model.AggregateCodeStatusEnabled {
		return nil, errors.NewBusinessError(constants.ErrCodeValidation, "聚合码已禁用")
	}

	// 检查是否过期
	isExpired := time.Now().After(aggregateCode.ExpireTime)
	if isExpired {
		// 更新过期状态
		s.aggregateCodeRepo.UpdateStatus(ctx, codeID, model.AggregateCodeStatusExpired)
		return nil, errors.NewBusinessError(constants.ErrCodeValidation, "聚合码已过期")
	}

	// 反序列化支持的渠道
	var supportedChannels []string
	if err := json.Unmarshal([]byte(aggregateCode.SupportedChannels), &supportedChannels); err != nil {
		return nil, errors.NewBusinessError(constants.ErrCodeInternalError, "支付渠道反序列化失败")
	}

	// 获取可用的支付渠道信息
	availableChannels, err := s.getAvailableChannels(ctx, aggregateCode.MerchantID, supportedChannels)
	if err != nil {
		return nil, err
	}

	// 智能路由推荐
	recommendedChannel, _ := s.detectPaymentChannel(userAgent, supportedChannels)

	// 反序列化品牌配置
	var brandConfig *dto.BrandConfig
	if aggregateCode.BrandConfig != "" {
		brandConfig = &dto.BrandConfig{}
		if err := json.Unmarshal([]byte(aggregateCode.BrandConfig), brandConfig); err != nil {
			return nil, errors.NewBusinessError(constants.ErrCodeInternalError, "品牌配置反序列化失败")
		}
	}

	// 格式化金额
	amountYuan := fmt.Sprintf("%.2f", float64(aggregateCode.Amount)/100)

	return &dto.PaymentPageData{
		CodeID:             codeID,
		Subject:            aggregateCode.Subject,
		Body:               aggregateCode.Body,
		Amount:             aggregateCode.Amount,
		AmountYuan:         amountYuan,
		Currency:           aggregateCode.Currency,
		AvailableChannels:  availableChannels,
		RecommendedChannel: recommendedChannel,
		BrandConfig:        brandConfig,
		ExpireTime:         aggregateCode.ExpireTime,
		IsExpired:          isExpired,
	}, nil
}

// ProcessExpiredCodes 处理过期的聚合码
func (s *aggregateCodeService) ProcessExpiredCodes(ctx context.Context) error {
	// 获取过期的聚合码
	expiredCodes, err := s.aggregateCodeRepo.GetExpiredCodes(ctx, 100)
	if err != nil {
		return err
	}

	if len(expiredCodes) == 0 {
		return nil
	}

	// 提取过期聚合码的ID
	codeIDs := make([]string, len(expiredCodes))
	for i, code := range expiredCodes {
		codeIDs[i] = code.CodeID
	}

	// 批量更新过期状态
	return s.aggregateCodeRepo.BatchUpdateExpiredStatus(ctx, codeIDs)
}

// getAvailableChannels 获取可用的支付渠道信息
func (s *aggregateCodeService) getAvailableChannels(ctx context.Context, merchantID uint, supportedChannels []string) ([]dto.ChannelInfo, error) {
	var availableChannels []dto.ChannelInfo

	// 获取商户的支付渠道配置
	configs, err := s.channelConfigRepo.GetEnabledByMerchant(ctx, merchantID)
	if err != nil {
		return nil, err
	}

	// 创建配置映射
	configMap := make(map[string]*model.PaymentChannelConfig)
	for _, config := range configs {
		configMap[config.ChannelCode] = config
	}

	// 获取系统支付渠道信息
	for _, channelCode := range supportedChannels {
		if config, exists := configMap[channelCode]; exists {
			// 获取渠道基础信息
			channel, err := s.paymentChannelRepo.GetByCode(ctx, channelCode)
			if err != nil {
				continue // 跳过获取失败的渠道
			}

			availableChannels = append(availableChannels, dto.ChannelInfo{
				Code:        channelCode,
				Name:        channel.ChannelName,
				Logo:        channel.Logo,
				Description: channel.Description,
				Available:   true,
				FeeRate:     config.FeeRate,
			})
		}
	}

	return availableChannels, nil
}
