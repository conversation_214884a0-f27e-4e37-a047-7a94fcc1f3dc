package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`              // 响应码
	Message string      `json:"message"`           // 响应消息
	Data    interface{} `json:"data,omitempty"`    // 响应数据
	TraceID string      `json:"trace_id,omitempty"` // 追踪ID
}

// PageResponse 分页响应结构
type PageResponse struct {
	Code    int         `json:"code"`              // 响应码
	Message string      `json:"message"`           // 响应消息
	Data    interface{} `json:"data,omitempty"`    // 响应数据
	Page    *PageInfo   `json:"page,omitempty"`    // 分页信息
	TraceID string      `json:"trace_id,omitempty"` // 追踪ID
}

// PageInfo 分页信息
type PageInfo struct {
	Page     int   `json:"page"`      // 当前页码
	PageSize int   `json:"page_size"` // 每页大小
	Total    int64 `json:"total"`     // 总记录数
	Pages    int   `json:"pages"`     // 总页数
}

// 响应码常量
const (
	CodeSuccess      = 200  // 成功
	CodeBadRequest   = 400  // 请求参数错误
	CodeUnauthorized = 401  // 未授权
	CodeForbidden    = 403  // 禁止访问
	CodeNotFound     = 404  // 资源不存在
	CodeConflict     = 409  // 资源冲突
	CodeTooManyReqs  = 429  // 请求过于频繁
	CodeServerError  = 500  // 服务器内部错误
	CodeServiceUnavailable = 503 // 服务不可用
)

// 业务响应码常量
const (
	CodeBusinessError     = 1000 // 业务错误
	CodeValidationError   = 1001 // 参数验证错误
	CodeDuplicateError    = 1002 // 重复操作错误
	CodeInsufficientFunds = 1003 // 余额不足
	CodeOrderExpired      = 1004 // 订单已过期
	CodeOrderPaid         = 1005 // 订单已支付
	CodeChannelError      = 1006 // 支付渠道错误
	CodeSignatureError    = 1007 // 签名验证失败
	CodeRiskControl       = 1008 // 风控拦截
	CodeMerchantDisabled  = 1009 // 商户已禁用
	CodeAppDisabled       = 1010 // 应用已禁用
)

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	response := Response{
		Code:    CodeSuccess,
		Message: "success",
		Data:    data,
		TraceID: getTraceID(c),
	}
	c.JSON(http.StatusOK, response)
}

// SuccessWithMessage 成功响应（自定义消息）
func SuccessWithMessage(c *gin.Context, message string, data interface{}) {
	response := Response{
		Code:    CodeSuccess,
		Message: message,
		Data:    data,
		TraceID: getTraceID(c),
	}
	c.JSON(http.StatusOK, response)
}

// SuccessPage 分页成功响应
func SuccessPage(c *gin.Context, data interface{}, page *PageInfo) {
	response := PageResponse{
		Code:    CodeSuccess,
		Message: "success",
		Data:    data,
		Page:    page,
		TraceID: getTraceID(c),
	}
	c.JSON(http.StatusOK, response)
}

// Error 错误响应
func Error(c *gin.Context, code int, message string) {
	response := Response{
		Code:    code,
		Message: message,
		TraceID: getTraceID(c),
	}
	
	// 根据错误码设置HTTP状态码
	httpStatus := getHTTPStatus(code)
	c.JSON(httpStatus, response)
}

// ErrorWithData 错误响应（包含数据）
func ErrorWithData(c *gin.Context, code int, message string, data interface{}) {
	response := Response{
		Code:    code,
		Message: message,
		Data:    data,
		TraceID: getTraceID(c),
	}
	
	// 根据错误码设置HTTP状态码
	httpStatus := getHTTPStatus(code)
	c.JSON(httpStatus, response)
}

// BadRequest 请求参数错误
func BadRequest(c *gin.Context, message string) {
	Error(c, CodeBadRequest, message)
}

// Unauthorized 未授权
func Unauthorized(c *gin.Context, message string) {
	Error(c, CodeUnauthorized, message)
}

// Forbidden 禁止访问
func Forbidden(c *gin.Context, message string) {
	Error(c, CodeForbidden, message)
}

// NotFound 资源不存在
func NotFound(c *gin.Context, message string) {
	Error(c, CodeNotFound, message)
}

// Conflict 资源冲突
func Conflict(c *gin.Context, message string) {
	Error(c, CodeConflict, message)
}

// TooManyRequests 请求过于频繁
func TooManyRequests(c *gin.Context, message string) {
	Error(c, CodeTooManyReqs, message)
}

// InternalServerError 服务器内部错误
func InternalServerError(c *gin.Context, message string) {
	Error(c, CodeServerError, message)
}

// ServiceUnavailable 服务不可用
func ServiceUnavailable(c *gin.Context, message string) {
	Error(c, CodeServiceUnavailable, message)
}

// BusinessError 业务错误
func BusinessError(c *gin.Context, message string) {
	Error(c, CodeBusinessError, message)
}

// ValidationError 参数验证错误
func ValidationError(c *gin.Context, message string) {
	Error(c, CodeValidationError, message)
}

// DuplicateError 重复操作错误
func DuplicateError(c *gin.Context, message string) {
	Error(c, CodeDuplicateError, message)
}

// InsufficientFunds 余额不足
func InsufficientFunds(c *gin.Context, message string) {
	Error(c, CodeInsufficientFunds, message)
}

// OrderExpired 订单已过期
func OrderExpired(c *gin.Context, message string) {
	Error(c, CodeOrderExpired, message)
}

// OrderPaid 订单已支付
func OrderPaid(c *gin.Context, message string) {
	Error(c, CodeOrderPaid, message)
}

// ChannelError 支付渠道错误
func ChannelError(c *gin.Context, message string) {
	Error(c, CodeChannelError, message)
}

// SignatureError 签名验证失败
func SignatureError(c *gin.Context, message string) {
	Error(c, CodeSignatureError, message)
}

// RiskControl 风控拦截
func RiskControl(c *gin.Context, message string) {
	Error(c, CodeRiskControl, message)
}

// MerchantDisabled 商户已禁用
func MerchantDisabled(c *gin.Context, message string) {
	Error(c, CodeMerchantDisabled, message)
}

// AppDisabled 应用已禁用
func AppDisabled(c *gin.Context, message string) {
	Error(c, CodeAppDisabled, message)
}

// getTraceID 获取追踪ID
func getTraceID(c *gin.Context) string {
	if traceID, exists := c.Get("trace_id"); exists {
		if id, ok := traceID.(string); ok {
			return id
		}
	}
	return ""
}

// getHTTPStatus 根据业务错误码获取HTTP状态码
func getHTTPStatus(code int) int {
	switch code {
	case CodeSuccess:
		return http.StatusOK
	case CodeBadRequest, CodeValidationError:
		return http.StatusBadRequest
	case CodeUnauthorized:
		return http.StatusUnauthorized
	case CodeForbidden:
		return http.StatusForbidden
	case CodeNotFound:
		return http.StatusNotFound
	case CodeConflict, CodeDuplicateError:
		return http.StatusConflict
	case CodeTooManyReqs:
		return http.StatusTooManyRequests
	case CodeServerError:
		return http.StatusInternalServerError
	case CodeServiceUnavailable:
		return http.StatusServiceUnavailable
	default:
		// 业务错误码默认返回200，由前端根据code字段处理
		return http.StatusOK
	}
}

// NewPageInfo 创建分页信息
func NewPageInfo(page, pageSize int, total int64) *PageInfo {
	pages := int(total) / pageSize
	if int(total)%pageSize > 0 {
		pages++
	}
	
	return &PageInfo{
		Page:     page,
		PageSize: pageSize,
		Total:    total,
		Pages:    pages,
	}
}
