package repository

import (
	"context"
	"payment-gateway/internal/notification/model"
	"time"

	"gorm.io/gorm"
)

// SMSTemplateRepository 短信模板仓储接口
type SMSTemplateRepository interface {
	// 基础操作
	Create(ctx context.Context, template *model.SMSTemplate) error
	GetByID(ctx context.Context, id uint) (*model.SMSTemplate, error)
	GetByCode(ctx context.Context, templateCode string) (*model.SMSTemplate, error)
	Update(ctx context.Context, template *model.SMSTemplate) error
	Delete(ctx context.Context, id uint) error
	
	// 查询操作
	List(ctx context.Context, condition *SMSTemplateQueryCondition) ([]*model.SMSTemplate, int64, error)
	GetByCategory(ctx context.Context, category string) ([]*model.SMSTemplate, error)
	GetActiveTemplates(ctx context.Context) ([]*model.SMSTemplate, error)
	
	// 状态操作
	UpdateStatus(ctx context.Context, id uint, status int) error
	
	// 验证操作
	CheckCodeExists(ctx context.Context, templateCode string, excludeID uint) bool
}

// SMSLogRepository 短信日志仓储接口
type SMSLogRepository interface {
	// 基础操作
	Create(ctx context.Context, log *model.SMSLog) error
	GetByID(ctx context.Context, id uint) (*model.SMSLog, error)
	Update(ctx context.Context, log *model.SMSLog) error
	List(ctx context.Context, condition *SMSLogQueryCondition) ([]*model.SMSLog, int64, error)
	Delete(ctx context.Context, id uint) error
	
	// 查询操作
	GetByPhone(ctx context.Context, phone string, limit int) ([]*model.SMSLog, error)
	GetByUserID(ctx context.Context, userID uint, limit int) ([]*model.SMSLog, error)
	GetByTemplateCode(ctx context.Context, templateCode string, limit int) ([]*model.SMSLog, error)
	
	// 统计操作
	GetSMSStats(ctx context.Context, startDate, endDate time.Time) (*SMSStats, error)
	GetFailedSMS(ctx context.Context, limit int) ([]*model.SMSLog, error)
	
	// 清理操作
	CleanOldLogs(ctx context.Context, beforeDate time.Time) (int64, error)
}

// MessageQueueRepository 消息队列仓储接口
type MessageQueueRepository interface {
	// 基础操作
	Create(ctx context.Context, message *model.MessageQueue) error
	GetByID(ctx context.Context, id uint) (*model.MessageQueue, error)
	GetByMessageID(ctx context.Context, messageID string) (*model.MessageQueue, error)
	Update(ctx context.Context, message *model.MessageQueue) error
	Delete(ctx context.Context, id uint) error
	
	// 查询操作
	List(ctx context.Context, condition *MessageQueueQueryCondition) ([]*model.MessageQueue, int64, error)
	GetByTopic(ctx context.Context, topic string, limit int) ([]*model.MessageQueue, error)
	
	// 队列操作
	GetPendingMessages(ctx context.Context, limit int) ([]*model.MessageQueue, error)
	GetScheduledMessages(ctx context.Context, limit int) ([]*model.MessageQueue, error)
	GetRetryMessages(ctx context.Context, limit int) ([]*model.MessageQueue, error)
	
	// 状态操作
	UpdateStatus(ctx context.Context, messageID string, status int) error
	UpdateProcessInfo(ctx context.Context, messageID, consumerID string, status int) error
	
	// 统计操作
	GetMessageStats(ctx context.Context, topic string, startDate, endDate time.Time) (*MessageStats, error)
	
	// 清理操作
	CleanProcessedMessages(ctx context.Context, beforeDate time.Time) (int64, error)
}

// SMSTemplateQueryCondition 短信模板查询条件
type SMSTemplateQueryCondition struct {
	TemplateCode string
	TemplateName string
	Category     string
	Status       int
	Page         int
	PageSize     int
	OrderBy      string
}

// SMSLogQueryCondition 短信日志查询条件
type SMSLogQueryCondition struct {
	TemplateCode string
	Phone        string
	UserID       uint
	UserType     string
	Status       int
	Provider     string
	StartDate    time.Time
	EndDate      time.Time
	Page         int
	PageSize     int
	OrderBy      string
}

// MessageQueueQueryCondition 消息队列查询条件
type MessageQueueQueryCondition struct {
	MessageID  string
	Topic      string
	Tag        string
	Status     int
	Priority   int
	ConsumerID string
	StartDate  time.Time
	EndDate    time.Time
	Page       int
	PageSize   int
	OrderBy    string
}

// SMSStats 短信统计
type SMSStats struct {
	TotalCount   int64   `json:"total_count"`
	SuccessCount int64   `json:"success_count"`
	FailedCount  int64   `json:"failed_count"`
	PendingCount int64   `json:"pending_count"`
	SuccessRate  float64 `json:"success_rate"`
}

// MessageStats 消息统计
type MessageStats struct {
	TotalCount      int64   `json:"total_count"`
	PendingCount    int64   `json:"pending_count"`
	ProcessingCount int64   `json:"processing_count"`
	SuccessCount    int64   `json:"success_count"`
	FailedCount     int64   `json:"failed_count"`
	SuccessRate     float64 `json:"success_rate"`
}

// smsTemplateRepository 短信模板仓储实现
type smsTemplateRepository struct {
	db *gorm.DB
}

// NewSMSTemplateRepository 创建短信模板仓储
func NewSMSTemplateRepository(db *gorm.DB) SMSTemplateRepository {
	return &smsTemplateRepository{db: db}
}

// Create 创建短信模板
func (r *smsTemplateRepository) Create(ctx context.Context, template *model.SMSTemplate) error {
	return r.db.WithContext(ctx).Create(template).Error
}

// GetByID 根据ID获取短信模板
func (r *smsTemplateRepository) GetByID(ctx context.Context, id uint) (*model.SMSTemplate, error) {
	var template model.SMSTemplate
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

// GetByCode 根据模板代码获取短信模板
func (r *smsTemplateRepository) GetByCode(ctx context.Context, templateCode string) (*model.SMSTemplate, error) {
	var template model.SMSTemplate
	err := r.db.WithContext(ctx).Where("template_code = ? AND status = ?", templateCode, 1).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

// Update 更新短信模板
func (r *smsTemplateRepository) Update(ctx context.Context, template *model.SMSTemplate) error {
	return r.db.WithContext(ctx).Save(template).Error
}

// Delete 删除短信模板
func (r *smsTemplateRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.SMSTemplate{}, id).Error
}

// List 分页查询短信模板
func (r *smsTemplateRepository) List(ctx context.Context, condition *SMSTemplateQueryCondition) ([]*model.SMSTemplate, int64, error) {
	var templates []*model.SMSTemplate
	var total int64
	
	query := r.db.WithContext(ctx).Model(&model.SMSTemplate{})
	
	// 构建查询条件
	if condition.TemplateCode != "" {
		query = query.Where("template_code LIKE ?", "%"+condition.TemplateCode+"%")
	}
	if condition.TemplateName != "" {
		query = query.Where("template_name LIKE ?", "%"+condition.TemplateName+"%")
	}
	if condition.Category != "" {
		query = query.Where("category = ?", condition.Category)
	}
	if condition.Status >= 0 {
		query = query.Where("status = ?", condition.Status)
	}
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := (condition.Page - 1) * condition.PageSize
	orderBy := "created_at DESC"
	if condition.OrderBy != "" {
		orderBy = condition.OrderBy
	}
	
	err := query.Order(orderBy).Offset(offset).Limit(condition.PageSize).Find(&templates).Error
	return templates, total, err
}

// GetByCategory 根据分类获取短信模板
func (r *smsTemplateRepository) GetByCategory(ctx context.Context, category string) ([]*model.SMSTemplate, error) {
	var templates []*model.SMSTemplate
	err := r.db.WithContext(ctx).Where("category = ? AND status = ?", category, 1).
		Order("sort_order ASC, created_at DESC").Find(&templates).Error
	return templates, err
}

// GetActiveTemplates 获取启用的短信模板
func (r *smsTemplateRepository) GetActiveTemplates(ctx context.Context) ([]*model.SMSTemplate, error) {
	var templates []*model.SMSTemplate
	err := r.db.WithContext(ctx).Where("status = ?", 1).
		Order("category ASC, sort_order ASC").Find(&templates).Error
	return templates, err
}

// UpdateStatus 更新短信模板状态
func (r *smsTemplateRepository) UpdateStatus(ctx context.Context, id uint, status int) error {
	return r.db.WithContext(ctx).Model(&model.SMSTemplate{}).
		Where("id = ?", id).Update("status", status).Error
}

// CheckCodeExists 检查模板代码是否存在
func (r *smsTemplateRepository) CheckCodeExists(ctx context.Context, templateCode string, excludeID uint) bool {
	var count int64
	query := r.db.WithContext(ctx).Model(&model.SMSTemplate{}).Where("template_code = ?", templateCode)
	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}
	query.Count(&count)
	return count > 0
}

// smsLogRepository 短信日志仓储实现
type smsLogRepository struct {
	db *gorm.DB
}

// NewSMSLogRepository 创建短信日志仓储
func NewSMSLogRepository(db *gorm.DB) SMSLogRepository {
	return &smsLogRepository{db: db}
}

// Create 创建短信日志
func (r *smsLogRepository) Create(ctx context.Context, log *model.SMSLog) error {
	return r.db.WithContext(ctx).Create(log).Error
}

// GetByID 根据ID获取短信日志
func (r *smsLogRepository) GetByID(ctx context.Context, id uint) (*model.SMSLog, error) {
	var log model.SMSLog
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&log).Error
	if err != nil {
		return nil, err
	}
	return &log, nil
}

// Update 更新短信日志
func (r *smsLogRepository) Update(ctx context.Context, log *model.SMSLog) error {
	return r.db.WithContext(ctx).Save(log).Error
}

// List 分页查询短信日志
func (r *smsLogRepository) List(ctx context.Context, condition *SMSLogQueryCondition) ([]*model.SMSLog, int64, error) {
	var logs []*model.SMSLog
	var total int64
	
	query := r.db.WithContext(ctx).Model(&model.SMSLog{})
	
	// 构建查询条件
	if condition.TemplateCode != "" {
		query = query.Where("template_code = ?", condition.TemplateCode)
	}
	if condition.Phone != "" {
		query = query.Where("phone LIKE ?", "%"+condition.Phone+"%")
	}
	if condition.UserID > 0 {
		query = query.Where("user_id = ?", condition.UserID)
	}
	if condition.UserType != "" {
		query = query.Where("user_type = ?", condition.UserType)
	}
	if condition.Status >= 0 {
		query = query.Where("status = ?", condition.Status)
	}
	if condition.Provider != "" {
		query = query.Where("provider = ?", condition.Provider)
	}
	if !condition.StartDate.IsZero() {
		query = query.Where("created_at >= ?", condition.StartDate)
	}
	if !condition.EndDate.IsZero() {
		query = query.Where("created_at <= ?", condition.EndDate)
	}
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := (condition.Page - 1) * condition.PageSize
	orderBy := "created_at DESC"
	if condition.OrderBy != "" {
		orderBy = condition.OrderBy
	}
	
	err := query.Order(orderBy).Offset(offset).Limit(condition.PageSize).Find(&logs).Error
	return logs, total, err
}

// Delete 删除短信日志
func (r *smsLogRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.SMSLog{}, id).Error
}

// GetByPhone 根据手机号获取短信日志
func (r *smsLogRepository) GetByPhone(ctx context.Context, phone string, limit int) ([]*model.SMSLog, error) {
	var logs []*model.SMSLog
	err := r.db.WithContext(ctx).Where("phone = ?", phone).
		Order("created_at DESC").Limit(limit).Find(&logs).Error
	return logs, err
}

// GetByUserID 根据用户ID获取短信日志
func (r *smsLogRepository) GetByUserID(ctx context.Context, userID uint, limit int) ([]*model.SMSLog, error) {
	var logs []*model.SMSLog
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).
		Order("created_at DESC").Limit(limit).Find(&logs).Error
	return logs, err
}

// GetByTemplateCode 根据模板代码获取短信日志
func (r *smsLogRepository) GetByTemplateCode(ctx context.Context, templateCode string, limit int) ([]*model.SMSLog, error) {
	var logs []*model.SMSLog
	err := r.db.WithContext(ctx).Where("template_code = ?", templateCode).
		Order("created_at DESC").Limit(limit).Find(&logs).Error
	return logs, err
}

// GetSMSStats 获取短信统计
func (r *smsLogRepository) GetSMSStats(ctx context.Context, startDate, endDate time.Time) (*SMSStats, error) {
	var stats SMSStats

	query := r.db.WithContext(ctx).Model(&model.SMSLog{})

	if !startDate.IsZero() {
		query = query.Where("created_at >= ?", startDate)
	}
	if !endDate.IsZero() {
		query = query.Where("created_at <= ?", endDate)
	}

	// 获取总数
	if err := query.Count(&stats.TotalCount).Error; err != nil {
		return nil, err
	}

	// 获取成功数
	if err := query.Where("status = ?", model.EmailStatusSuccess).Count(&stats.SuccessCount).Error; err != nil {
		return nil, err
	}

	// 获取失败数
	if err := query.Where("status = ?", model.EmailStatusFailed).Count(&stats.FailedCount).Error; err != nil {
		return nil, err
	}

	// 获取待发送数
	if err := query.Where("status = ?", model.EmailStatusPending).Count(&stats.PendingCount).Error; err != nil {
		return nil, err
	}

	// 计算成功率
	if stats.TotalCount > 0 {
		stats.SuccessRate = float64(stats.SuccessCount) / float64(stats.TotalCount) * 100
	}

	return &stats, nil
}

// GetFailedSMS 获取失败的短信
func (r *smsLogRepository) GetFailedSMS(ctx context.Context, limit int) ([]*model.SMSLog, error) {
	var logs []*model.SMSLog
	err := r.db.WithContext(ctx).Where("status = ?", model.EmailStatusFailed).
		Order("created_at DESC").Limit(limit).Find(&logs).Error
	return logs, err
}

// CleanOldLogs 清理旧短信日志
func (r *smsLogRepository) CleanOldLogs(ctx context.Context, beforeDate time.Time) (int64, error) {
	result := r.db.WithContext(ctx).Where("created_at < ?", beforeDate).Delete(&model.SMSLog{})
	return result.RowsAffected, result.Error
}

// messageQueueRepository 消息队列仓储实现
type messageQueueRepository struct {
	db *gorm.DB
}

// NewMessageQueueRepository 创建消息队列仓储
func NewMessageQueueRepository(db *gorm.DB) MessageQueueRepository {
	return &messageQueueRepository{db: db}
}

// Create 创建消息队列
func (r *messageQueueRepository) Create(ctx context.Context, message *model.MessageQueue) error {
	return r.db.WithContext(ctx).Create(message).Error
}

// GetByID 根据ID获取消息队列
func (r *messageQueueRepository) GetByID(ctx context.Context, id uint) (*model.MessageQueue, error) {
	var message model.MessageQueue
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&message).Error
	if err != nil {
		return nil, err
	}
	return &message, nil
}

// GetByMessageID 根据消息ID获取消息队列
func (r *messageQueueRepository) GetByMessageID(ctx context.Context, messageID string) (*model.MessageQueue, error) {
	var message model.MessageQueue
	err := r.db.WithContext(ctx).Where("message_id = ?", messageID).First(&message).Error
	if err != nil {
		return nil, err
	}
	return &message, nil
}

// Update 更新消息队列
func (r *messageQueueRepository) Update(ctx context.Context, message *model.MessageQueue) error {
	return r.db.WithContext(ctx).Save(message).Error
}

// Delete 删除消息队列
func (r *messageQueueRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.MessageQueue{}, id).Error
}

// List 分页查询消息队列
func (r *messageQueueRepository) List(ctx context.Context, condition *MessageQueueQueryCondition) ([]*model.MessageQueue, int64, error) {
	var messages []*model.MessageQueue
	var total int64

	query := r.db.WithContext(ctx).Model(&model.MessageQueue{})

	// 构建查询条件
	if condition.MessageID != "" {
		query = query.Where("message_id = ?", condition.MessageID)
	}
	if condition.Topic != "" {
		query = query.Where("topic = ?", condition.Topic)
	}
	if condition.Tag != "" {
		query = query.Where("tag = ?", condition.Tag)
	}
	if condition.Status >= 0 {
		query = query.Where("status = ?", condition.Status)
	}
	if condition.Priority >= 0 {
		query = query.Where("priority = ?", condition.Priority)
	}
	if condition.ConsumerID != "" {
		query = query.Where("consumer_id = ?", condition.ConsumerID)
	}
	if !condition.StartDate.IsZero() {
		query = query.Where("created_at >= ?", condition.StartDate)
	}
	if !condition.EndDate.IsZero() {
		query = query.Where("created_at <= ?", condition.EndDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (condition.Page - 1) * condition.PageSize
	orderBy := "priority DESC, created_at ASC"
	if condition.OrderBy != "" {
		orderBy = condition.OrderBy
	}

	err := query.Order(orderBy).Offset(offset).Limit(condition.PageSize).Find(&messages).Error
	return messages, total, err
}

// GetByTopic 根据主题获取消息队列
func (r *messageQueueRepository) GetByTopic(ctx context.Context, topic string, limit int) ([]*model.MessageQueue, error) {
	var messages []*model.MessageQueue
	err := r.db.WithContext(ctx).Where("topic = ?", topic).
		Order("priority DESC, created_at ASC").Limit(limit).Find(&messages).Error
	return messages, err
}

// GetPendingMessages 获取待处理消息
func (r *messageQueueRepository) GetPendingMessages(ctx context.Context, limit int) ([]*model.MessageQueue, error) {
	var messages []*model.MessageQueue
	err := r.db.WithContext(ctx).
		Where("status = ? AND schedule_at <= ?", model.MessageStatusPending, time.Now()).
		Order("priority DESC, created_at ASC").
		Limit(limit).
		Find(&messages).Error
	return messages, err
}

// GetScheduledMessages 获取定时消息
func (r *messageQueueRepository) GetScheduledMessages(ctx context.Context, limit int) ([]*model.MessageQueue, error) {
	var messages []*model.MessageQueue
	err := r.db.WithContext(ctx).
		Where("status = ? AND schedule_at > ?", model.MessageStatusPending, time.Now()).
		Order("schedule_at ASC").
		Limit(limit).
		Find(&messages).Error
	return messages, err
}

// GetRetryMessages 获取需要重试的消息
func (r *messageQueueRepository) GetRetryMessages(ctx context.Context, limit int) ([]*model.MessageQueue, error) {
	var messages []*model.MessageQueue
	err := r.db.WithContext(ctx).
		Where("status = ? AND retry_count < max_retry", model.MessageStatusFailed).
		Order("priority DESC, created_at ASC").
		Limit(limit).
		Find(&messages).Error
	return messages, err
}

// UpdateStatus 更新消息状态
func (r *messageQueueRepository) UpdateStatus(ctx context.Context, messageID string, status int) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	if status == model.MessageStatusProcessing {
		updates["process_at"] = time.Now()
	} else if status == model.MessageStatusSuccess || status == model.MessageStatusFailed {
		updates["finish_at"] = time.Now()
	}

	return r.db.WithContext(ctx).Model(&model.MessageQueue{}).
		Where("message_id = ?", messageID).Updates(updates).Error
}

// UpdateProcessInfo 更新处理信息
func (r *messageQueueRepository) UpdateProcessInfo(ctx context.Context, messageID, consumerID string, status int) error {
	updates := map[string]interface{}{
		"consumer_id": consumerID,
		"status":      status,
		"process_at":  time.Now(),
		"updated_at":  time.Now(),
	}

	if status == model.MessageStatusSuccess || status == model.MessageStatusFailed {
		updates["finish_at"] = time.Now()
	}

	return r.db.WithContext(ctx).Model(&model.MessageQueue{}).
		Where("message_id = ?", messageID).Updates(updates).Error
}

// GetMessageStats 获取消息统计
func (r *messageQueueRepository) GetMessageStats(ctx context.Context, topic string, startDate, endDate time.Time) (*MessageStats, error) {
	var stats MessageStats

	query := r.db.WithContext(ctx).Model(&model.MessageQueue{})

	if topic != "" {
		query = query.Where("topic = ?", topic)
	}
	if !startDate.IsZero() {
		query = query.Where("created_at >= ?", startDate)
	}
	if !endDate.IsZero() {
		query = query.Where("created_at <= ?", endDate)
	}

	// 获取总数
	if err := query.Count(&stats.TotalCount).Error; err != nil {
		return nil, err
	}

	// 获取待处理数
	if err := query.Where("status = ?", model.MessageStatusPending).Count(&stats.PendingCount).Error; err != nil {
		return nil, err
	}

	// 获取处理中数
	if err := query.Where("status = ?", model.MessageStatusProcessing).Count(&stats.ProcessingCount).Error; err != nil {
		return nil, err
	}

	// 获取成功数
	if err := query.Where("status = ?", model.MessageStatusSuccess).Count(&stats.SuccessCount).Error; err != nil {
		return nil, err
	}

	// 获取失败数
	if err := query.Where("status = ?", model.MessageStatusFailed).Count(&stats.FailedCount).Error; err != nil {
		return nil, err
	}

	// 计算成功率
	processedCount := stats.SuccessCount + stats.FailedCount
	if processedCount > 0 {
		stats.SuccessRate = float64(stats.SuccessCount) / float64(processedCount) * 100
	}

	return &stats, nil
}

// CleanProcessedMessages 清理已处理的消息
func (r *messageQueueRepository) CleanProcessedMessages(ctx context.Context, beforeDate time.Time) (int64, error) {
	result := r.db.WithContext(ctx).
		Where("status IN (?, ?) AND finish_at < ?",
			model.MessageStatusSuccess, model.MessageStatusFailed, beforeDate).
		Delete(&model.MessageQueue{})
	return result.RowsAffected, result.Error
}
