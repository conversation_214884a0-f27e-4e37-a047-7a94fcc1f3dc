package main

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

func main() {
	// 创建Gin引擎
	r := gin.Default()

	// 添加基本路由
	r.GET("/", func(c *gin.Context) {
		c.<PERSON>SON(http.StatusOK, gin.H{
			"message": "Payment Gateway API is running",
			"version": "1.0.0",
		})
	})

	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "healthy",
		})
	})

	// 启动服务器
	fmt.Println("Starting Payment Gateway on port 8080...")
	if err := r.Run(":8080"); err != nil {
		fmt.Printf("Failed to start server: %v\n", err)
	}
}
