import type { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _Textarea from './textarea';
declare const Textarea: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
        modelValue: StringConstructor;
        defaultValue: {
            type: StringConstructor;
            default: string;
        };
        placeholder: StringConstructor;
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
        error: {
            type: BooleanConstructor;
            default: boolean;
        };
        maxLength: {
            type: import("vue").PropType<number | {
                length: number;
                errorOnly?: boolean | undefined;
            }>;
            default: number;
        };
        showWordLimit: {
            type: BooleanConstructor;
            default: boolean;
        };
        allowClear: {
            type: BooleanConstructor;
            default: boolean;
        };
        autoSize: {
            type: import("vue").PropType<boolean | {
                minRows?: number | undefined;
                maxRows?: number | undefined;
            }>;
            default: boolean;
        };
        wordLength: {
            type: import("vue").PropType<(value: string) => number>;
        };
        wordSlice: {
            type: import("vue").PropType<(value: string, maxLength: number) => string>;
        };
        textareaAttrs: {
            type: import("vue").PropType<Record<string, any>>;
        };
    }>> & Readonly<{
        onFocus?: ((ev: FocusEvent) => any) | undefined;
        onClear?: ((ev: MouseEvent) => any) | undefined;
        onChange?: ((value: string, ev: Event) => any) | undefined;
        onBlur?: ((ev: FocusEvent) => any) | undefined;
        onInput?: ((value: string, ev: Event) => any) | undefined;
        "onUpdate:modelValue"?: ((value: string) => any) | undefined;
    }>, {
        prefixCls: string;
        wrapperCls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        textareaRef: import("vue").Ref<HTMLInputElement | undefined, HTMLInputElement | undefined>;
        textareaStyle: import("vue").Ref<import("vue").CSSProperties | undefined, import("vue").CSSProperties | undefined>;
        mirrorRef: import("vue").Ref<HTMLInputElement | undefined, HTMLInputElement | undefined>;
        mirrorStyle: import("vue").Ref<import("vue").CSSProperties | undefined, import("vue").CSSProperties | undefined>;
        computedValue: import("vue").ComputedRef<string>;
        showClearBtn: import("vue").ComputedRef<string | false>;
        valueLength: import("vue").ComputedRef<number>;
        computedMaxLength: import("vue").ComputedRef<number>;
        mergedDisabled: import("vue").ComputedRef<boolean>;
        mergeTextareaAttrs: import("vue").ComputedRef<{
            [x: string]: any;
        }>;
        getWrapperAttrs: (attr: Record<string, any>) => Omit<{
            [x: string]: unknown;
        }, string>;
        getTextareaAttrs: (attr: Record<string, any>) => Pick<{
            [x: string]: unknown;
        }, string>;
        handleInput: (e: InputEvent) => void;
        handleFocus: (ev: FocusEvent) => void;
        handleBlur: (ev: FocusEvent) => void;
        handleComposition: (e: CompositionEvent) => void;
        handleClear: (ev: MouseEvent) => void;
        handleResize: () => void;
        handleMousedown: (e: MouseEvent) => void;
    }, {}, {}, {
        focus(): void;
        blur(): void;
    }, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        'update:modelValue': (value: string) => true;
        input: (value: string, ev: Event) => true;
        change: (value: string, ev: Event) => true;
        clear: (ev: MouseEvent) => true;
        focus: (ev: FocusEvent) => true;
        blur: (ev: FocusEvent) => true;
    }, import("vue").PublicProps, {
        disabled: boolean;
        error: boolean;
        allowClear: boolean;
        defaultValue: string;
        maxLength: number | {
            length: number;
            errorOnly?: boolean | undefined;
        };
        showWordLimit: boolean;
        autoSize: boolean | {
            minRows?: number | undefined;
            maxRows?: number | undefined;
        };
    }, true, {}, {}, {
        ResizeObserver: import("vue").DefineComponent<{}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
            [key: string]: any;
        }> | null, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "resize"[], "resize", import("vue").PublicProps, Readonly<{}> & Readonly<{
            onResize?: ((...args: any[]) => any) | undefined;
        }>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        IconHover: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            prefix: {
                type: StringConstructor;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
                default: string;
            };
            disabled: {
                type: BooleanConstructor;
                default: boolean;
            };
        }>, {
            prefixCls: string;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            prefix: {
                type: StringConstructor;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
                default: string;
            };
            disabled: {
                type: BooleanConstructor;
                default: boolean;
            };
        }>> & Readonly<{}>, {
            disabled: boolean;
            size: "mini" | "medium" | "large" | "small";
        }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        IconClose: any;
    } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        modelValue: StringConstructor;
        defaultValue: {
            type: StringConstructor;
            default: string;
        };
        placeholder: StringConstructor;
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
        error: {
            type: BooleanConstructor;
            default: boolean;
        };
        maxLength: {
            type: import("vue").PropType<number | {
                length: number;
                errorOnly?: boolean | undefined;
            }>;
            default: number;
        };
        showWordLimit: {
            type: BooleanConstructor;
            default: boolean;
        };
        allowClear: {
            type: BooleanConstructor;
            default: boolean;
        };
        autoSize: {
            type: import("vue").PropType<boolean | {
                minRows?: number | undefined;
                maxRows?: number | undefined;
            }>;
            default: boolean;
        };
        wordLength: {
            type: import("vue").PropType<(value: string) => number>;
        };
        wordSlice: {
            type: import("vue").PropType<(value: string, maxLength: number) => string>;
        };
        textareaAttrs: {
            type: import("vue").PropType<Record<string, any>>;
        };
    }>> & Readonly<{
        onFocus?: ((ev: FocusEvent) => any) | undefined;
        onClear?: ((ev: MouseEvent) => any) | undefined;
        onChange?: ((value: string, ev: Event) => any) | undefined;
        onBlur?: ((ev: FocusEvent) => any) | undefined;
        onInput?: ((value: string, ev: Event) => any) | undefined;
        "onUpdate:modelValue"?: ((value: string) => any) | undefined;
    }>, {
        prefixCls: string;
        wrapperCls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        textareaRef: import("vue").Ref<HTMLInputElement | undefined, HTMLInputElement | undefined>;
        textareaStyle: import("vue").Ref<import("vue").CSSProperties | undefined, import("vue").CSSProperties | undefined>;
        mirrorRef: import("vue").Ref<HTMLInputElement | undefined, HTMLInputElement | undefined>;
        mirrorStyle: import("vue").Ref<import("vue").CSSProperties | undefined, import("vue").CSSProperties | undefined>;
        computedValue: import("vue").ComputedRef<string>;
        showClearBtn: import("vue").ComputedRef<string | false>;
        valueLength: import("vue").ComputedRef<number>;
        computedMaxLength: import("vue").ComputedRef<number>;
        mergedDisabled: import("vue").ComputedRef<boolean>;
        mergeTextareaAttrs: import("vue").ComputedRef<{
            [x: string]: any;
        }>;
        getWrapperAttrs: (attr: Record<string, any>) => Omit<{
            [x: string]: unknown;
        }, string>;
        getTextareaAttrs: (attr: Record<string, any>) => Pick<{
            [x: string]: unknown;
        }, string>;
        handleInput: (e: InputEvent) => void;
        handleFocus: (ev: FocusEvent) => void;
        handleBlur: (ev: FocusEvent) => void;
        handleComposition: (e: CompositionEvent) => void;
        handleClear: (ev: MouseEvent) => void;
        handleResize: () => void;
        handleMousedown: (e: MouseEvent) => void;
    }, {}, {}, {
        focus(): void;
        blur(): void;
    }, {
        disabled: boolean;
        error: boolean;
        allowClear: boolean;
        defaultValue: string;
        maxLength: number | {
            length: number;
            errorOnly?: boolean | undefined;
        };
        showWordLimit: boolean;
        autoSize: boolean | {
            minRows?: number | undefined;
            maxRows?: number | undefined;
        };
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    modelValue: StringConstructor;
    defaultValue: {
        type: StringConstructor;
        default: string;
    };
    placeholder: StringConstructor;
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    error: {
        type: BooleanConstructor;
        default: boolean;
    };
    maxLength: {
        type: import("vue").PropType<number | {
            length: number;
            errorOnly?: boolean | undefined;
        }>;
        default: number;
    };
    showWordLimit: {
        type: BooleanConstructor;
        default: boolean;
    };
    allowClear: {
        type: BooleanConstructor;
        default: boolean;
    };
    autoSize: {
        type: import("vue").PropType<boolean | {
            minRows?: number | undefined;
            maxRows?: number | undefined;
        }>;
        default: boolean;
    };
    wordLength: {
        type: import("vue").PropType<(value: string) => number>;
    };
    wordSlice: {
        type: import("vue").PropType<(value: string, maxLength: number) => string>;
    };
    textareaAttrs: {
        type: import("vue").PropType<Record<string, any>>;
    };
}>> & Readonly<{
    onFocus?: ((ev: FocusEvent) => any) | undefined;
    onClear?: ((ev: MouseEvent) => any) | undefined;
    onChange?: ((value: string, ev: Event) => any) | undefined;
    onBlur?: ((ev: FocusEvent) => any) | undefined;
    onInput?: ((value: string, ev: Event) => any) | undefined;
    "onUpdate:modelValue"?: ((value: string) => any) | undefined;
}>, {
    prefixCls: string;
    wrapperCls: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    textareaRef: import("vue").Ref<HTMLInputElement | undefined, HTMLInputElement | undefined>;
    textareaStyle: import("vue").Ref<import("vue").CSSProperties | undefined, import("vue").CSSProperties | undefined>;
    mirrorRef: import("vue").Ref<HTMLInputElement | undefined, HTMLInputElement | undefined>;
    mirrorStyle: import("vue").Ref<import("vue").CSSProperties | undefined, import("vue").CSSProperties | undefined>;
    computedValue: import("vue").ComputedRef<string>;
    showClearBtn: import("vue").ComputedRef<string | false>;
    valueLength: import("vue").ComputedRef<number>;
    computedMaxLength: import("vue").ComputedRef<number>;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    mergeTextareaAttrs: import("vue").ComputedRef<{
        [x: string]: any;
    }>;
    getWrapperAttrs: (attr: Record<string, any>) => Omit<{
        [x: string]: unknown;
    }, string>;
    getTextareaAttrs: (attr: Record<string, any>) => Pick<{
        [x: string]: unknown;
    }, string>;
    handleInput: (e: InputEvent) => void;
    handleFocus: (ev: FocusEvent) => void;
    handleBlur: (ev: FocusEvent) => void;
    handleComposition: (e: CompositionEvent) => void;
    handleClear: (ev: MouseEvent) => void;
    handleResize: () => void;
    handleMousedown: (e: MouseEvent) => void;
}, {}, {}, {
    focus(): void;
    blur(): void;
}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    'update:modelValue': (value: string) => true;
    input: (value: string, ev: Event) => true;
    change: (value: string, ev: Event) => true;
    clear: (ev: MouseEvent) => true;
    focus: (ev: FocusEvent) => true;
    blur: (ev: FocusEvent) => true;
}, string, {
    disabled: boolean;
    error: boolean;
    allowClear: boolean;
    defaultValue: string;
    maxLength: number | {
        length: number;
        errorOnly?: boolean | undefined;
    };
    showWordLimit: boolean;
    autoSize: boolean | {
        minRows?: number | undefined;
        maxRows?: number | undefined;
    };
}, {}, string, {}, {
    ResizeObserver: import("vue").DefineComponent<{}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }> | null, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "resize"[], "resize", import("vue").PublicProps, Readonly<{}> & Readonly<{
        onResize?: ((...args: any[]) => any) | undefined;
    }>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    IconHover: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>, {
        prefixCls: string;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{}>, {
        disabled: boolean;
        size: "mini" | "medium" | "large" | "small";
    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    IconClose: any;
} & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type TextareaInstance = InstanceType<typeof _Textarea>;
export default Textarea;
