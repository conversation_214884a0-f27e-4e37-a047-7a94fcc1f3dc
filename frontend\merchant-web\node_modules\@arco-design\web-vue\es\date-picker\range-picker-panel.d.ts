import { PropType } from 'vue';
import { Dayjs } from 'dayjs';
import { isFunction } from '../_utils/is';
import pick from '../_utils/pick';
import type { ShortcutType, Mode, RangeDisabledTime, RangeDisabledDate, WeekStart, StartHeaderProps } from './interface';
import { RenderFunc } from '../_components/render-function';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    mode: {
        type: PropType<Mode>;
        default: string;
    };
    value: {
        type: PropType<(Dayjs | undefined)[]>;
        default: () => never[];
    };
    footerValue: {
        type: PropType<(Dayjs | undefined)[]>;
    };
    timePickerValue: {
        type: PropType<Dayjs[]>;
    };
    showTime: {
        type: BooleanConstructor;
    };
    showConfirmBtn: {
        type: BooleanConstructor;
    };
    prefixCls: {
        type: StringConstructor;
        required: true;
    };
    shortcuts: {
        type: PropType<ShortcutType[]>;
        default: () => never[];
    };
    shortcutsPosition: {
        type: PropType<"bottom" | "left" | "right">;
        default: string;
    };
    format: {
        type: StringConstructor;
        required: true;
    };
    dayStartOfWeek: {
        type: PropType<WeekStart>;
        default: number;
    };
    disabledDate: {
        type: PropType<RangeDisabledDate>;
    };
    disabledTime: {
        type: PropType<RangeDisabledTime>;
    };
    timePickerProps: {
        type: PropType<Partial<import("../time-picker/interface").TimePickerProps> | undefined>;
    };
    extra: {
        type: PropType<RenderFunc>;
    };
    dateRender: {
        type: PropType<RenderFunc>;
    };
    hideTrigger: {
        type: BooleanConstructor;
    };
    startHeaderProps: {
        type: PropType<StartHeaderProps>;
        default: () => {};
    };
    endHeaderProps: {
        type: PropType<Record<string, any>>;
        default: () => {};
    };
    confirmBtnDisabled: {
        type: BooleanConstructor;
    };
    disabled: {
        type: PropType<boolean[]>;
        default: () => boolean[];
    };
    visible: {
        type: BooleanConstructor;
    };
    startHeaderMode: {
        type: PropType<"month" | "year">;
    };
    endHeaderMode: {
        type: PropType<"month" | "year">;
    };
    abbreviation: {
        type: BooleanConstructor;
    };
}>, {
    pick: typeof pick;
    classNames: import("vue").ComputedRef<(string | {
        [x: string]: boolean | 0;
    })[]>;
    showShortcuts: import("vue").ComputedRef<number | false>;
    shortcutsProps: {
        prefixCls: string;
        shortcuts: ShortcutType[];
        onItemClick: (shortcut: ShortcutType) => void;
        onItemMouseEnter: (shortcut: ShortcutType) => void;
        onItemMouseLeave: (shortcut: ShortcutType) => void;
    };
    startPanelProps: import("vue").ComputedRef<{
        rangeValues: (Dayjs | undefined)[];
        disabledDate: ((current: Date) => boolean) | undefined;
        dateRender: ((props: any) => import("vue").VNodeTypes | undefined) | undefined;
        onSelect: (date: Dayjs) => void;
        onCellMouseEnter: (date: Dayjs) => void;
        onHeaderLabelClick: (type: 'year' | 'month') => void;
        headerValue: Dayjs;
        headerOperations: Pick<any, string>;
        headerIcons: {
            prev: import("vue").Slot<any> | undefined;
            prevDouble: import("vue").Slot<any> | undefined;
            next: import("vue").Slot<any> | undefined;
            nextDouble: import("vue").Slot<any> | undefined;
        };
    }>;
    endPanelProps: import("vue").ComputedRef<{
        rangeValues: (Dayjs | undefined)[];
        disabledDate: ((current: Date) => boolean) | undefined;
        dateRender: ((props: any) => import("vue").VNodeTypes | undefined) | undefined;
        onSelect: (date: Dayjs) => void;
        onCellMouseEnter: (date: Dayjs) => void;
        onHeaderLabelClick: (type: 'year' | 'month') => void;
    }>;
    getDisabledTimeFunc: (index: 0 | 1) => ((current: Date) => false | import("./interface").DisabledTimeProps) | undefined;
    onConfirmBtnClick: () => void;
    currentDateView: import("vue").Ref<string, string>;
    onStartTimePickerSelect: (time: Dayjs) => void;
    onEndTimePickerSelect: (time: Dayjs) => void;
    onStartHeaderPanelSelect: (date: Dayjs) => void;
    onEndHeaderPanelSelect: (date: Dayjs) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("cell-click" | "cell-mouse-enter" | "time-picker-select" | "shortcut-click" | "shortcut-mouse-enter" | "shortcut-mouse-leave" | "confirm" | "start-header-label-click" | "end-header-label-click" | "start-header-select" | "end-header-select")[], "cell-click" | "cell-mouse-enter" | "confirm" | "time-picker-select" | "shortcut-click" | "shortcut-mouse-enter" | "shortcut-mouse-leave" | "start-header-label-click" | "end-header-label-click" | "start-header-select" | "end-header-select", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    mode: {
        type: PropType<Mode>;
        default: string;
    };
    value: {
        type: PropType<(Dayjs | undefined)[]>;
        default: () => never[];
    };
    footerValue: {
        type: PropType<(Dayjs | undefined)[]>;
    };
    timePickerValue: {
        type: PropType<Dayjs[]>;
    };
    showTime: {
        type: BooleanConstructor;
    };
    showConfirmBtn: {
        type: BooleanConstructor;
    };
    prefixCls: {
        type: StringConstructor;
        required: true;
    };
    shortcuts: {
        type: PropType<ShortcutType[]>;
        default: () => never[];
    };
    shortcutsPosition: {
        type: PropType<"bottom" | "left" | "right">;
        default: string;
    };
    format: {
        type: StringConstructor;
        required: true;
    };
    dayStartOfWeek: {
        type: PropType<WeekStart>;
        default: number;
    };
    disabledDate: {
        type: PropType<RangeDisabledDate>;
    };
    disabledTime: {
        type: PropType<RangeDisabledTime>;
    };
    timePickerProps: {
        type: PropType<Partial<import("../time-picker/interface").TimePickerProps> | undefined>;
    };
    extra: {
        type: PropType<RenderFunc>;
    };
    dateRender: {
        type: PropType<RenderFunc>;
    };
    hideTrigger: {
        type: BooleanConstructor;
    };
    startHeaderProps: {
        type: PropType<StartHeaderProps>;
        default: () => {};
    };
    endHeaderProps: {
        type: PropType<Record<string, any>>;
        default: () => {};
    };
    confirmBtnDisabled: {
        type: BooleanConstructor;
    };
    disabled: {
        type: PropType<boolean[]>;
        default: () => boolean[];
    };
    visible: {
        type: BooleanConstructor;
    };
    startHeaderMode: {
        type: PropType<"month" | "year">;
    };
    endHeaderMode: {
        type: PropType<"month" | "year">;
    };
    abbreviation: {
        type: BooleanConstructor;
    };
}>> & Readonly<{
    "onCell-click"?: ((...args: any[]) => any) | undefined;
    "onCell-mouse-enter"?: ((...args: any[]) => any) | undefined;
    onConfirm?: ((...args: any[]) => any) | undefined;
    "onTime-picker-select"?: ((...args: any[]) => any) | undefined;
    "onShortcut-click"?: ((...args: any[]) => any) | undefined;
    "onShortcut-mouse-enter"?: ((...args: any[]) => any) | undefined;
    "onShortcut-mouse-leave"?: ((...args: any[]) => any) | undefined;
    "onStart-header-label-click"?: ((...args: any[]) => any) | undefined;
    "onEnd-header-label-click"?: ((...args: any[]) => any) | undefined;
    "onStart-header-select"?: ((...args: any[]) => any) | undefined;
    "onEnd-header-select"?: ((...args: any[]) => any) | undefined;
}>, {
    disabled: boolean[];
    mode: Mode;
    visible: boolean;
    value: (Dayjs | undefined)[];
    hideTrigger: boolean;
    dayStartOfWeek: WeekStart;
    showTime: boolean;
    shortcuts: ShortcutType[];
    showConfirmBtn: boolean;
    confirmBtnDisabled: boolean;
    abbreviation: boolean;
    shortcutsPosition: "bottom" | "left" | "right";
    startHeaderProps: StartHeaderProps;
    endHeaderProps: Record<string, any>;
}, {}, {
    PanelShortcuts: import("vue").DefineComponent<{
        prefixCls: string;
        shortcuts: ShortcutType[];
    }, {
        datePickerT: (key: string, ...args: any[]) => any;
        onItemClick: (item: ShortcutType) => void;
        onItemMouseEnter: (item: ShortcutType) => void;
        onItemMouseLeave: (item: ShortcutType) => void;
        onNowClick: () => void;
        isFunction: typeof isFunction;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("item-click" | "item-mouse-enter" | "item-mouse-leave" | "now-click")[], "item-click" | "item-mouse-enter" | "item-mouse-leave" | "now-click", import("vue").PublicProps, Readonly<{
        prefixCls: string;
        shortcuts: ShortcutType[];
    }> & Readonly<{
        "onItem-click"?: ((...args: any[]) => any) | undefined;
        "onItem-mouse-enter"?: ((...args: any[]) => any) | undefined;
        "onItem-mouse-leave"?: ((...args: any[]) => any) | undefined;
        "onNow-click"?: ((...args: any[]) => any) | undefined;
    }>, {
        showNowBtn: boolean;
        shortcuts: ShortcutType[];
    }, {}, {
        Button: {
            new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
                type: {
                    type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                };
                shape: {
                    type: PropType<"round" | "circle" | "square">;
                };
                status: {
                    type: PropType<"normal" | "success" | "warning" | "danger">;
                };
                size: {
                    type: PropType<"mini" | "medium" | "large" | "small">;
                };
                long: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                loading: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                disabled: {
                    type: BooleanConstructor;
                };
                htmlType: {
                    type: StringConstructor;
                    default: string;
                };
                autofocus: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                href: StringConstructor;
            }>> & Readonly<{
                onClick?: ((ev: MouseEvent) => any) | undefined;
            }>, {
                prefixCls: string;
                cls: import("vue").ComputedRef<(string | {
                    [x: string]: boolean;
                })[]>;
                mergedDisabled: import("vue").ComputedRef<boolean>;
                handleClick: (ev: MouseEvent) => void;
            }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
                click: (ev: MouseEvent) => true;
            }, import("vue").PublicProps, {
                disabled: boolean;
                autofocus: boolean;
                loading: boolean;
                long: boolean;
                htmlType: string;
            }, true, {}, {}, {
                IconLoading: any;
            } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
                P: {};
                B: {};
                D: {};
                C: {};
                M: {};
                Defaults: {};
            }, Readonly<import("vue").ExtractPropTypes<{
                type: {
                    type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                };
                shape: {
                    type: PropType<"round" | "circle" | "square">;
                };
                status: {
                    type: PropType<"normal" | "success" | "warning" | "danger">;
                };
                size: {
                    type: PropType<"mini" | "medium" | "large" | "small">;
                };
                long: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                loading: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                disabled: {
                    type: BooleanConstructor;
                };
                htmlType: {
                    type: StringConstructor;
                    default: string;
                };
                autofocus: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                href: StringConstructor;
            }>> & Readonly<{
                onClick?: ((ev: MouseEvent) => any) | undefined;
            }>, {
                prefixCls: string;
                cls: import("vue").ComputedRef<(string | {
                    [x: string]: boolean;
                })[]>;
                mergedDisabled: import("vue").ComputedRef<boolean>;
                handleClick: (ev: MouseEvent) => void;
            }, {}, {}, {}, {
                disabled: boolean;
                autofocus: boolean;
                loading: boolean;
                long: boolean;
                htmlType: string;
            }>;
            __isFragment?: undefined;
            __isTeleport?: undefined;
            __isSuspense?: undefined;
        } & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            shape: {
                type: PropType<"round" | "circle" | "square">;
            };
            status: {
                type: PropType<"normal" | "success" | "warning" | "danger">;
            };
            size: {
                type: PropType<"mini" | "medium" | "large" | "small">;
            };
            long: {
                type: BooleanConstructor;
                default: boolean;
            };
            loading: {
                type: BooleanConstructor;
                default: boolean;
            };
            disabled: {
                type: BooleanConstructor;
            };
            htmlType: {
                type: StringConstructor;
                default: string;
            };
            autofocus: {
                type: BooleanConstructor;
                default: boolean;
            };
            href: StringConstructor;
        }>> & Readonly<{
            onClick?: ((ev: MouseEvent) => any) | undefined;
        }>, {
            prefixCls: string;
            cls: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            mergedDisabled: import("vue").ComputedRef<boolean>;
            handleClick: (ev: MouseEvent) => void;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            click: (ev: MouseEvent) => true;
        }, string, {
            disabled: boolean;
            autofocus: boolean;
            loading: boolean;
            long: boolean;
            htmlType: string;
        }, {}, string, {}, {
            IconLoading: any;
        } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
            Group: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                type: {
                    type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                };
                status: {
                    type: PropType<"normal" | "success" | "warning" | "danger">;
                };
                shape: {
                    type: PropType<"round" | "circle" | "square">;
                };
                size: {
                    type: PropType<"mini" | "medium" | "large" | "small">;
                };
                disabled: {
                    type: BooleanConstructor;
                };
            }>, {
                prefixCls: string;
            }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                type: {
                    type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                };
                status: {
                    type: PropType<"normal" | "success" | "warning" | "danger">;
                };
                shape: {
                    type: PropType<"round" | "circle" | "square">;
                };
                size: {
                    type: PropType<"mini" | "medium" | "large" | "small">;
                };
                disabled: {
                    type: BooleanConstructor;
                };
            }>> & Readonly<{}>, {
                disabled: boolean;
            }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
            install: (app: import("vue").App<any>, options?: import("../_utils/types").ArcoOptions | undefined) => void;
        };
        RenderFunction: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            renderFunc: {
                type: PropType<RenderFunc>;
                required: true;
            };
        }>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            renderFunc: {
                type: PropType<RenderFunc>;
                required: true;
            };
        }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    PanelFooter: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        prefixCls: {
            type: StringConstructor;
            required: true;
        };
        showTodayBtn: {
            type: BooleanConstructor;
        };
        showConfirmBtn: {
            type: BooleanConstructor;
        };
        confirmBtnDisabled: {
            type: BooleanConstructor;
        };
    }>, {
        datePickerT: (key: string, ...args: any[]) => any;
        onTodayClick: () => void;
        onConfirmBtnClick: () => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("today-btn-click" | "confirm-btn-click")[], "today-btn-click" | "confirm-btn-click", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        prefixCls: {
            type: StringConstructor;
            required: true;
        };
        showTodayBtn: {
            type: BooleanConstructor;
        };
        showConfirmBtn: {
            type: BooleanConstructor;
        };
        confirmBtnDisabled: {
            type: BooleanConstructor;
        };
    }>> & Readonly<{
        "onToday-btn-click"?: ((...args: any[]) => any) | undefined;
        "onConfirm-btn-click"?: ((...args: any[]) => any) | undefined;
    }>, {
        showTodayBtn: boolean;
        showConfirmBtn: boolean;
        confirmBtnDisabled: boolean;
    }, {}, {
        Link: {
            new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
                href: StringConstructor;
                status: {
                    type: PropType<"normal" | "success" | "warning" | "danger">;
                    default: string;
                };
                hoverable: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                icon: BooleanConstructor;
                loading: BooleanConstructor;
                disabled: BooleanConstructor;
            }>> & Readonly<{
                onClick?: ((ev: MouseEvent) => any) | undefined;
            }>, {
                cls: import("vue").ComputedRef<(string | {
                    [x: string]: boolean;
                })[]>;
                prefixCls: string;
                showIcon: import("vue").ComputedRef<boolean>;
                handleClick: (ev: MouseEvent) => void;
            }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
                click: (ev: MouseEvent) => true;
            }, import("vue").PublicProps, {
                disabled: boolean;
                icon: boolean;
                loading: boolean;
                status: "normal" | "success" | "warning" | "danger";
                hoverable: boolean;
            }, true, {}, {}, {
                IconLink: any;
                IconLoading: any;
            } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
                P: {};
                B: {};
                D: {};
                C: {};
                M: {};
                Defaults: {};
            }, Readonly<import("vue").ExtractPropTypes<{
                href: StringConstructor;
                status: {
                    type: PropType<"normal" | "success" | "warning" | "danger">;
                    default: string;
                };
                hoverable: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                icon: BooleanConstructor;
                loading: BooleanConstructor;
                disabled: BooleanConstructor;
            }>> & Readonly<{
                onClick?: ((ev: MouseEvent) => any) | undefined;
            }>, {
                cls: import("vue").ComputedRef<(string | {
                    [x: string]: boolean;
                })[]>;
                prefixCls: string;
                showIcon: import("vue").ComputedRef<boolean>;
                handleClick: (ev: MouseEvent) => void;
            }, {}, {}, {}, {
                disabled: boolean;
                icon: boolean;
                loading: boolean;
                status: "normal" | "success" | "warning" | "danger";
                hoverable: boolean;
            }>;
            __isFragment?: undefined;
            __isTeleport?: undefined;
            __isSuspense?: undefined;
        } & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
            href: StringConstructor;
            status: {
                type: PropType<"normal" | "success" | "warning" | "danger">;
                default: string;
            };
            hoverable: {
                type: BooleanConstructor;
                default: boolean;
            };
            icon: BooleanConstructor;
            loading: BooleanConstructor;
            disabled: BooleanConstructor;
        }>> & Readonly<{
            onClick?: ((ev: MouseEvent) => any) | undefined;
        }>, {
            cls: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            prefixCls: string;
            showIcon: import("vue").ComputedRef<boolean>;
            handleClick: (ev: MouseEvent) => void;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            click: (ev: MouseEvent) => true;
        }, string, {
            disabled: boolean;
            icon: boolean;
            loading: boolean;
            status: "normal" | "success" | "warning" | "danger";
            hoverable: boolean;
        }, {}, string, {}, {
            IconLink: any;
            IconLoading: any;
        } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
            install: (app: import("vue").App<any>, options?: import("../_utils/types").ArcoOptions | undefined) => void;
        };
        Button: {
            new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
                type: {
                    type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                };
                shape: {
                    type: PropType<"round" | "circle" | "square">;
                };
                status: {
                    type: PropType<"normal" | "success" | "warning" | "danger">;
                };
                size: {
                    type: PropType<"mini" | "medium" | "large" | "small">;
                };
                long: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                loading: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                disabled: {
                    type: BooleanConstructor;
                };
                htmlType: {
                    type: StringConstructor;
                    default: string;
                };
                autofocus: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                href: StringConstructor;
            }>> & Readonly<{
                onClick?: ((ev: MouseEvent) => any) | undefined;
            }>, {
                prefixCls: string;
                cls: import("vue").ComputedRef<(string | {
                    [x: string]: boolean;
                })[]>;
                mergedDisabled: import("vue").ComputedRef<boolean>;
                handleClick: (ev: MouseEvent) => void;
            }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
                click: (ev: MouseEvent) => true;
            }, import("vue").PublicProps, {
                disabled: boolean;
                autofocus: boolean;
                loading: boolean;
                long: boolean;
                htmlType: string;
            }, true, {}, {}, {
                IconLoading: any;
            } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
                P: {};
                B: {};
                D: {};
                C: {};
                M: {};
                Defaults: {};
            }, Readonly<import("vue").ExtractPropTypes<{
                type: {
                    type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                };
                shape: {
                    type: PropType<"round" | "circle" | "square">;
                };
                status: {
                    type: PropType<"normal" | "success" | "warning" | "danger">;
                };
                size: {
                    type: PropType<"mini" | "medium" | "large" | "small">;
                };
                long: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                loading: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                disabled: {
                    type: BooleanConstructor;
                };
                htmlType: {
                    type: StringConstructor;
                    default: string;
                };
                autofocus: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                href: StringConstructor;
            }>> & Readonly<{
                onClick?: ((ev: MouseEvent) => any) | undefined;
            }>, {
                prefixCls: string;
                cls: import("vue").ComputedRef<(string | {
                    [x: string]: boolean;
                })[]>;
                mergedDisabled: import("vue").ComputedRef<boolean>;
                handleClick: (ev: MouseEvent) => void;
            }, {}, {}, {}, {
                disabled: boolean;
                autofocus: boolean;
                loading: boolean;
                long: boolean;
                htmlType: string;
            }>;
            __isFragment?: undefined;
            __isTeleport?: undefined;
            __isSuspense?: undefined;
        } & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            shape: {
                type: PropType<"round" | "circle" | "square">;
            };
            status: {
                type: PropType<"normal" | "success" | "warning" | "danger">;
            };
            size: {
                type: PropType<"mini" | "medium" | "large" | "small">;
            };
            long: {
                type: BooleanConstructor;
                default: boolean;
            };
            loading: {
                type: BooleanConstructor;
                default: boolean;
            };
            disabled: {
                type: BooleanConstructor;
            };
            htmlType: {
                type: StringConstructor;
                default: string;
            };
            autofocus: {
                type: BooleanConstructor;
                default: boolean;
            };
            href: StringConstructor;
        }>> & Readonly<{
            onClick?: ((ev: MouseEvent) => any) | undefined;
        }>, {
            prefixCls: string;
            cls: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            mergedDisabled: import("vue").ComputedRef<boolean>;
            handleClick: (ev: MouseEvent) => void;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            click: (ev: MouseEvent) => true;
        }, string, {
            disabled: boolean;
            autofocus: boolean;
            loading: boolean;
            long: boolean;
            htmlType: string;
        }, {}, string, {}, {
            IconLoading: any;
        } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
            Group: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                type: {
                    type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                };
                status: {
                    type: PropType<"normal" | "success" | "warning" | "danger">;
                };
                shape: {
                    type: PropType<"round" | "circle" | "square">;
                };
                size: {
                    type: PropType<"mini" | "medium" | "large" | "small">;
                };
                disabled: {
                    type: BooleanConstructor;
                };
            }>, {
                prefixCls: string;
            }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                type: {
                    type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                };
                status: {
                    type: PropType<"normal" | "success" | "warning" | "danger">;
                };
                shape: {
                    type: PropType<"round" | "circle" | "square">;
                };
                size: {
                    type: PropType<"mini" | "medium" | "large" | "small">;
                };
                disabled: {
                    type: BooleanConstructor;
                };
            }>> & Readonly<{}>, {
                disabled: boolean;
            }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
            install: (app: import("vue").App<any>, options?: import("../_utils/types").ArcoOptions | undefined) => void;
        };
    }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    RenderFunction: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        renderFunc: {
            type: PropType<RenderFunc>;
            required: true;
        };
    }>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        renderFunc: {
            type: PropType<RenderFunc>;
            required: true;
        };
    }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    DatePanel: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        isRange: {
            type: BooleanConstructor;
        };
        value: {
            type: PropType<Dayjs>;
        };
        rangeValues: {
            type: PropType<(Dayjs | undefined)[]>;
        };
        headerValue: {
            type: PropType<Dayjs>;
            required: true;
        };
        footerValue: {
            type: PropType<Dayjs>;
        };
        timePickerValue: {
            type: PropType<Dayjs>;
        };
        headerOperations: {
            type: PropType<import("./interface").HeaderOperations>;
            default: () => {};
        };
        headerIcons: {
            type: PropType<import("./interface").HeaderIcons>;
            default: () => {};
        };
        dayStartOfWeek: {
            type: PropType<WeekStart>;
            default: number;
        };
        disabledDate: {
            type: PropType<import("./interface").DisabledDate>;
        };
        disabledTime: {
            type: PropType<import("./interface").DisabledTime>;
        };
        isSameTime: {
            type: PropType<import("./interface").IsSameTime>;
        };
        mode: {
            type: PropType<Mode>;
            default: string;
        };
        showTime: {
            type: BooleanConstructor;
        };
        timePickerProps: {
            type: PropType<Partial<import("../time-picker/interface").TimePickerProps>>;
        };
        currentView: {
            type: PropType<"time" | "date">;
        };
        dateRender: {
            type: PropType<RenderFunc>;
        };
        disabled: {
            type: BooleanConstructor;
        };
        onHeaderLabelClick: {
            type: PropType<import("./panels/header").HeaderLabelClickFunc>;
        };
    }>, {
        prefixCls: import("vue").ComputedRef<string>;
        classNames: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        pickerPrefixCls: string;
        headerTitle: import("vue").ComputedRef<string>;
        rows: import("vue").ComputedRef<import("./interface").Cell[][]>;
        weekList: import("vue").ComputedRef<number[]>;
        mergedIsSameTime: import("vue").ComputedRef<import("./interface").IsSameTime>;
        disabledTimeProps: import("vue").ComputedRef<import("./interface").DisabledTimeProps>;
        onCellClick: (cellData: import("./interface").Cell) => void;
        onCellMouseEnter: (cellData: import("./interface").Cell) => void;
        onTimePanelSelect: (time: Dayjs) => void;
        showViewTabs: import("vue").ComputedRef<boolean>;
        showDateView: import("vue").ComputedRef<boolean>;
        showTimeView: import("vue").ComputedRef<boolean>;
        changeViewTo: (newView: "time" | "date") => void;
        datePickerT: (key: string, ...args: any[]) => any;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("select" | "cell-mouse-enter" | "time-picker-select" | "current-view-change" | "update:currentView")[], "select" | "cell-mouse-enter" | "time-picker-select" | "current-view-change" | "update:currentView", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        isRange: {
            type: BooleanConstructor;
        };
        value: {
            type: PropType<Dayjs>;
        };
        rangeValues: {
            type: PropType<(Dayjs | undefined)[]>;
        };
        headerValue: {
            type: PropType<Dayjs>;
            required: true;
        };
        footerValue: {
            type: PropType<Dayjs>;
        };
        timePickerValue: {
            type: PropType<Dayjs>;
        };
        headerOperations: {
            type: PropType<import("./interface").HeaderOperations>;
            default: () => {};
        };
        headerIcons: {
            type: PropType<import("./interface").HeaderIcons>;
            default: () => {};
        };
        dayStartOfWeek: {
            type: PropType<WeekStart>;
            default: number;
        };
        disabledDate: {
            type: PropType<import("./interface").DisabledDate>;
        };
        disabledTime: {
            type: PropType<import("./interface").DisabledTime>;
        };
        isSameTime: {
            type: PropType<import("./interface").IsSameTime>;
        };
        mode: {
            type: PropType<Mode>;
            default: string;
        };
        showTime: {
            type: BooleanConstructor;
        };
        timePickerProps: {
            type: PropType<Partial<import("../time-picker/interface").TimePickerProps>>;
        };
        currentView: {
            type: PropType<"time" | "date">;
        };
        dateRender: {
            type: PropType<RenderFunc>;
        };
        disabled: {
            type: BooleanConstructor;
        };
        onHeaderLabelClick: {
            type: PropType<import("./panels/header").HeaderLabelClickFunc>;
        };
    }>> & Readonly<{
        onSelect?: ((...args: any[]) => any) | undefined;
        "onCell-mouse-enter"?: ((...args: any[]) => any) | undefined;
        "onTime-picker-select"?: ((...args: any[]) => any) | undefined;
        "onCurrent-view-change"?: ((...args: any[]) => any) | undefined;
        "onUpdate:currentView"?: ((...args: any[]) => any) | undefined;
    }>, {
        disabled: boolean;
        mode: Mode;
        dayStartOfWeek: WeekStart;
        showTime: boolean;
        isRange: boolean;
        headerOperations: import("./interface").HeaderOperations;
        headerIcons: import("./interface").HeaderIcons;
    }, {}, {
        PanelHeader: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            title: {
                type: StringConstructor;
                required: true;
            };
            mode: {
                type: PropType<Mode>;
                default: string;
            };
            value: {
                type: PropType<Dayjs>;
            };
            icons: {
                type: PropType<import("./interface").HeaderIcons>;
            };
            onPrev: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onSuperPrev: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onNext: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onSuperNext: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onLabelClick: {
                type: PropType<import("./panels/header").HeaderLabelClickFunc>;
            };
        }>, {
            showPrev: import("vue").ComputedRef<boolean>;
            showSuperPrev: import("vue").ComputedRef<boolean>;
            showNext: import("vue").ComputedRef<boolean>;
            showSuperNext: import("vue").ComputedRef<boolean>;
            year: import("vue").ComputedRef<string>;
            month: import("vue").ComputedRef<string>;
            getIconClassName: (show?: boolean | undefined) => (string | {
                [x: string]: boolean;
            })[];
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "label-click"[], "label-click", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            title: {
                type: StringConstructor;
                required: true;
            };
            mode: {
                type: PropType<Mode>;
                default: string;
            };
            value: {
                type: PropType<Dayjs>;
            };
            icons: {
                type: PropType<import("./interface").HeaderIcons>;
            };
            onPrev: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onSuperPrev: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onNext: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onSuperNext: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onLabelClick: {
                type: PropType<import("./panels/header").HeaderLabelClickFunc>;
            };
        }>> & Readonly<{
            "onLabel-click"?: ((...args: any[]) => any) | undefined;
        }>, {
            mode: Mode;
        }, {}, {
            IconLeft: any;
            IconRight: any;
            IconDoubleLeft: any;
            IconDoubleRight: any;
            RenderFunction: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                renderFunc: {
                    type: PropType<RenderFunc>;
                    required: true;
                };
            }>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                renderFunc: {
                    type: PropType<RenderFunc>;
                    required: true;
                };
            }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        PanelBody: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            rows: {
                type: PropType<import("./interface").Cell[][]>;
                default: () => never[];
            };
            value: {
                type: PropType<Dayjs>;
            };
            disabledDate: {
                type: PropType<import("./interface").DisabledDate>;
            };
            isSameTime: {
                type: PropType<import("./interface").IsSameTime>;
                required: true;
            };
            mode: {
                type: PropType<Mode>;
            };
            rangeValues: {
                type: PropType<(Dayjs | undefined)[]>;
            };
            dateRender: {
                type: PropType<RenderFunc>;
            };
        }>, {
            isWeek: import("vue").ComputedRef<boolean>;
            getCellClassName: (cellData: import("./interface").Cell) => (string | {
                [x: string]: boolean | undefined;
            } | undefined)[];
            onCellClick: (cellData: import("./interface").Cell) => void;
            onCellMouseEnter: (cellData: import("./interface").Cell) => void;
            onCellMouseLeave: (cellData: import("./interface").Cell) => void;
            getDateValue: typeof import("../_utils/date").getDateValue;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("cell-click" | "cell-mouse-enter")[], "cell-click" | "cell-mouse-enter", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            rows: {
                type: PropType<import("./interface").Cell[][]>;
                default: () => never[];
            };
            value: {
                type: PropType<Dayjs>;
            };
            disabledDate: {
                type: PropType<import("./interface").DisabledDate>;
            };
            isSameTime: {
                type: PropType<import("./interface").IsSameTime>;
                required: true;
            };
            mode: {
                type: PropType<Mode>;
            };
            rangeValues: {
                type: PropType<(Dayjs | undefined)[]>;
            };
            dateRender: {
                type: PropType<RenderFunc>;
            };
        }>> & Readonly<{
            "onCell-click"?: ((...args: any[]) => any) | undefined;
            "onCell-mouse-enter"?: ((...args: any[]) => any) | undefined;
        }>, {
            rows: import("./interface").Cell[][];
        }, {}, {
            RenderFunction: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                renderFunc: {
                    type: PropType<RenderFunc>;
                    required: true;
                };
            }>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                renderFunc: {
                    type: PropType<RenderFunc>;
                    required: true;
                };
            }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        PanelWeekList: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            weekList: {
                type: PropType<number[]>;
                required: true;
            };
        }>, {
            labelList: import("vue").ComputedRef<string[]>;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            weekList: {
                type: PropType<number[]>;
                required: true;
            };
        }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        TimePanel: import("vue").DefineComponent<{
            value?: Dayjs | undefined;
            defaultValue?: Dayjs | undefined;
            format: string;
            visible: boolean;
            hideFooter: boolean;
            isRange: boolean;
            disabled: boolean;
            use12Hours: boolean;
            step?: {
                hour?: number | undefined;
                minute?: number | undefined;
                second?: number | undefined;
            } | undefined;
            disabledHours?: (() => number[]) | undefined;
            disabledMinutes?: ((selectedHour?: number | undefined) => number[]) | undefined;
            disabledSeconds?: ((selectedHour?: number | undefined, selectedMinute?: number | undefined) => number[]) | undefined;
            hideDisabledOptions: boolean;
        }, {
            prefixCls: string;
            t: (key: string, ...args: any[]) => string;
            hours: import("vue").ComputedRef<import("../time-picker/interface").TimeList>;
            minutes: import("vue").ComputedRef<import("../time-picker/interface").TimeList>;
            seconds: import("vue").ComputedRef<import("../time-picker/interface").TimeList>;
            ampmList: import("vue").ComputedRef<import("../time-picker/interface").TimeList>;
            selectedValue: import("vue").Ref<{
                clone: () => Dayjs;
                isValid: () => boolean;
                year: {
                    (): number;
                    (value: number): Dayjs;
                };
                month: {
                    (): number;
                    (value: number): Dayjs;
                };
                date: {
                    (): number;
                    (value: number): Dayjs;
                };
                day: {
                    (): 0 | 1 | 2 | 4 | 3 | 6 | 5;
                    (value: number): Dayjs;
                };
                hour: {
                    (): number;
                    (value: number): Dayjs;
                };
                minute: {
                    (): number;
                    (value: number): Dayjs;
                };
                second: {
                    (): number;
                    (value: number): Dayjs;
                };
                millisecond: {
                    (): number;
                    (value: number): Dayjs;
                };
                set: (unit: import("dayjs").UnitType, value: number) => Dayjs;
                get: (unit: import("dayjs").UnitType) => number;
                add: {
                    (value: number, unit?: import("dayjs").ManipulateType | undefined): Dayjs;
                    (value: number, unit: import("dayjs").QUnitType): Dayjs;
                };
                subtract: {
                    (value: number, unit?: import("dayjs").ManipulateType | undefined): Dayjs;
                    (value: number, unit: import("dayjs").QUnitType): Dayjs;
                };
                startOf: {
                    (unit: import("dayjs").OpUnitType): Dayjs;
                    (unit: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q"): Dayjs;
                };
                endOf: {
                    (unit: import("dayjs").OpUnitType): Dayjs;
                    (unit: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q"): Dayjs;
                };
                format: (template?: string | undefined) => string;
                diff: (date?: string | number | Date | Dayjs | null | undefined, unit?: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q" | undefined, float?: boolean | undefined) => number;
                valueOf: () => number;
                unix: () => number;
                daysInMonth: () => number;
                toDate: () => Date;
                toJSON: () => string;
                toISOString: () => string;
                toString: () => string;
                utcOffset: () => number;
                isBefore: {
                    (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                    (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
                };
                isSame: {
                    (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                    (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
                };
                isAfter: {
                    (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                    (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
                };
                locale: {
                    (): string;
                    (preset: string | ILocale, object?: Partial<ILocale> | undefined): Dayjs;
                };
                isBetween: (a: string | number | Date | Dayjs | null | undefined, b: string | number | Date | Dayjs | null | undefined, c?: import("dayjs").OpUnitType | null | undefined, d?: "()" | "[]" | "[)" | "(]" | undefined) => boolean;
                week: {
                    (): number;
                    (value: number): Dayjs;
                };
                weekYear: () => number;
                quarter: {
                    (): number;
                    (quarter: number): Dayjs;
                };
            } | undefined, Dayjs | {
                clone: () => Dayjs;
                isValid: () => boolean;
                year: {
                    (): number;
                    (value: number): Dayjs;
                };
                month: {
                    (): number;
                    (value: number): Dayjs;
                };
                date: {
                    (): number;
                    (value: number): Dayjs;
                };
                day: {
                    (): 0 | 1 | 2 | 4 | 3 | 6 | 5;
                    (value: number): Dayjs;
                };
                hour: {
                    (): number;
                    (value: number): Dayjs;
                };
                minute: {
                    (): number;
                    (value: number): Dayjs;
                };
                second: {
                    (): number;
                    (value: number): Dayjs;
                };
                millisecond: {
                    (): number;
                    (value: number): Dayjs;
                };
                set: (unit: import("dayjs").UnitType, value: number) => Dayjs;
                get: (unit: import("dayjs").UnitType) => number;
                add: {
                    (value: number, unit?: import("dayjs").ManipulateType | undefined): Dayjs;
                    (value: number, unit: import("dayjs").QUnitType): Dayjs;
                };
                subtract: {
                    (value: number, unit?: import("dayjs").ManipulateType | undefined): Dayjs;
                    (value: number, unit: import("dayjs").QUnitType): Dayjs;
                };
                startOf: {
                    (unit: import("dayjs").OpUnitType): Dayjs;
                    (unit: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q"): Dayjs;
                };
                endOf: {
                    (unit: import("dayjs").OpUnitType): Dayjs;
                    (unit: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q"): Dayjs;
                };
                format: (template?: string | undefined) => string;
                diff: (date?: string | number | Date | Dayjs | null | undefined, unit?: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q" | undefined, float?: boolean | undefined) => number;
                valueOf: () => number;
                unix: () => number;
                daysInMonth: () => number;
                toDate: () => Date;
                toJSON: () => string;
                toISOString: () => string;
                toString: () => string;
                utcOffset: () => number;
                isBefore: {
                    (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                    (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
                };
                isSame: {
                    (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                    (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
                };
                isAfter: {
                    (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                    (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
                };
                locale: {
                    (): string;
                    (preset: string | ILocale, object?: Partial<ILocale> | undefined): Dayjs;
                };
                isBetween: (a: string | number | Date | Dayjs | null | undefined, b: string | number | Date | Dayjs | null | undefined, c?: import("dayjs").OpUnitType | null | undefined, d?: "()" | "[]" | "[)" | "(]" | undefined) => boolean;
                week: {
                    (): number;
                    (value: number): Dayjs;
                };
                weekYear: () => number;
                quarter: {
                    (): number;
                    (quarter: number): Dayjs;
                };
            } | undefined>;
            selectedHour: import("vue").ComputedRef<number | undefined>;
            selectedMinute: import("vue").ComputedRef<number | undefined>;
            selectedSecond: import("vue").ComputedRef<number | undefined>;
            selectedAmpm: import("vue").ComputedRef<"pm" | "am">;
            computedUse12Hours: import("vue").ComputedRef<boolean>;
            confirmBtnDisabled: import("vue").ComputedRef<boolean>;
            columns: import("vue").ComputedRef<string[]>;
            onSelect: (value: string | number, type?: "second" | "minute" | "hour" | "ampm") => void;
            onSelectNow(): void;
            onConfirm(): void;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            select: (value: Dayjs) => boolean;
            confirm: (value: Dayjs) => boolean;
        }, string, import("vue").PublicProps, Readonly<{
            value?: Dayjs | undefined;
            defaultValue?: Dayjs | undefined;
            format: string;
            visible: boolean;
            hideFooter: boolean;
            isRange: boolean;
            disabled: boolean;
            use12Hours: boolean;
            step?: {
                hour?: number | undefined;
                minute?: number | undefined;
                second?: number | undefined;
            } | undefined;
            disabledHours?: (() => number[]) | undefined;
            disabledMinutes?: ((selectedHour?: number | undefined) => number[]) | undefined;
            disabledSeconds?: ((selectedHour?: number | undefined, selectedMinute?: number | undefined) => number[]) | undefined;
            hideDisabledOptions: boolean;
        }> & Readonly<{
            onSelect?: ((value: Dayjs) => any) | undefined;
            onConfirm?: ((value: Dayjs) => any) | undefined;
        }>, {
            disabled: boolean;
            visible: boolean;
            format: string;
            hideFooter: boolean;
            use12Hours: boolean;
            hideDisabledOptions: boolean;
            isRange: boolean;
        }, {}, {
            TimeColumn: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                prefixCls: {
                    type: StringConstructor;
                    required: true;
                };
                list: {
                    type: PropType<import("../time-picker/interface").TimeList>;
                    required: true;
                };
                value: {
                    type: (StringConstructor | NumberConstructor)[];
                };
                visible: {
                    type: BooleanConstructor;
                };
            }>, {
                refWrapper: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
                refMap: import("vue").Ref<Map<string | number, HTMLElement> & Omit<Map<string | number, HTMLElement>, keyof Map<any, any>>, Map<string | number, HTMLElement> | (Map<string | number, HTMLElement> & Omit<Map<string | number, HTMLElement>, keyof Map<any, any>>)>;
                onItemRef(el: HTMLElement, item: import("../time-picker/interface").TimeListItem): void;
                onItemClick(item: import("../time-picker/interface").TimeListItem): void;
            }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "select"[], "select", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                prefixCls: {
                    type: StringConstructor;
                    required: true;
                };
                list: {
                    type: PropType<import("../time-picker/interface").TimeList>;
                    required: true;
                };
                value: {
                    type: (StringConstructor | NumberConstructor)[];
                };
                visible: {
                    type: BooleanConstructor;
                };
            }>> & Readonly<{
                onSelect?: ((...args: any[]) => any) | undefined;
            }>, {
                visible: boolean;
            }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
            Button: {
                new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
                    type: {
                        type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                    };
                    shape: {
                        type: PropType<"round" | "circle" | "square">;
                    };
                    status: {
                        type: PropType<"normal" | "success" | "warning" | "danger">;
                    };
                    size: {
                        type: PropType<"mini" | "medium" | "large" | "small">;
                    };
                    long: {
                        type: BooleanConstructor;
                        default: boolean;
                    };
                    loading: {
                        type: BooleanConstructor;
                        default: boolean;
                    };
                    disabled: {
                        type: BooleanConstructor;
                    };
                    htmlType: {
                        type: StringConstructor;
                        default: string;
                    };
                    autofocus: {
                        type: BooleanConstructor;
                        default: boolean;
                    };
                    href: StringConstructor;
                }>> & Readonly<{
                    onClick?: ((ev: MouseEvent) => any) | undefined;
                }>, {
                    prefixCls: string;
                    cls: import("vue").ComputedRef<(string | {
                        [x: string]: boolean;
                    })[]>;
                    mergedDisabled: import("vue").ComputedRef<boolean>;
                    handleClick: (ev: MouseEvent) => void;
                }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
                    click: (ev: MouseEvent) => true;
                }, import("vue").PublicProps, {
                    disabled: boolean;
                    autofocus: boolean;
                    loading: boolean;
                    long: boolean;
                    htmlType: string;
                }, true, {}, {}, {
                    IconLoading: any;
                } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
                    P: {};
                    B: {};
                    D: {};
                    C: {};
                    M: {};
                    Defaults: {};
                }, Readonly<import("vue").ExtractPropTypes<{
                    type: {
                        type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                    };
                    shape: {
                        type: PropType<"round" | "circle" | "square">;
                    };
                    status: {
                        type: PropType<"normal" | "success" | "warning" | "danger">;
                    };
                    size: {
                        type: PropType<"mini" | "medium" | "large" | "small">;
                    };
                    long: {
                        type: BooleanConstructor;
                        default: boolean;
                    };
                    loading: {
                        type: BooleanConstructor;
                        default: boolean;
                    };
                    disabled: {
                        type: BooleanConstructor;
                    };
                    htmlType: {
                        type: StringConstructor;
                        default: string;
                    };
                    autofocus: {
                        type: BooleanConstructor;
                        default: boolean;
                    };
                    href: StringConstructor;
                }>> & Readonly<{
                    onClick?: ((ev: MouseEvent) => any) | undefined;
                }>, {
                    prefixCls: string;
                    cls: import("vue").ComputedRef<(string | {
                        [x: string]: boolean;
                    })[]>;
                    mergedDisabled: import("vue").ComputedRef<boolean>;
                    handleClick: (ev: MouseEvent) => void;
                }, {}, {}, {}, {
                    disabled: boolean;
                    autofocus: boolean;
                    loading: boolean;
                    long: boolean;
                    htmlType: string;
                }>;
                __isFragment?: undefined;
                __isTeleport?: undefined;
                __isSuspense?: undefined;
            } & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
                type: {
                    type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                };
                shape: {
                    type: PropType<"round" | "circle" | "square">;
                };
                status: {
                    type: PropType<"normal" | "success" | "warning" | "danger">;
                };
                size: {
                    type: PropType<"mini" | "medium" | "large" | "small">;
                };
                long: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                loading: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                disabled: {
                    type: BooleanConstructor;
                };
                htmlType: {
                    type: StringConstructor;
                    default: string;
                };
                autofocus: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                href: StringConstructor;
            }>> & Readonly<{
                onClick?: ((ev: MouseEvent) => any) | undefined;
            }>, {
                prefixCls: string;
                cls: import("vue").ComputedRef<(string | {
                    [x: string]: boolean;
                })[]>;
                mergedDisabled: import("vue").ComputedRef<boolean>;
                handleClick: (ev: MouseEvent) => void;
            }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
                click: (ev: MouseEvent) => true;
            }, string, {
                disabled: boolean;
                autofocus: boolean;
                loading: boolean;
                long: boolean;
                htmlType: string;
            }, {}, string, {}, {
                IconLoading: any;
            } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
                Group: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                    type: {
                        type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                    };
                    status: {
                        type: PropType<"normal" | "success" | "warning" | "danger">;
                    };
                    shape: {
                        type: PropType<"round" | "circle" | "square">;
                    };
                    size: {
                        type: PropType<"mini" | "medium" | "large" | "small">;
                    };
                    disabled: {
                        type: BooleanConstructor;
                    };
                }>, {
                    prefixCls: string;
                }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                    type: {
                        type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                    };
                    status: {
                        type: PropType<"normal" | "success" | "warning" | "danger">;
                    };
                    shape: {
                        type: PropType<"round" | "circle" | "square">;
                    };
                    size: {
                        type: PropType<"mini" | "medium" | "large" | "small">;
                    };
                    disabled: {
                        type: BooleanConstructor;
                    };
                }>> & Readonly<{}>, {
                    disabled: boolean;
                }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
                install: (app: import("vue").App<any>, options?: import("../_utils/types").ArcoOptions | undefined) => void;
            };
        }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        IconCalendar: any;
        IconClockCircle: any;
    }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    WeekPanel: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        dayStartOfWeek: {
            type: PropType<WeekStart>;
            default: number;
        };
    }>, {
        isSameTime: import("./interface").IsSameTime;
        onSelect: (value: Dayjs) => void;
        onCellMouseEnter: (value: Dayjs) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("select" | "cell-mouse-enter")[], "select" | "cell-mouse-enter", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        dayStartOfWeek: {
            type: PropType<WeekStart>;
            default: number;
        };
    }>> & Readonly<{
        onSelect?: ((...args: any[]) => any) | undefined;
        "onCell-mouse-enter"?: ((...args: any[]) => any) | undefined;
    }>, {
        dayStartOfWeek: WeekStart;
    }, {}, {
        DatePanel: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            isRange: {
                type: BooleanConstructor;
            };
            value: {
                type: PropType<Dayjs>;
            };
            rangeValues: {
                type: PropType<(Dayjs | undefined)[]>;
            };
            headerValue: {
                type: PropType<Dayjs>;
                required: true;
            };
            footerValue: {
                type: PropType<Dayjs>;
            };
            timePickerValue: {
                type: PropType<Dayjs>;
            };
            headerOperations: {
                type: PropType<import("./interface").HeaderOperations>;
                default: () => {};
            };
            headerIcons: {
                type: PropType<import("./interface").HeaderIcons>;
                default: () => {};
            };
            dayStartOfWeek: {
                type: PropType<WeekStart>;
                default: number;
            };
            disabledDate: {
                type: PropType<import("./interface").DisabledDate>;
            };
            disabledTime: {
                type: PropType<import("./interface").DisabledTime>;
            };
            isSameTime: {
                type: PropType<import("./interface").IsSameTime>;
            };
            mode: {
                type: PropType<Mode>;
                default: string;
            };
            showTime: {
                type: BooleanConstructor;
            };
            timePickerProps: {
                type: PropType<Partial<import("../time-picker/interface").TimePickerProps>>;
            };
            currentView: {
                type: PropType<"time" | "date">;
            };
            dateRender: {
                type: PropType<RenderFunc>;
            };
            disabled: {
                type: BooleanConstructor;
            };
            onHeaderLabelClick: {
                type: PropType<import("./panels/header").HeaderLabelClickFunc>;
            };
        }>, {
            prefixCls: import("vue").ComputedRef<string>;
            classNames: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            pickerPrefixCls: string;
            headerTitle: import("vue").ComputedRef<string>;
            rows: import("vue").ComputedRef<import("./interface").Cell[][]>;
            weekList: import("vue").ComputedRef<number[]>;
            mergedIsSameTime: import("vue").ComputedRef<import("./interface").IsSameTime>;
            disabledTimeProps: import("vue").ComputedRef<import("./interface").DisabledTimeProps>;
            onCellClick: (cellData: import("./interface").Cell) => void;
            onCellMouseEnter: (cellData: import("./interface").Cell) => void;
            onTimePanelSelect: (time: Dayjs) => void;
            showViewTabs: import("vue").ComputedRef<boolean>;
            showDateView: import("vue").ComputedRef<boolean>;
            showTimeView: import("vue").ComputedRef<boolean>;
            changeViewTo: (newView: "time" | "date") => void;
            datePickerT: (key: string, ...args: any[]) => any;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("select" | "cell-mouse-enter" | "time-picker-select" | "current-view-change" | "update:currentView")[], "select" | "cell-mouse-enter" | "time-picker-select" | "current-view-change" | "update:currentView", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            isRange: {
                type: BooleanConstructor;
            };
            value: {
                type: PropType<Dayjs>;
            };
            rangeValues: {
                type: PropType<(Dayjs | undefined)[]>;
            };
            headerValue: {
                type: PropType<Dayjs>;
                required: true;
            };
            footerValue: {
                type: PropType<Dayjs>;
            };
            timePickerValue: {
                type: PropType<Dayjs>;
            };
            headerOperations: {
                type: PropType<import("./interface").HeaderOperations>;
                default: () => {};
            };
            headerIcons: {
                type: PropType<import("./interface").HeaderIcons>;
                default: () => {};
            };
            dayStartOfWeek: {
                type: PropType<WeekStart>;
                default: number;
            };
            disabledDate: {
                type: PropType<import("./interface").DisabledDate>;
            };
            disabledTime: {
                type: PropType<import("./interface").DisabledTime>;
            };
            isSameTime: {
                type: PropType<import("./interface").IsSameTime>;
            };
            mode: {
                type: PropType<Mode>;
                default: string;
            };
            showTime: {
                type: BooleanConstructor;
            };
            timePickerProps: {
                type: PropType<Partial<import("../time-picker/interface").TimePickerProps>>;
            };
            currentView: {
                type: PropType<"time" | "date">;
            };
            dateRender: {
                type: PropType<RenderFunc>;
            };
            disabled: {
                type: BooleanConstructor;
            };
            onHeaderLabelClick: {
                type: PropType<import("./panels/header").HeaderLabelClickFunc>;
            };
        }>> & Readonly<{
            onSelect?: ((...args: any[]) => any) | undefined;
            "onCell-mouse-enter"?: ((...args: any[]) => any) | undefined;
            "onTime-picker-select"?: ((...args: any[]) => any) | undefined;
            "onCurrent-view-change"?: ((...args: any[]) => any) | undefined;
            "onUpdate:currentView"?: ((...args: any[]) => any) | undefined;
        }>, {
            disabled: boolean;
            mode: Mode;
            dayStartOfWeek: WeekStart;
            showTime: boolean;
            isRange: boolean;
            headerOperations: import("./interface").HeaderOperations;
            headerIcons: import("./interface").HeaderIcons;
        }, {}, {
            PanelHeader: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                prefixCls: {
                    type: StringConstructor;
                    required: true;
                };
                title: {
                    type: StringConstructor;
                    required: true;
                };
                mode: {
                    type: PropType<Mode>;
                    default: string;
                };
                value: {
                    type: PropType<Dayjs>;
                };
                icons: {
                    type: PropType<import("./interface").HeaderIcons>;
                };
                onPrev: {
                    type: PropType<(payload: MouseEvent) => void>;
                };
                onSuperPrev: {
                    type: PropType<(payload: MouseEvent) => void>;
                };
                onNext: {
                    type: PropType<(payload: MouseEvent) => void>;
                };
                onSuperNext: {
                    type: PropType<(payload: MouseEvent) => void>;
                };
                onLabelClick: {
                    type: PropType<import("./panels/header").HeaderLabelClickFunc>;
                };
            }>, {
                showPrev: import("vue").ComputedRef<boolean>;
                showSuperPrev: import("vue").ComputedRef<boolean>;
                showNext: import("vue").ComputedRef<boolean>;
                showSuperNext: import("vue").ComputedRef<boolean>;
                year: import("vue").ComputedRef<string>;
                month: import("vue").ComputedRef<string>;
                getIconClassName: (show?: boolean | undefined) => (string | {
                    [x: string]: boolean;
                })[];
            }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "label-click"[], "label-click", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                prefixCls: {
                    type: StringConstructor;
                    required: true;
                };
                title: {
                    type: StringConstructor;
                    required: true;
                };
                mode: {
                    type: PropType<Mode>;
                    default: string;
                };
                value: {
                    type: PropType<Dayjs>;
                };
                icons: {
                    type: PropType<import("./interface").HeaderIcons>;
                };
                onPrev: {
                    type: PropType<(payload: MouseEvent) => void>;
                };
                onSuperPrev: {
                    type: PropType<(payload: MouseEvent) => void>;
                };
                onNext: {
                    type: PropType<(payload: MouseEvent) => void>;
                };
                onSuperNext: {
                    type: PropType<(payload: MouseEvent) => void>;
                };
                onLabelClick: {
                    type: PropType<import("./panels/header").HeaderLabelClickFunc>;
                };
            }>> & Readonly<{
                "onLabel-click"?: ((...args: any[]) => any) | undefined;
            }>, {
                mode: Mode;
            }, {}, {
                IconLeft: any;
                IconRight: any;
                IconDoubleLeft: any;
                IconDoubleRight: any;
                RenderFunction: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                    renderFunc: {
                        type: PropType<RenderFunc>;
                        required: true;
                    };
                }>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                    renderFunc: {
                        type: PropType<RenderFunc>;
                        required: true;
                    };
                }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
            }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
            PanelBody: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                prefixCls: {
                    type: StringConstructor;
                    required: true;
                };
                rows: {
                    type: PropType<import("./interface").Cell[][]>;
                    default: () => never[];
                };
                value: {
                    type: PropType<Dayjs>;
                };
                disabledDate: {
                    type: PropType<import("./interface").DisabledDate>;
                };
                isSameTime: {
                    type: PropType<import("./interface").IsSameTime>;
                    required: true;
                };
                mode: {
                    type: PropType<Mode>;
                };
                rangeValues: {
                    type: PropType<(Dayjs | undefined)[]>;
                };
                dateRender: {
                    type: PropType<RenderFunc>;
                };
            }>, {
                isWeek: import("vue").ComputedRef<boolean>;
                getCellClassName: (cellData: import("./interface").Cell) => (string | {
                    [x: string]: boolean | undefined;
                } | undefined)[];
                onCellClick: (cellData: import("./interface").Cell) => void;
                onCellMouseEnter: (cellData: import("./interface").Cell) => void;
                onCellMouseLeave: (cellData: import("./interface").Cell) => void;
                getDateValue: typeof import("../_utils/date").getDateValue;
            }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("cell-click" | "cell-mouse-enter")[], "cell-click" | "cell-mouse-enter", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                prefixCls: {
                    type: StringConstructor;
                    required: true;
                };
                rows: {
                    type: PropType<import("./interface").Cell[][]>;
                    default: () => never[];
                };
                value: {
                    type: PropType<Dayjs>;
                };
                disabledDate: {
                    type: PropType<import("./interface").DisabledDate>;
                };
                isSameTime: {
                    type: PropType<import("./interface").IsSameTime>;
                    required: true;
                };
                mode: {
                    type: PropType<Mode>;
                };
                rangeValues: {
                    type: PropType<(Dayjs | undefined)[]>;
                };
                dateRender: {
                    type: PropType<RenderFunc>;
                };
            }>> & Readonly<{
                "onCell-click"?: ((...args: any[]) => any) | undefined;
                "onCell-mouse-enter"?: ((...args: any[]) => any) | undefined;
            }>, {
                rows: import("./interface").Cell[][];
            }, {}, {
                RenderFunction: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                    renderFunc: {
                        type: PropType<RenderFunc>;
                        required: true;
                    };
                }>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                    renderFunc: {
                        type: PropType<RenderFunc>;
                        required: true;
                    };
                }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
            }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
            PanelWeekList: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                prefixCls: {
                    type: StringConstructor;
                    required: true;
                };
                weekList: {
                    type: PropType<number[]>;
                    required: true;
                };
            }>, {
                labelList: import("vue").ComputedRef<string[]>;
            }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                prefixCls: {
                    type: StringConstructor;
                    required: true;
                };
                weekList: {
                    type: PropType<number[]>;
                    required: true;
                };
            }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
            TimePanel: import("vue").DefineComponent<{
                value?: Dayjs | undefined;
                defaultValue?: Dayjs | undefined;
                format: string;
                visible: boolean;
                hideFooter: boolean;
                isRange: boolean;
                disabled: boolean;
                use12Hours: boolean;
                step?: {
                    hour?: number | undefined;
                    minute?: number | undefined;
                    second?: number | undefined;
                } | undefined;
                disabledHours?: (() => number[]) | undefined;
                disabledMinutes?: ((selectedHour?: number | undefined) => number[]) | undefined;
                disabledSeconds?: ((selectedHour?: number | undefined, selectedMinute?: number | undefined) => number[]) | undefined;
                hideDisabledOptions: boolean;
            }, {
                prefixCls: string;
                t: (key: string, ...args: any[]) => string;
                hours: import("vue").ComputedRef<import("../time-picker/interface").TimeList>;
                minutes: import("vue").ComputedRef<import("../time-picker/interface").TimeList>;
                seconds: import("vue").ComputedRef<import("../time-picker/interface").TimeList>;
                ampmList: import("vue").ComputedRef<import("../time-picker/interface").TimeList>;
                selectedValue: import("vue").Ref<{
                    clone: () => Dayjs;
                    isValid: () => boolean;
                    year: {
                        (): number;
                        (value: number): Dayjs;
                    };
                    month: {
                        (): number;
                        (value: number): Dayjs;
                    };
                    date: {
                        (): number;
                        (value: number): Dayjs;
                    };
                    day: {
                        (): 0 | 1 | 2 | 4 | 3 | 6 | 5;
                        (value: number): Dayjs;
                    };
                    hour: {
                        (): number;
                        (value: number): Dayjs;
                    };
                    minute: {
                        (): number;
                        (value: number): Dayjs;
                    };
                    second: {
                        (): number;
                        (value: number): Dayjs;
                    };
                    millisecond: {
                        (): number;
                        (value: number): Dayjs;
                    };
                    set: (unit: import("dayjs").UnitType, value: number) => Dayjs;
                    get: (unit: import("dayjs").UnitType) => number;
                    add: {
                        (value: number, unit?: import("dayjs").ManipulateType | undefined): Dayjs;
                        (value: number, unit: import("dayjs").QUnitType): Dayjs;
                    };
                    subtract: {
                        (value: number, unit?: import("dayjs").ManipulateType | undefined): Dayjs;
                        (value: number, unit: import("dayjs").QUnitType): Dayjs;
                    };
                    startOf: {
                        (unit: import("dayjs").OpUnitType): Dayjs;
                        (unit: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q"): Dayjs;
                    };
                    endOf: {
                        (unit: import("dayjs").OpUnitType): Dayjs;
                        (unit: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q"): Dayjs;
                    };
                    format: (template?: string | undefined) => string;
                    diff: (date?: string | number | Date | Dayjs | null | undefined, unit?: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q" | undefined, float?: boolean | undefined) => number;
                    valueOf: () => number;
                    unix: () => number;
                    daysInMonth: () => number;
                    toDate: () => Date;
                    toJSON: () => string;
                    toISOString: () => string;
                    toString: () => string;
                    utcOffset: () => number;
                    isBefore: {
                        (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                        (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
                    };
                    isSame: {
                        (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                        (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
                    };
                    isAfter: {
                        (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                        (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
                    };
                    locale: {
                        (): string;
                        (preset: string | ILocale, object?: Partial<ILocale> | undefined): Dayjs;
                    };
                    isBetween: (a: string | number | Date | Dayjs | null | undefined, b: string | number | Date | Dayjs | null | undefined, c?: import("dayjs").OpUnitType | null | undefined, d?: "()" | "[]" | "[)" | "(]" | undefined) => boolean;
                    week: {
                        (): number;
                        (value: number): Dayjs;
                    };
                    weekYear: () => number;
                    quarter: {
                        (): number;
                        (quarter: number): Dayjs;
                    };
                } | undefined, Dayjs | {
                    clone: () => Dayjs;
                    isValid: () => boolean;
                    year: {
                        (): number;
                        (value: number): Dayjs;
                    };
                    month: {
                        (): number;
                        (value: number): Dayjs;
                    };
                    date: {
                        (): number;
                        (value: number): Dayjs;
                    };
                    day: {
                        (): 0 | 1 | 2 | 4 | 3 | 6 | 5;
                        (value: number): Dayjs;
                    };
                    hour: {
                        (): number;
                        (value: number): Dayjs;
                    };
                    minute: {
                        (): number;
                        (value: number): Dayjs;
                    };
                    second: {
                        (): number;
                        (value: number): Dayjs;
                    };
                    millisecond: {
                        (): number;
                        (value: number): Dayjs;
                    };
                    set: (unit: import("dayjs").UnitType, value: number) => Dayjs;
                    get: (unit: import("dayjs").UnitType) => number;
                    add: {
                        (value: number, unit?: import("dayjs").ManipulateType | undefined): Dayjs;
                        (value: number, unit: import("dayjs").QUnitType): Dayjs;
                    };
                    subtract: {
                        (value: number, unit?: import("dayjs").ManipulateType | undefined): Dayjs;
                        (value: number, unit: import("dayjs").QUnitType): Dayjs;
                    };
                    startOf: {
                        (unit: import("dayjs").OpUnitType): Dayjs;
                        (unit: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q"): Dayjs;
                    };
                    endOf: {
                        (unit: import("dayjs").OpUnitType): Dayjs;
                        (unit: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q"): Dayjs;
                    };
                    format: (template?: string | undefined) => string;
                    diff: (date?: string | number | Date | Dayjs | null | undefined, unit?: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q" | undefined, float?: boolean | undefined) => number;
                    valueOf: () => number;
                    unix: () => number;
                    daysInMonth: () => number;
                    toDate: () => Date;
                    toJSON: () => string;
                    toISOString: () => string;
                    toString: () => string;
                    utcOffset: () => number;
                    isBefore: {
                        (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                        (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
                    };
                    isSame: {
                        (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                        (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
                    };
                    isAfter: {
                        (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                        (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
                    };
                    locale: {
                        (): string;
                        (preset: string | ILocale, object?: Partial<ILocale> | undefined): Dayjs;
                    };
                    isBetween: (a: string | number | Date | Dayjs | null | undefined, b: string | number | Date | Dayjs | null | undefined, c?: import("dayjs").OpUnitType | null | undefined, d?: "()" | "[]" | "[)" | "(]" | undefined) => boolean;
                    week: {
                        (): number;
                        (value: number): Dayjs;
                    };
                    weekYear: () => number;
                    quarter: {
                        (): number;
                        (quarter: number): Dayjs;
                    };
                } | undefined>;
                selectedHour: import("vue").ComputedRef<number | undefined>;
                selectedMinute: import("vue").ComputedRef<number | undefined>;
                selectedSecond: import("vue").ComputedRef<number | undefined>;
                selectedAmpm: import("vue").ComputedRef<"pm" | "am">;
                computedUse12Hours: import("vue").ComputedRef<boolean>;
                confirmBtnDisabled: import("vue").ComputedRef<boolean>;
                columns: import("vue").ComputedRef<string[]>;
                onSelect: (value: string | number, type?: "second" | "minute" | "hour" | "ampm") => void;
                onSelectNow(): void;
                onConfirm(): void;
            }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
                select: (value: Dayjs) => boolean;
                confirm: (value: Dayjs) => boolean;
            }, string, import("vue").PublicProps, Readonly<{
                value?: Dayjs | undefined;
                defaultValue?: Dayjs | undefined;
                format: string;
                visible: boolean;
                hideFooter: boolean;
                isRange: boolean;
                disabled: boolean;
                use12Hours: boolean;
                step?: {
                    hour?: number | undefined;
                    minute?: number | undefined;
                    second?: number | undefined;
                } | undefined;
                disabledHours?: (() => number[]) | undefined;
                disabledMinutes?: ((selectedHour?: number | undefined) => number[]) | undefined;
                disabledSeconds?: ((selectedHour?: number | undefined, selectedMinute?: number | undefined) => number[]) | undefined;
                hideDisabledOptions: boolean;
            }> & Readonly<{
                onSelect?: ((value: Dayjs) => any) | undefined;
                onConfirm?: ((value: Dayjs) => any) | undefined;
            }>, {
                disabled: boolean;
                visible: boolean;
                format: string;
                hideFooter: boolean;
                use12Hours: boolean;
                hideDisabledOptions: boolean;
                isRange: boolean;
            }, {}, {
                TimeColumn: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                    prefixCls: {
                        type: StringConstructor;
                        required: true;
                    };
                    list: {
                        type: PropType<import("../time-picker/interface").TimeList>;
                        required: true;
                    };
                    value: {
                        type: (StringConstructor | NumberConstructor)[];
                    };
                    visible: {
                        type: BooleanConstructor;
                    };
                }>, {
                    refWrapper: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
                    refMap: import("vue").Ref<Map<string | number, HTMLElement> & Omit<Map<string | number, HTMLElement>, keyof Map<any, any>>, Map<string | number, HTMLElement> | (Map<string | number, HTMLElement> & Omit<Map<string | number, HTMLElement>, keyof Map<any, any>>)>;
                    onItemRef(el: HTMLElement, item: import("../time-picker/interface").TimeListItem): void;
                    onItemClick(item: import("../time-picker/interface").TimeListItem): void;
                }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "select"[], "select", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                    prefixCls: {
                        type: StringConstructor;
                        required: true;
                    };
                    list: {
                        type: PropType<import("../time-picker/interface").TimeList>;
                        required: true;
                    };
                    value: {
                        type: (StringConstructor | NumberConstructor)[];
                    };
                    visible: {
                        type: BooleanConstructor;
                    };
                }>> & Readonly<{
                    onSelect?: ((...args: any[]) => any) | undefined;
                }>, {
                    visible: boolean;
                }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
                Button: {
                    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
                        type: {
                            type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                        };
                        shape: {
                            type: PropType<"round" | "circle" | "square">;
                        };
                        status: {
                            type: PropType<"normal" | "success" | "warning" | "danger">;
                        };
                        size: {
                            type: PropType<"mini" | "medium" | "large" | "small">;
                        };
                        long: {
                            type: BooleanConstructor;
                            default: boolean;
                        };
                        loading: {
                            type: BooleanConstructor;
                            default: boolean;
                        };
                        disabled: {
                            type: BooleanConstructor;
                        };
                        htmlType: {
                            type: StringConstructor;
                            default: string;
                        };
                        autofocus: {
                            type: BooleanConstructor;
                            default: boolean;
                        };
                        href: StringConstructor;
                    }>> & Readonly<{
                        onClick?: ((ev: MouseEvent) => any) | undefined;
                    }>, {
                        prefixCls: string;
                        cls: import("vue").ComputedRef<(string | {
                            [x: string]: boolean;
                        })[]>;
                        mergedDisabled: import("vue").ComputedRef<boolean>;
                        handleClick: (ev: MouseEvent) => void;
                    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
                        click: (ev: MouseEvent) => true;
                    }, import("vue").PublicProps, {
                        disabled: boolean;
                        autofocus: boolean;
                        loading: boolean;
                        long: boolean;
                        htmlType: string;
                    }, true, {}, {}, {
                        IconLoading: any;
                    } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
                        P: {};
                        B: {};
                        D: {};
                        C: {};
                        M: {};
                        Defaults: {};
                    }, Readonly<import("vue").ExtractPropTypes<{
                        type: {
                            type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                        };
                        shape: {
                            type: PropType<"round" | "circle" | "square">;
                        };
                        status: {
                            type: PropType<"normal" | "success" | "warning" | "danger">;
                        };
                        size: {
                            type: PropType<"mini" | "medium" | "large" | "small">;
                        };
                        long: {
                            type: BooleanConstructor;
                            default: boolean;
                        };
                        loading: {
                            type: BooleanConstructor;
                            default: boolean;
                        };
                        disabled: {
                            type: BooleanConstructor;
                        };
                        htmlType: {
                            type: StringConstructor;
                            default: string;
                        };
                        autofocus: {
                            type: BooleanConstructor;
                            default: boolean;
                        };
                        href: StringConstructor;
                    }>> & Readonly<{
                        onClick?: ((ev: MouseEvent) => any) | undefined;
                    }>, {
                        prefixCls: string;
                        cls: import("vue").ComputedRef<(string | {
                            [x: string]: boolean;
                        })[]>;
                        mergedDisabled: import("vue").ComputedRef<boolean>;
                        handleClick: (ev: MouseEvent) => void;
                    }, {}, {}, {}, {
                        disabled: boolean;
                        autofocus: boolean;
                        loading: boolean;
                        long: boolean;
                        htmlType: string;
                    }>;
                    __isFragment?: undefined;
                    __isTeleport?: undefined;
                    __isSuspense?: undefined;
                } & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
                    type: {
                        type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                    };
                    shape: {
                        type: PropType<"round" | "circle" | "square">;
                    };
                    status: {
                        type: PropType<"normal" | "success" | "warning" | "danger">;
                    };
                    size: {
                        type: PropType<"mini" | "medium" | "large" | "small">;
                    };
                    long: {
                        type: BooleanConstructor;
                        default: boolean;
                    };
                    loading: {
                        type: BooleanConstructor;
                        default: boolean;
                    };
                    disabled: {
                        type: BooleanConstructor;
                    };
                    htmlType: {
                        type: StringConstructor;
                        default: string;
                    };
                    autofocus: {
                        type: BooleanConstructor;
                        default: boolean;
                    };
                    href: StringConstructor;
                }>> & Readonly<{
                    onClick?: ((ev: MouseEvent) => any) | undefined;
                }>, {
                    prefixCls: string;
                    cls: import("vue").ComputedRef<(string | {
                        [x: string]: boolean;
                    })[]>;
                    mergedDisabled: import("vue").ComputedRef<boolean>;
                    handleClick: (ev: MouseEvent) => void;
                }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
                    click: (ev: MouseEvent) => true;
                }, string, {
                    disabled: boolean;
                    autofocus: boolean;
                    loading: boolean;
                    long: boolean;
                    htmlType: string;
                }, {}, string, {}, {
                    IconLoading: any;
                } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
                    Group: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                        type: {
                            type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                        };
                        status: {
                            type: PropType<"normal" | "success" | "warning" | "danger">;
                        };
                        shape: {
                            type: PropType<"round" | "circle" | "square">;
                        };
                        size: {
                            type: PropType<"mini" | "medium" | "large" | "small">;
                        };
                        disabled: {
                            type: BooleanConstructor;
                        };
                    }>, {
                        prefixCls: string;
                    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                        type: {
                            type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                        };
                        status: {
                            type: PropType<"normal" | "success" | "warning" | "danger">;
                        };
                        shape: {
                            type: PropType<"round" | "circle" | "square">;
                        };
                        size: {
                            type: PropType<"mini" | "medium" | "large" | "small">;
                        };
                        disabled: {
                            type: BooleanConstructor;
                        };
                    }>> & Readonly<{}>, {
                        disabled: boolean;
                    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
                    install: (app: import("vue").App<any>, options?: import("../_utils/types").ArcoOptions | undefined) => void;
                };
            }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
            IconCalendar: any;
            IconClockCircle: any;
        }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    MonthPanel: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        headerValue: {
            type: PropType<Dayjs>;
            required: true;
        };
        headerOperations: {
            type: PropType<import("./interface").HeaderOperations>;
            default: () => {};
        };
        headerIcons: {
            type: PropType<import("./interface").HeaderIcons>;
            default: () => {};
        };
        value: {
            type: PropType<Dayjs>;
        };
        disabledDate: {
            type: PropType<import("./interface").DisabledDate>;
        };
        rangeValues: {
            type: PropType<(Dayjs | undefined)[]>;
        };
        dateRender: {
            type: PropType<RenderFunc>;
        };
        onHeaderLabelClick: {
            type: PropType<import("./panels/header").HeaderLabelClickFunc>;
        };
        abbreviation: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>, {
        prefixCls: import("vue").ComputedRef<string>;
        pickerPrefixCls: string;
        headerTitle: import("vue").ComputedRef<string>;
        rows: import("vue").ComputedRef<{
            label: any;
            value: Dayjs;
        }[][]>;
        isSameTime: import("./interface").IsSameTime;
        onCellClick: (cellData: import("./interface").Cell) => void;
        onCellMouseEnter: (cellData: import("./interface").Cell) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("select" | "cell-mouse-enter")[], "select" | "cell-mouse-enter", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        headerValue: {
            type: PropType<Dayjs>;
            required: true;
        };
        headerOperations: {
            type: PropType<import("./interface").HeaderOperations>;
            default: () => {};
        };
        headerIcons: {
            type: PropType<import("./interface").HeaderIcons>;
            default: () => {};
        };
        value: {
            type: PropType<Dayjs>;
        };
        disabledDate: {
            type: PropType<import("./interface").DisabledDate>;
        };
        rangeValues: {
            type: PropType<(Dayjs | undefined)[]>;
        };
        dateRender: {
            type: PropType<RenderFunc>;
        };
        onHeaderLabelClick: {
            type: PropType<import("./panels/header").HeaderLabelClickFunc>;
        };
        abbreviation: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{
        onSelect?: ((...args: any[]) => any) | undefined;
        "onCell-mouse-enter"?: ((...args: any[]) => any) | undefined;
    }>, {
        headerOperations: import("./interface").HeaderOperations;
        headerIcons: import("./interface").HeaderIcons;
        abbreviation: boolean;
    }, {}, {
        PanelHeader: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            title: {
                type: StringConstructor;
                required: true;
            };
            mode: {
                type: PropType<Mode>;
                default: string;
            };
            value: {
                type: PropType<Dayjs>;
            };
            icons: {
                type: PropType<import("./interface").HeaderIcons>;
            };
            onPrev: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onSuperPrev: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onNext: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onSuperNext: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onLabelClick: {
                type: PropType<import("./panels/header").HeaderLabelClickFunc>;
            };
        }>, {
            showPrev: import("vue").ComputedRef<boolean>;
            showSuperPrev: import("vue").ComputedRef<boolean>;
            showNext: import("vue").ComputedRef<boolean>;
            showSuperNext: import("vue").ComputedRef<boolean>;
            year: import("vue").ComputedRef<string>;
            month: import("vue").ComputedRef<string>;
            getIconClassName: (show?: boolean | undefined) => (string | {
                [x: string]: boolean;
            })[];
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "label-click"[], "label-click", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            title: {
                type: StringConstructor;
                required: true;
            };
            mode: {
                type: PropType<Mode>;
                default: string;
            };
            value: {
                type: PropType<Dayjs>;
            };
            icons: {
                type: PropType<import("./interface").HeaderIcons>;
            };
            onPrev: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onSuperPrev: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onNext: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onSuperNext: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onLabelClick: {
                type: PropType<import("./panels/header").HeaderLabelClickFunc>;
            };
        }>> & Readonly<{
            "onLabel-click"?: ((...args: any[]) => any) | undefined;
        }>, {
            mode: Mode;
        }, {}, {
            IconLeft: any;
            IconRight: any;
            IconDoubleLeft: any;
            IconDoubleRight: any;
            RenderFunction: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                renderFunc: {
                    type: PropType<RenderFunc>;
                    required: true;
                };
            }>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                renderFunc: {
                    type: PropType<RenderFunc>;
                    required: true;
                };
            }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        PanelBody: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            rows: {
                type: PropType<import("./interface").Cell[][]>;
                default: () => never[];
            };
            value: {
                type: PropType<Dayjs>;
            };
            disabledDate: {
                type: PropType<import("./interface").DisabledDate>;
            };
            isSameTime: {
                type: PropType<import("./interface").IsSameTime>;
                required: true;
            };
            mode: {
                type: PropType<Mode>;
            };
            rangeValues: {
                type: PropType<(Dayjs | undefined)[]>;
            };
            dateRender: {
                type: PropType<RenderFunc>;
            };
        }>, {
            isWeek: import("vue").ComputedRef<boolean>;
            getCellClassName: (cellData: import("./interface").Cell) => (string | {
                [x: string]: boolean | undefined;
            } | undefined)[];
            onCellClick: (cellData: import("./interface").Cell) => void;
            onCellMouseEnter: (cellData: import("./interface").Cell) => void;
            onCellMouseLeave: (cellData: import("./interface").Cell) => void;
            getDateValue: typeof import("../_utils/date").getDateValue;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("cell-click" | "cell-mouse-enter")[], "cell-click" | "cell-mouse-enter", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            rows: {
                type: PropType<import("./interface").Cell[][]>;
                default: () => never[];
            };
            value: {
                type: PropType<Dayjs>;
            };
            disabledDate: {
                type: PropType<import("./interface").DisabledDate>;
            };
            isSameTime: {
                type: PropType<import("./interface").IsSameTime>;
                required: true;
            };
            mode: {
                type: PropType<Mode>;
            };
            rangeValues: {
                type: PropType<(Dayjs | undefined)[]>;
            };
            dateRender: {
                type: PropType<RenderFunc>;
            };
        }>> & Readonly<{
            "onCell-click"?: ((...args: any[]) => any) | undefined;
            "onCell-mouse-enter"?: ((...args: any[]) => any) | undefined;
        }>, {
            rows: import("./interface").Cell[][];
        }, {}, {
            RenderFunction: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                renderFunc: {
                    type: PropType<RenderFunc>;
                    required: true;
                };
            }>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                renderFunc: {
                    type: PropType<RenderFunc>;
                    required: true;
                };
            }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    YearPanel: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        headerValue: {
            type: PropType<Dayjs>;
            required: true;
        };
        headerOperations: {
            type: PropType<import("./interface").HeaderOperations>;
            default: () => {};
        };
        headerIcons: {
            type: PropType<import("./interface").HeaderIcons>;
            default: () => {};
        };
        value: {
            type: PropType<Dayjs>;
        };
        disabledDate: {
            type: PropType<import("./interface").DisabledDate>;
        };
        rangeValues: {
            type: PropType<(Dayjs | undefined)[]>;
        };
        dateRender: {
            type: PropType<RenderFunc>;
        };
    }>, {
        prefixCls: import("vue").ComputedRef<string>;
        pickerPrefixCls: string;
        headerTitle: import("vue").ComputedRef<string>;
        rows: import("vue").ComputedRef<{
            label: number;
            value: Dayjs;
            isPrev: boolean;
            isNext: boolean;
        }[][]>;
        isSameTime: import("./interface").IsSameTime;
        onCellClick: (cellData: import("./interface").Cell) => void;
        onCellMouseEnter: (cellData: import("./interface").Cell) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("select" | "cell-mouse-enter")[], "select" | "cell-mouse-enter", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        headerValue: {
            type: PropType<Dayjs>;
            required: true;
        };
        headerOperations: {
            type: PropType<import("./interface").HeaderOperations>;
            default: () => {};
        };
        headerIcons: {
            type: PropType<import("./interface").HeaderIcons>;
            default: () => {};
        };
        value: {
            type: PropType<Dayjs>;
        };
        disabledDate: {
            type: PropType<import("./interface").DisabledDate>;
        };
        rangeValues: {
            type: PropType<(Dayjs | undefined)[]>;
        };
        dateRender: {
            type: PropType<RenderFunc>;
        };
    }>> & Readonly<{
        onSelect?: ((...args: any[]) => any) | undefined;
        "onCell-mouse-enter"?: ((...args: any[]) => any) | undefined;
    }>, {
        headerOperations: import("./interface").HeaderOperations;
        headerIcons: import("./interface").HeaderIcons;
    }, {}, {
        PanelHeader: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            title: {
                type: StringConstructor;
                required: true;
            };
            mode: {
                type: PropType<Mode>;
                default: string;
            };
            value: {
                type: PropType<Dayjs>;
            };
            icons: {
                type: PropType<import("./interface").HeaderIcons>;
            };
            onPrev: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onSuperPrev: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onNext: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onSuperNext: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onLabelClick: {
                type: PropType<import("./panels/header").HeaderLabelClickFunc>;
            };
        }>, {
            showPrev: import("vue").ComputedRef<boolean>;
            showSuperPrev: import("vue").ComputedRef<boolean>;
            showNext: import("vue").ComputedRef<boolean>;
            showSuperNext: import("vue").ComputedRef<boolean>;
            year: import("vue").ComputedRef<string>;
            month: import("vue").ComputedRef<string>;
            getIconClassName: (show?: boolean | undefined) => (string | {
                [x: string]: boolean;
            })[];
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "label-click"[], "label-click", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            title: {
                type: StringConstructor;
                required: true;
            };
            mode: {
                type: PropType<Mode>;
                default: string;
            };
            value: {
                type: PropType<Dayjs>;
            };
            icons: {
                type: PropType<import("./interface").HeaderIcons>;
            };
            onPrev: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onSuperPrev: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onNext: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onSuperNext: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onLabelClick: {
                type: PropType<import("./panels/header").HeaderLabelClickFunc>;
            };
        }>> & Readonly<{
            "onLabel-click"?: ((...args: any[]) => any) | undefined;
        }>, {
            mode: Mode;
        }, {}, {
            IconLeft: any;
            IconRight: any;
            IconDoubleLeft: any;
            IconDoubleRight: any;
            RenderFunction: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                renderFunc: {
                    type: PropType<RenderFunc>;
                    required: true;
                };
            }>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                renderFunc: {
                    type: PropType<RenderFunc>;
                    required: true;
                };
            }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        PanelBody: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            rows: {
                type: PropType<import("./interface").Cell[][]>;
                default: () => never[];
            };
            value: {
                type: PropType<Dayjs>;
            };
            disabledDate: {
                type: PropType<import("./interface").DisabledDate>;
            };
            isSameTime: {
                type: PropType<import("./interface").IsSameTime>;
                required: true;
            };
            mode: {
                type: PropType<Mode>;
            };
            rangeValues: {
                type: PropType<(Dayjs | undefined)[]>;
            };
            dateRender: {
                type: PropType<RenderFunc>;
            };
        }>, {
            isWeek: import("vue").ComputedRef<boolean>;
            getCellClassName: (cellData: import("./interface").Cell) => (string | {
                [x: string]: boolean | undefined;
            } | undefined)[];
            onCellClick: (cellData: import("./interface").Cell) => void;
            onCellMouseEnter: (cellData: import("./interface").Cell) => void;
            onCellMouseLeave: (cellData: import("./interface").Cell) => void;
            getDateValue: typeof import("../_utils/date").getDateValue;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("cell-click" | "cell-mouse-enter")[], "cell-click" | "cell-mouse-enter", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            rows: {
                type: PropType<import("./interface").Cell[][]>;
                default: () => never[];
            };
            value: {
                type: PropType<Dayjs>;
            };
            disabledDate: {
                type: PropType<import("./interface").DisabledDate>;
            };
            isSameTime: {
                type: PropType<import("./interface").IsSameTime>;
                required: true;
            };
            mode: {
                type: PropType<Mode>;
            };
            rangeValues: {
                type: PropType<(Dayjs | undefined)[]>;
            };
            dateRender: {
                type: PropType<RenderFunc>;
            };
        }>> & Readonly<{
            "onCell-click"?: ((...args: any[]) => any) | undefined;
            "onCell-mouse-enter"?: ((...args: any[]) => any) | undefined;
        }>, {
            rows: import("./interface").Cell[][];
        }, {}, {
            RenderFunction: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                renderFunc: {
                    type: PropType<RenderFunc>;
                    required: true;
                };
            }>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                renderFunc: {
                    type: PropType<RenderFunc>;
                    required: true;
                };
            }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    QuarterPanel: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        headerValue: {
            type: PropType<Dayjs>;
            required: true;
        };
        headerOperations: {
            type: PropType<import("./interface").HeaderOperations>;
            default: () => {};
        };
        headerIcons: {
            type: PropType<import("./interface").HeaderIcons>;
            default: () => {};
        };
        value: {
            type: PropType<Dayjs>;
        };
        disabledDate: {
            type: PropType<import("./interface").DisabledDate>;
        };
        rangeValues: {
            type: PropType<(Dayjs | undefined)[]>;
        };
        dateRender: {
            type: PropType<RenderFunc>;
        };
        onHeaderLabelClick: {
            type: PropType<import("./panels/header").HeaderLabelClickFunc>;
        };
    }>, {
        prefixCls: import("vue").ComputedRef<string>;
        pickerPrefixCls: string;
        headerTitle: import("vue").ComputedRef<string>;
        rows: import("vue").ComputedRef<import("./interface").Cell[][]>;
        isSameTime: import("./interface").IsSameTime;
        onCellClick: (cellData: import("./interface").Cell) => void;
        onCellMouseEnter: (cellData: import("./interface").Cell) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("select" | "cell-mouse-enter")[], "select" | "cell-mouse-enter", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        headerValue: {
            type: PropType<Dayjs>;
            required: true;
        };
        headerOperations: {
            type: PropType<import("./interface").HeaderOperations>;
            default: () => {};
        };
        headerIcons: {
            type: PropType<import("./interface").HeaderIcons>;
            default: () => {};
        };
        value: {
            type: PropType<Dayjs>;
        };
        disabledDate: {
            type: PropType<import("./interface").DisabledDate>;
        };
        rangeValues: {
            type: PropType<(Dayjs | undefined)[]>;
        };
        dateRender: {
            type: PropType<RenderFunc>;
        };
        onHeaderLabelClick: {
            type: PropType<import("./panels/header").HeaderLabelClickFunc>;
        };
    }>> & Readonly<{
        onSelect?: ((...args: any[]) => any) | undefined;
        "onCell-mouse-enter"?: ((...args: any[]) => any) | undefined;
    }>, {
        headerOperations: import("./interface").HeaderOperations;
        headerIcons: import("./interface").HeaderIcons;
    }, {}, {
        PanelHeader: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            title: {
                type: StringConstructor;
                required: true;
            };
            mode: {
                type: PropType<Mode>;
                default: string;
            };
            value: {
                type: PropType<Dayjs>;
            };
            icons: {
                type: PropType<import("./interface").HeaderIcons>;
            };
            onPrev: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onSuperPrev: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onNext: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onSuperNext: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onLabelClick: {
                type: PropType<import("./panels/header").HeaderLabelClickFunc>;
            };
        }>, {
            showPrev: import("vue").ComputedRef<boolean>;
            showSuperPrev: import("vue").ComputedRef<boolean>;
            showNext: import("vue").ComputedRef<boolean>;
            showSuperNext: import("vue").ComputedRef<boolean>;
            year: import("vue").ComputedRef<string>;
            month: import("vue").ComputedRef<string>;
            getIconClassName: (show?: boolean | undefined) => (string | {
                [x: string]: boolean;
            })[];
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "label-click"[], "label-click", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            title: {
                type: StringConstructor;
                required: true;
            };
            mode: {
                type: PropType<Mode>;
                default: string;
            };
            value: {
                type: PropType<Dayjs>;
            };
            icons: {
                type: PropType<import("./interface").HeaderIcons>;
            };
            onPrev: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onSuperPrev: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onNext: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onSuperNext: {
                type: PropType<(payload: MouseEvent) => void>;
            };
            onLabelClick: {
                type: PropType<import("./panels/header").HeaderLabelClickFunc>;
            };
        }>> & Readonly<{
            "onLabel-click"?: ((...args: any[]) => any) | undefined;
        }>, {
            mode: Mode;
        }, {}, {
            IconLeft: any;
            IconRight: any;
            IconDoubleLeft: any;
            IconDoubleRight: any;
            RenderFunction: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                renderFunc: {
                    type: PropType<RenderFunc>;
                    required: true;
                };
            }>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                renderFunc: {
                    type: PropType<RenderFunc>;
                    required: true;
                };
            }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        PanelBody: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            rows: {
                type: PropType<import("./interface").Cell[][]>;
                default: () => never[];
            };
            value: {
                type: PropType<Dayjs>;
            };
            disabledDate: {
                type: PropType<import("./interface").DisabledDate>;
            };
            isSameTime: {
                type: PropType<import("./interface").IsSameTime>;
                required: true;
            };
            mode: {
                type: PropType<Mode>;
            };
            rangeValues: {
                type: PropType<(Dayjs | undefined)[]>;
            };
            dateRender: {
                type: PropType<RenderFunc>;
            };
        }>, {
            isWeek: import("vue").ComputedRef<boolean>;
            getCellClassName: (cellData: import("./interface").Cell) => (string | {
                [x: string]: boolean | undefined;
            } | undefined)[];
            onCellClick: (cellData: import("./interface").Cell) => void;
            onCellMouseEnter: (cellData: import("./interface").Cell) => void;
            onCellMouseLeave: (cellData: import("./interface").Cell) => void;
            getDateValue: typeof import("../_utils/date").getDateValue;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("cell-click" | "cell-mouse-enter")[], "cell-click" | "cell-mouse-enter", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            rows: {
                type: PropType<import("./interface").Cell[][]>;
                default: () => never[];
            };
            value: {
                type: PropType<Dayjs>;
            };
            disabledDate: {
                type: PropType<import("./interface").DisabledDate>;
            };
            isSameTime: {
                type: PropType<import("./interface").IsSameTime>;
                required: true;
            };
            mode: {
                type: PropType<Mode>;
            };
            rangeValues: {
                type: PropType<(Dayjs | undefined)[]>;
            };
            dateRender: {
                type: PropType<RenderFunc>;
            };
        }>> & Readonly<{
            "onCell-click"?: ((...args: any[]) => any) | undefined;
            "onCell-mouse-enter"?: ((...args: any[]) => any) | undefined;
        }>, {
            rows: import("./interface").Cell[][];
        }, {}, {
            RenderFunction: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                renderFunc: {
                    type: PropType<RenderFunc>;
                    required: true;
                };
            }>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                renderFunc: {
                    type: PropType<RenderFunc>;
                    required: true;
                };
            }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
