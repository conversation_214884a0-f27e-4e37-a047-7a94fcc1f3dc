package interfaces

import (
	"context"
	"fmt"
	"time"
)

// BusinessError 业务错误
type BusinessError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e *BusinessError) Error() string {
	return e.Message
}

// NewBusinessError 创建业务错误
func NewBusinessError(code, message string) *BusinessError {
	return &BusinessError{
		Code:    code,
		Message: message,
	}
}

// NewBusinessErrorWithCode 创建业务错误（支持int类型的错误码）
func NewBusinessErrorWithCode(code int, message string) *BusinessError {
	return &BusinessError{
		Code:    fmt.Sprintf("%d", code),
		Message: message,
	}
}

// NewValidationError 创建验证错误
func NewValidationError(errors map[string]string) *BusinessError {
	return &BusinessError{
		Code:    ErrCodeValidation,
		Message: "参数验证失败",
		Details: formatValidationErrors(errors),
	}
}

// 错误代码常量
const (
	ErrCodeValidation = "VALIDATION_ERROR"
	ErrCodeNotFound   = "NOT_FOUND"
	ErrCodeDatabase   = "DATABASE_ERROR"
	ErrCodeExternal   = "EXTERNAL_ERROR"
	ErrCodeAuth       = "AUTH_ERROR"
	ErrCodePermission = "PERMISSION_ERROR"
)

// formatValidationErrors 格式化验证错误
func formatValidationErrors(errors map[string]string) string {
	if len(errors) == 0 {
		return ""
	}

	var result string
	for field, message := range errors {
		if result != "" {
			result += "; "
		}
		result += field + ": " + message
	}
	return result
}

// PaymentService 支付服务接口
type PaymentService interface {
	CreatePayment(ctx context.Context, req *CreatePaymentRequest) (*PaymentResponse, error)
	QueryPayment(ctx context.Context, orderNo string) (*PaymentResponse, error)
	CancelPayment(ctx context.Context, orderNo string) error
	ProcessCallback(ctx context.Context, channel string, data map[string]interface{}) error
	CreateRefund(ctx context.Context, req *CreateRefundRequest) (*RefundResponse, error)
	QueryRefund(ctx context.Context, refundNo string) (*RefundResponse, error)
	ProcessRefundCallback(ctx context.Context, channel string, data map[string]interface{}) error
}

// MerchantService 商户服务接口
type MerchantService interface {
	CreateMerchant(ctx context.Context, req *CreateMerchantRequest) (*MerchantResponse, error)
	GetMerchant(ctx context.Context, merchantID uint) (*MerchantResponse, error)
	UpdateMerchant(ctx context.Context, merchantID uint, req *UpdateMerchantRequest) error
	DeleteMerchant(ctx context.Context, merchantID uint) error
	ListMerchants(ctx context.Context, req *ListMerchantsRequest) (*ListMerchantsResponse, error)
	GetMerchantBalance(ctx context.Context, merchantID uint) (*MerchantBalanceResponse, error)
	FreezeBalance(ctx context.Context, merchantID uint, amount float64, reason, orderNo string) error
	UnfreezeBalance(ctx context.Context, merchantID uint, amount float64, reason, orderNo string) error
	DeductFrozenBalance(ctx context.Context, merchantID uint, amount float64, reason, orderNo string) error
	AddBalance(ctx context.Context, merchantID uint, amount float64, reason, orderNo string) error
}

// AdminService 管理服务接口
type AdminService interface {
	CreateAdminUser(ctx context.Context, req *CreateAdminUserRequest) (*AdminUserResponse, error)
	GetAdminUser(ctx context.Context, userID uint) (*AdminUserResponse, error)
	UpdateAdminUser(ctx context.Context, userID uint, req *UpdateAdminUserRequest) error
	DeleteAdminUser(ctx context.Context, userID uint) error
	ListAdminUsers(ctx context.Context, req *ListAdminUsersRequest) (*ListAdminUsersResponse, error)
	GetSystemConfig(ctx context.Context, key string) (*SystemConfigResponse, error)
	SetSystemConfig(ctx context.Context, req *SetSystemConfigRequest) error
	GetSystemStats(ctx context.Context, req *SystemStatsRequest) (*SystemStatsResponse, error)
}

// NotificationService 通知服务接口
type NotificationService interface {
	SendPaymentNotification(ctx context.Context, orderNo string, notifyType int) error
	SendEmail(ctx context.Context, req *SendEmailRequest) error
	SendSMS(ctx context.Context, req *SendSMSRequest) error
	CreateEmailTemplate(ctx context.Context, req *CreateEmailTemplateRequest) (*EmailTemplateResponse, error)
	CreateSMSTemplate(ctx context.Context, req *CreateSMSTemplateRequest) (*SMSTemplateResponse, error)
}

// SettlementService 结算服务接口
type SettlementService interface {
	CreateSettlement(ctx context.Context, req *CreateSettlementRequest) (*SettlementResponse, error)
	ProcessSettlement(ctx context.Context, settlementNo string) error
	CreateWithdraw(ctx context.Context, req *CreateWithdrawRequest) (*WithdrawResponse, error)
	ProcessWithdraw(ctx context.Context, withdrawNo string) error
	CreateReconciliation(ctx context.Context, req *CreateReconciliationRequest) (*ReconciliationResponse, error)
	ProcessReconciliation(ctx context.Context, reconciliationNo string) error
	GetSettlementStats(ctx context.Context, merchantID uint, startDate, endDate time.Time) (*SettlementStatsResponse, error)
	GetWithdrawStats(ctx context.Context, merchantID uint, startDate, endDate time.Time) (*WithdrawStatsResponse, error)
	GetReconciliationStats(ctx context.Context, merchantID uint, startDate, endDate time.Time) (*ReconciliationStatsResponse, error)
}

// 请求和响应结构体定义

// CreatePaymentRequest 创建支付请求
type CreatePaymentRequest struct {
	MerchantID uint    `json:"merchant_id"`
	OrderNo    string  `json:"order_no"`
	Amount     float64 `json:"amount"`
	Currency   string  `json:"currency"`
	Subject    string  `json:"subject"`
	Body       string  `json:"body"`
	Channel    string  `json:"channel"`
	NotifyURL  string  `json:"notify_url"`
	ReturnURL  string  `json:"return_url"`
	ExpireTime int     `json:"expire_time"`
	ClientIP   string  `json:"client_ip"`
	Extra      string  `json:"extra"`
}

// PaymentResponse 支付响应
type PaymentResponse struct {
	OrderNo     string    `json:"order_no"`
	MerchantID  uint      `json:"merchant_id"`
	Amount      float64   `json:"amount"`
	Currency    string    `json:"currency"`
	Subject     string    `json:"subject"`
	Channel     string    `json:"channel"`
	Status      int       `json:"status"`
	PayURL      string    `json:"pay_url,omitempty"`
	QRCode      string    `json:"qr_code,omitempty"`
	ChannelData string    `json:"channel_data,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	PaidAt      time.Time `json:"paid_at,omitempty"`
}

// CreateRefundRequest 创建退款请求
type CreateRefundRequest struct {
	MerchantID uint    `json:"merchant_id"`
	OrderNo    string  `json:"order_no"`
	RefundNo   string  `json:"refund_no"`
	Amount     float64 `json:"amount"`
	Reason     string  `json:"reason"`
	NotifyURL  string  `json:"notify_url"`
}

// RefundResponse 退款响应
type RefundResponse struct {
	RefundNo    string    `json:"refund_no"`
	OrderNo     string    `json:"order_no"`
	MerchantID  uint      `json:"merchant_id"`
	Amount      float64   `json:"amount"`
	Reason      string    `json:"reason"`
	Status      int       `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	ProcessedAt time.Time `json:"processed_at,omitempty"`
}

// RegisterMerchantRequest 商户注册请求
type RegisterMerchantRequest struct {
	Username        string `json:"username" validate:"required,max=50"`
	Email           string `json:"email" validate:"required,email,max=100"`
	Phone           string `json:"phone" validate:"required,max=20"`
	CompanyName     string `json:"company_name" validate:"required,max=100"`
	CompanyType     int    `json:"company_type" validate:"required"`
	BusinessLicense string `json:"business_license" validate:"required,max=100"`
	LegalPerson     string `json:"legal_person" validate:"required,max=50"`  // 法人姓名
	LegalIDCard     string `json:"legal_id_card" validate:"required,max=20"` // 法人身份证
	Province        string `json:"province" validate:"required,max=50"`      // 省份
	City            string `json:"city" validate:"required,max=50"`          // 城市
	District        string `json:"district" validate:"required,max=50"`      // 区县
	Address         string `json:"address" validate:"required,max=200"`      // 详细地址
	Industry        string `json:"industry" validate:"required,max=50"`      // 行业
	Website         string `json:"website" validate:"max=100"`               // 网站
	Description     string `json:"description" validate:"max=500"`           // 描述
	MerchantName    string `json:"merchant_name" validate:"required,max=100"`
	ContactName     string `json:"contact_name" validate:"required,max=50"`
	ContactPhone    string `json:"contact_phone" validate:"required,max=20"`
	ContactEmail    string `json:"contact_email" validate:"required,email,max=100"`
	BusinessType    int    `json:"business_type" validate:"required"`
	Password        string `json:"password" validate:"required,min=6,max=20"`
}

// LoginMerchantRequest 商户登录请求
type LoginMerchantRequest struct {
	Username   string `json:"username" validate:"required"` // 用户名（可以是商户号、邮箱、手机号）
	MerchantNo string `json:"merchant_no" validate:"required"`
	Password   string `json:"password" validate:"required"`
	LoginIP    string `json:"login_ip"` // 登录IP
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token     string            `json:"token"`
	ExpiresAt time.Time         `json:"expires_at"`
	UserID    uint              `json:"user_id"`  // 用户ID
	Username  string            `json:"username"` // 用户名
	Merchant  *MerchantResponse `json:"merchant"`
}

// AuditMerchantRequest 商户审核请求
type AuditMerchantRequest struct {
	Status int    `json:"status" validate:"required,oneof=1 2"` // 1-审核通过，2-审核拒绝
	Remark string `json:"remark" validate:"max=200"`
}

// CreateMerchantAppRequest 创建商户应用请求
type CreateMerchantAppRequest struct {
	AppName   string `json:"app_name" validate:"required,max=100"`
	AppType   int    `json:"app_type" validate:"required"`
	NotifyURL string `json:"notify_url" validate:"url,max=255"`
	ReturnURL string `json:"return_url" validate:"url,max=255"`
	Remark    string `json:"remark" validate:"max=200"`
}

// MerchantAppResponse 商户应用响应
type MerchantAppResponse struct {
	ID        uint      `json:"id"`
	AppID     string    `json:"app_id"`
	AppName   string    `json:"app_name"`
	AppType   int       `json:"app_type"`
	AppSecret string    `json:"app_secret"`
	NotifyURL string    `json:"notify_url"`
	ReturnURL string    `json:"return_url"`
	Status    int       `json:"status"`
	Remark    string    `json:"remark"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// UpdateMerchantAppRequest 更新商户应用请求
type UpdateMerchantAppRequest struct {
	AppName   string `json:"app_name" validate:"max=100"`
	NotifyURL string `json:"notify_url" validate:"url,max=255"`
	ReturnURL string `json:"return_url" validate:"url,max=255"`
	Status    int    `json:"status" validate:"oneof=0 1"`
	Remark    string `json:"remark" validate:"max=200"`
}

// CreateMerchantRequest 创建商户请求
type CreateMerchantRequest struct {
	MerchantNo   string `json:"merchant_no"`
	MerchantName string `json:"merchant_name"`
	ContactName  string `json:"contact_name"`
	ContactPhone string `json:"contact_phone"`
	ContactEmail string `json:"contact_email"`
	BusinessType int    `json:"business_type"`
	Status       int    `json:"status"`
}

// MerchantResponse 商户响应
type MerchantResponse struct {
	ID           uint      `json:"id"`
	MerchantNo   string    `json:"merchant_no"`
	MerchantName string    `json:"merchant_name"`
	ContactName  string    `json:"contact_name"`
	ContactPhone string    `json:"contact_phone"`
	ContactEmail string    `json:"contact_email"`
	BusinessType int       `json:"business_type"`
	Status       int       `json:"status"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// UpdateMerchantRequest 更新商户请求
type UpdateMerchantRequest struct {
	MerchantName string `json:"merchant_name"`
	ContactName  string `json:"contact_name"`
	ContactPhone string `json:"contact_phone"`
	ContactEmail string `json:"contact_email"`
	BusinessType int    `json:"business_type"`
	Status       int    `json:"status"`
}

// ListMerchantsRequest 商户列表请求
type ListMerchantsRequest struct {
	Page         int    `json:"page"`
	PageSize     int    `json:"page_size"`
	MerchantNo   string `json:"merchant_no"`
	MerchantName string `json:"merchant_name"`
	Status       int    `json:"status"`
}

// ListMerchantsResponse 商户列表响应
type ListMerchantsResponse struct {
	List  []*MerchantResponse `json:"list"`
	Total int64               `json:"total"`
	Page  int                 `json:"page"`
	Size  int                 `json:"size"`
}

// MerchantBalanceResponse 商户余额响应
type MerchantBalanceResponse struct {
	MerchantID       uint      `json:"merchant_id"`
	TotalBalance     float64   `json:"total_balance"`
	AvailableBalance float64   `json:"available_balance"`
	FrozenBalance    float64   `json:"frozen_balance"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// CreateAdminUserRequest 创建管理员用户请求
type CreateAdminUserRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
	RealName string `json:"real_name"`
	Email    string `json:"email"`
	Phone    string `json:"phone"`
	RoleID   uint   `json:"role_id"`
	Status   int    `json:"status"`
}

// AdminUserResponse 管理员用户响应
type AdminUserResponse struct {
	ID        uint      `json:"id"`
	Username  string    `json:"username"`
	RealName  string    `json:"real_name"`
	Email     string    `json:"email"`
	Phone     string    `json:"phone"`
	RoleID    uint      `json:"role_id"`
	Status    int       `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// UpdateAdminUserRequest 更新管理员用户请求
type UpdateAdminUserRequest struct {
	RealName string `json:"real_name"`
	Email    string `json:"email"`
	Phone    string `json:"phone"`
	RoleID   uint   `json:"role_id"`
	Status   int    `json:"status"`
}

// ListAdminUsersRequest 管理员用户列表请求
type ListAdminUsersRequest struct {
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	Username string `json:"username"`
	RealName string `json:"real_name"`
	Status   int    `json:"status"`
}

// ListAdminUsersResponse 管理员用户列表响应
type ListAdminUsersResponse struct {
	List  []*AdminUserResponse `json:"list"`
	Total int64                `json:"total"`
	Page  int                  `json:"page"`
	Size  int                  `json:"size"`
}

// SetSystemConfigRequest 设置系统配置请求
type SetSystemConfigRequest struct {
	Key         string `json:"key"`
	Value       string `json:"value"`
	Type        string `json:"type"`
	Description string `json:"description"`
}

// SystemStatsRequest 系统统计请求
type SystemStatsRequest struct {
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`
}

// SystemStatsResponse 系统统计响应
type SystemStatsResponse struct {
	TotalMerchants  int64   `json:"total_merchants"`
	TotalOrders     int64   `json:"total_orders"`
	TotalAmount     float64 `json:"total_amount"`
	SuccessOrders   int64   `json:"success_orders"`
	SuccessAmount   float64 `json:"success_amount"`
	SuccessRate     float64 `json:"success_rate"`
	TodayOrders     int64   `json:"today_orders"`
	TodayAmount     float64 `json:"today_amount"`
	YesterdayOrders int64   `json:"yesterday_orders"`
	YesterdayAmount float64 `json:"yesterday_amount"`
}

// AdminLoginRequest 管理员登录请求
type AdminLoginRequest struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
}

// AdminLoginResponse 管理员登录响应
type AdminLoginResponse struct {
	Token     string             `json:"token"`
	ExpiresAt time.Time          `json:"expires_at"`
	User      *AdminUserResponse `json:"user"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=6,max=20"`
}

// CreateAdminRequest 创建管理员请求（别名）
type CreateAdminRequest = CreateAdminUserRequest

// AdminResponse 管理员响应（别名）
type AdminResponse = AdminUserResponse

// UpdateAdminRequest 更新管理员请求（别名）
type UpdateAdminRequest = UpdateAdminUserRequest

// ListAdminsRequest 管理员列表请求（别名）
type ListAdminsRequest = ListAdminUsersRequest

// ListAdminsResponse 管理员列表响应（别名）
type ListAdminsResponse = ListAdminUsersResponse

// SendEmailRequest 发送邮件请求
type SendEmailRequest struct {
	To           []string               `json:"to"`
	ToEmail      string                 `json:"to_email"`
	ToName       string                 `json:"to_name"`
	Subject      string                 `json:"subject"`
	Content      string                 `json:"content"`
	Template     string                 `json:"template"`
	TemplateCode string                 `json:"template_code"`
	Data         map[string]interface{} `json:"data"`
	UserID       uint                   `json:"user_id"`
	UserType     int                    `json:"user_type"`
	IP           string                 `json:"ip"`
}

// SendSMSRequest 发送短信请求
type SendSMSRequest struct {
	Phone    string                 `json:"phone"`
	Content  string                 `json:"content"`
	Template string                 `json:"template"`
	Data     map[string]interface{} `json:"data"`
}

// CreateEmailTemplateRequest 创建邮件模板请求
type CreateEmailTemplateRequest struct {
	Name        string `json:"name"`
	Subject     string `json:"subject"`
	Content     string `json:"content"`
	Category    string `json:"category"`
	Description string `json:"description"`
}

// EmailTemplateResponse 邮件模板响应
type EmailTemplateResponse struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Subject     string    `json:"subject"`
	Content     string    `json:"content"`
	Category    string    `json:"category"`
	Description string    `json:"description"`
	Status      int       `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
}

// CreateSMSTemplateRequest 创建短信模板请求
type CreateSMSTemplateRequest struct {
	Name        string `json:"name"`
	Content     string `json:"content"`
	Category    string `json:"category"`
	Description string `json:"description"`
}

// SMSTemplateResponse 短信模板响应
type SMSTemplateResponse struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Content     string    `json:"content"`
	Category    string    `json:"category"`
	Description string    `json:"description"`
	Status      int       `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
}

// BalanceLogResponse 余额日志响应
type BalanceLogResponse struct {
	ID          uint      `json:"id"`
	MerchantID  uint      `json:"merchant_id"`
	Type        int       `json:"type"`
	Amount      int64     `json:"amount"`
	Balance     int64     `json:"balance"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
}

// ResetPasswordRequest 重置密码请求
type ResetPasswordRequest struct {
	UserID      uint   `json:"user_id"`
	OldPassword string `json:"old_password"`
	NewPassword string `json:"new_password"`
}

// AdminStatsResponse 管理员统计响应
type AdminStatsResponse struct {
	TotalUsers     int64 `json:"total_users"`
	TotalMerchants int64 `json:"total_merchants"`
	TotalOrders    int64 `json:"total_orders"`
	TotalAmount    int64 `json:"total_amount"`
}

// ListSystemConfigsRequest 系统配置列表请求
type ListSystemConfigsRequest struct {
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	Key      string `json:"key"`
	Category string `json:"category"`
}

// ListSystemConfigsResponse 系统配置列表响应
type ListSystemConfigsResponse struct {
	Total int64                   `json:"total"`
	Items []*SystemConfigResponse `json:"items"`
}

// SystemConfigResponse 系统配置响应
type SystemConfigResponse struct {
	ID          uint      `json:"id"`
	Key         string    `json:"key"`
	Value       string    `json:"value"`
	Type        string    `json:"type"`
	Category    string    `json:"category"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// CreateSystemLogRequest 创建系统日志请求
type CreateSystemLogRequest struct {
	Level   string `json:"level"`
	Message string `json:"message"`
	Context string `json:"context"`
}

// ListSystemLogsRequest 系统日志列表请求
type ListSystemLogsRequest struct {
	Page      int    `json:"page"`
	PageSize  int    `json:"page_size"`
	Level     string `json:"level"`
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
}

// ListSystemLogsResponse 系统日志列表响应
type ListSystemLogsResponse struct {
	Total int64                `json:"total"`
	Items []*SystemLogResponse `json:"items"`
}

// SystemLogResponse 系统日志响应
type SystemLogResponse struct {
	ID        uint      `json:"id"`
	Level     string    `json:"level"`
	Message   string    `json:"message"`
	Context   string    `json:"context"`
	CreatedAt time.Time `json:"created_at"`
}

// GetLogStatsRequest 日志统计请求
type GetLogStatsRequest struct {
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
}

// LogStatsResponse 日志统计响应
type LogStatsResponse struct {
	ErrorCount int64 `json:"error_count"`
	WarnCount  int64 `json:"warn_count"`
	InfoCount  int64 `json:"info_count"`
	DebugCount int64 `json:"debug_count"`
}

// CreatePaymentOrderRequest 创建支付订单请求
type CreatePaymentOrderRequest struct {
	MerchantID  uint      `json:"merchant_id"`
	AppID       string    `json:"app_id"`
	OutTradeNo  string    `json:"out_trade_no"`
	OutOrderNo  string    `json:"out_order_no"` // 商户订单号
	Subject     string    `json:"subject"`
	Body        string    `json:"body"` // 商品描述
	Amount      int64     `json:"amount"`
	Currency    string    `json:"currency"` // 货币类型
	Channel     string    `json:"channel"`  // 支付渠道
	ChannelCode string    `json:"channel_code"`
	PayMethod   string    `json:"pay_method"` // 支付方式
	NotifyURL   string    `json:"notify_url"`
	ReturnURL   string    `json:"return_url"`
	ClientIP    string    `json:"client_ip"`   // 客户端IP
	ExpireTime  time.Time `json:"expire_time"` // 过期时间
	Extra       string    `json:"extra"`       // 扩展参数
}

// PaymentOrderResponse 支付订单响应
type PaymentOrderResponse struct {
	ID          uint      `json:"id"`
	OrderNo     string    `json:"order_no"`
	OutTradeNo  string    `json:"out_trade_no"`
	Subject     string    `json:"subject"`
	Amount      int64     `json:"amount"`
	Status      int       `json:"status"`
	ChannelCode string    `json:"channel_code"`
	PayURL      string    `json:"pay_url"`
	QRCode      string    `json:"qr_code"`
	CreatedAt   time.Time `json:"created_at"`
}

// CreateRefundOrderRequest 创建退款订单请求
type CreateRefundOrderRequest struct {
	PaymentOrderID uint   `json:"payment_order_id"`
	OutRefundNo    string `json:"out_refund_no"`
	RefundAmount   int64  `json:"refund_amount"`
	RefundReason   string `json:"refund_reason"`
}

// RefundOrderResponse 退款订单响应
type RefundOrderResponse struct {
	ID           uint      `json:"id"`
	RefundNo     string    `json:"refund_no"`
	OutRefundNo  string    `json:"out_refund_no"`
	RefundAmount int64     `json:"refund_amount"`
	Status       int       `json:"status"`
	CreatedAt    time.Time `json:"created_at"`
}

// PaymentChannelResponse 支付渠道响应
type PaymentChannelResponse struct {
	ID          uint   `json:"id"`
	ChannelCode string `json:"channel_code"`
	ChannelName string `json:"channel_name"`
	Status      int    `json:"status"`
}

// CreateSettlementRequest 创建结算请求
type CreateSettlementRequest struct {
	MerchantID   uint   `json:"merchant_id"`
	SettleAmount int64  `json:"settle_amount"`
	SettleType   int    `json:"settle_type"`
	BankCode     string `json:"bank_code"`
	BankAccount  string `json:"bank_account"`
	BankName     string `json:"bank_name"`
	AccountName  string `json:"account_name"`
	Remark       string `json:"remark"`
}

// UpdateSystemStatsRequest 更新系统统计请求
type UpdateSystemStatsRequest struct {
	Date string `json:"date"`
}

// DashboardDataResponse 仪表板数据响应
type DashboardDataResponse struct {
	TotalUsers     int64 `json:"total_users"`
	TotalMerchants int64 `json:"total_merchants"`
	TotalOrders    int64 `json:"total_orders"`
	TotalAmount    int64 `json:"total_amount"`
	TodayOrders    int64 `json:"today_orders"`
	TodayAmount    int64 `json:"today_amount"`
}

// AddBlacklistIPRequest 添加黑名单IP请求
type AddBlacklistIPRequest struct {
	IP     string `json:"ip"`
	Reason string `json:"reason"`
}

// ListBlacklistIPsRequest 黑名单IP列表请求
type ListBlacklistIPsRequest struct {
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	IP       string `json:"ip"`
}

// ListBlacklistIPsResponse 黑名单IP列表响应
type ListBlacklistIPsResponse struct {
	Total int64                  `json:"total"`
	Items []*BlacklistIPResponse `json:"items"`
}

// BlacklistIPResponse 黑名单IP响应
type BlacklistIPResponse struct {
	ID        uint      `json:"id"`
	IP        string    `json:"ip"`
	Reason    string    `json:"reason"`
	CreatedAt time.Time `json:"created_at"`
}

// SettlementResponse 结算响应
type SettlementResponse struct {
	ID             uint      `json:"id"`
	MerchantID     uint      `json:"merchant_id"`
	SettlementNo   string    `json:"settlement_no"`
	Amount         float64   `json:"amount"`
	Fee            float64   `json:"fee"`
	ActualAmount   float64   `json:"actual_amount"`
	Status         int       `json:"status"`
	SettlementDate time.Time `json:"settlement_date"`
	BankAccount    string    `json:"bank_account"`
	BankName       string    `json:"bank_name"`
	AccountName    string    `json:"account_name"`
	CreatedAt      time.Time `json:"created_at"`
	ProcessedAt    time.Time `json:"processed_at,omitempty"`
}

// CreateWithdrawRequest 创建提现请求
type CreateWithdrawRequest struct {
	MerchantID   uint    `json:"merchant_id"`
	WithdrawNo   string  `json:"withdraw_no"`
	Amount       float64 `json:"amount"`
	Fee          float64 `json:"fee"`
	ActualAmount float64 `json:"actual_amount"`
	BankAccount  string  `json:"bank_account"`
	BankName     string  `json:"bank_name"`
	AccountName  string  `json:"account_name"`
	Remark       string  `json:"remark"`
}

// WithdrawResponse 提现响应
type WithdrawResponse struct {
	ID           uint      `json:"id"`
	MerchantID   uint      `json:"merchant_id"`
	WithdrawNo   string    `json:"withdraw_no"`
	Amount       float64   `json:"amount"`
	Fee          float64   `json:"fee"`
	FeeAmount    float64   `json:"fee_amount"` // 手续费金额（别名）
	ActualAmount float64   `json:"actual_amount"`
	Status       int       `json:"status"`
	BankAccount  string    `json:"bank_account"`
	BankName     string    `json:"bank_name"`
	AccountName  string    `json:"account_name"`
	Remark       string    `json:"remark"`
	CreatedAt    time.Time `json:"created_at"`
	ProcessedAt  time.Time `json:"processed_at,omitempty"`
}

// CreateReconciliationRequest 创建对账请求
type CreateReconciliationRequest struct {
	MerchantID         uint      `json:"merchant_id"`
	ReconciliationNo   string    `json:"reconciliation_no"`
	Channel            string    `json:"channel"`
	ReconciliationDate time.Time `json:"reconciliation_date"`
	TotalCount         int       `json:"total_count"`
	TotalAmount        float64   `json:"total_amount"`
	PlatformAmount     float64   `json:"platform_amount"` // 平台金额
	ChannelAmount      float64   `json:"channel_amount"`  // 渠道金额
	SuccessCount       int       `json:"success_count"`
	SuccessAmount      float64   `json:"success_amount"`
	DifferenceAmount   float64   `json:"difference_amount"`
}

// ReconciliationResponse 对账响应
type ReconciliationResponse struct {
	ID                 uint      `json:"id"`
	MerchantID         uint      `json:"merchant_id"`
	ReconciliationNo   string    `json:"reconciliation_no"`
	Channel            string    `json:"channel"`
	ReconciliationDate time.Time `json:"reconciliation_date"`
	TotalCount         int       `json:"total_count"`
	TotalAmount        float64   `json:"total_amount"`
	SuccessCount       int       `json:"success_count"`
	SuccessAmount      float64   `json:"success_amount"`
	DifferenceAmount   float64   `json:"difference_amount"`
	Status             int       `json:"status"`
	CreatedAt          time.Time `json:"created_at"`
	ProcessedAt        time.Time `json:"processed_at,omitempty"`
}

// SettlementStatsResponse 结算统计响应
type SettlementStatsResponse struct {
	TotalCount        int64   `json:"total_count"`
	TotalAmount       float64 `json:"total_amount"`
	TotalFee          float64 `json:"total_fee"`
	TotalActualAmount float64 `json:"total_actual_amount"`
	PendingCount      int64   `json:"pending_count"`
	PendingAmount     float64 `json:"pending_amount"`
	ProcessedCount    int64   `json:"processed_count"`
	ProcessedAmount   float64 `json:"processed_amount"`
	FailedCount       int64   `json:"failed_count"`
	FailedAmount      float64 `json:"failed_amount"`
}

// WithdrawStatsResponse 提现统计响应
type WithdrawStatsResponse struct {
	TotalCount        int64   `json:"total_count"`
	TotalAmount       float64 `json:"total_amount"`
	TotalFee          float64 `json:"total_fee"`
	TotalActualAmount float64 `json:"total_actual_amount"`
	PendingCount      int64   `json:"pending_count"`
	PendingAmount     float64 `json:"pending_amount"`
	ProcessedCount    int64   `json:"processed_count"`
	ProcessedAmount   float64 `json:"processed_amount"`
	FailedCount       int64   `json:"failed_count"`
	FailedAmount      float64 `json:"failed_amount"`
}

// ReconciliationStatsResponse 对账统计响应
type ReconciliationStatsResponse struct {
	TotalCount       int64   `json:"total_count"`
	TotalAmount      float64 `json:"total_amount"`
	SuccessCount     int64   `json:"success_count"`
	SuccessAmount    float64 `json:"success_amount"`
	DifferenceCount  int64   `json:"difference_count"`
	DifferenceAmount float64 `json:"difference_amount"`
	ProcessedCount   int64   `json:"processed_count"`
	PendingCount     int64   `json:"pending_count"`
}
