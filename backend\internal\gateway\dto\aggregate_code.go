package dto

import (
	"time"
)

// CreateAggregateCodeRequest 创建聚合码请求
type CreateAggregateCodeRequest struct {
	MerchantID        uint     `json:"merchant_id" validate:"required" comment:"商户ID"`
	AppID             string   `json:"app_id" validate:"required,max=32" comment:"应用ID"`
	Subject           string   `json:"subject" validate:"required,max=200" comment:"商品标题"`
	Body              string   `json:"body" validate:"max=500" comment:"商品描述"`
	Amount            int64    `json:"amount" validate:"required,min=1" comment:"支付金额（分）"`
	Currency          string   `json:"currency" validate:"max=3" comment:"货币类型，默认CNY"`
	SupportedChannels []string `json:"supported_channels" validate:"required,min=1" comment:"支持的支付渠道"`
	BrandConfig       *BrandConfig `json:"brand_config,omitempty" comment:"品牌定制配置"`
	NotifyURL         string   `json:"notify_url" validate:"max=255,url" comment:"异步通知地址"`
	ReturnURL         string   `json:"return_url" validate:"max=255,url" comment:"同步跳转地址"`
	ExpireMinutes     int      `json:"expire_minutes" validate:"min=1,max=1440" comment:"过期时间（分钟），默认30分钟"`
	ExtraData         string   `json:"extra_data,omitempty" comment:"扩展数据，JSON格式"`
	Remark            string   `json:"remark" validate:"max=200" comment:"备注"`
}

// BrandConfig 品牌定制配置
type BrandConfig struct {
	Title       string `json:"title,omitempty" comment:"页面标题"`
	Logo        string `json:"logo,omitempty" comment:"商户Logo URL"`
	ThemeColor  string `json:"theme_color,omitempty" comment:"主题颜色，如#1890ff"`
	BackgroundColor string `json:"background_color,omitempty" comment:"背景颜色"`
	Description string `json:"description,omitempty" comment:"页面描述"`
	ContactInfo string `json:"contact_info,omitempty" comment:"联系方式"`
	CustomCSS   string `json:"custom_css,omitempty" comment:"自定义CSS样式"`
}

// CreateAggregateCodeResponse 创建聚合码响应
type CreateAggregateCodeResponse struct {
	CodeID    string `json:"code_id" comment:"聚合码ID"`
	PayURL    string `json:"pay_url" comment:"支付页面URL"`
	QRCodeURL string `json:"qr_code_url,omitempty" comment:"二维码图片URL"`
	ExpireTime time.Time `json:"expire_time" comment:"过期时间"`
}

// GetAggregateCodeRequest 获取聚合码信息请求
type GetAggregateCodeRequest struct {
	CodeID string `json:"code_id" validate:"required" comment:"聚合码ID"`
}

// GetAggregateCodeResponse 获取聚合码信息响应
type GetAggregateCodeResponse struct {
	CodeID            string       `json:"code_id" comment:"聚合码ID"`
	MerchantID        uint         `json:"merchant_id" comment:"商户ID"`
	AppID             string       `json:"app_id" comment:"应用ID"`
	Subject           string       `json:"subject" comment:"商品标题"`
	Body              string       `json:"body" comment:"商品描述"`
	Amount            int64        `json:"amount" comment:"支付金额（分）"`
	Currency          string       `json:"currency" comment:"货币类型"`
	SupportedChannels []string     `json:"supported_channels" comment:"支持的支付渠道"`
	BrandConfig       *BrandConfig `json:"brand_config,omitempty" comment:"品牌定制配置"`
	PayURL            string       `json:"pay_url" comment:"支付页面URL"`
	QRCodeURL         string       `json:"qr_code_url,omitempty" comment:"二维码图片URL"`
	Status            int          `json:"status" comment:"状态"`
	ExpireTime        time.Time    `json:"expire_time" comment:"过期时间"`
	ScanCount         int          `json:"scan_count" comment:"扫码次数"`
	PayCount          int          `json:"pay_count" comment:"支付成功次数"`
	TotalAmount       int64        `json:"total_amount" comment:"累计支付金额（分）"`
	CreatedAt         time.Time    `json:"created_at" comment:"创建时间"`
}

// PaymentRequest 发起支付请求
type PaymentRequest struct {
	CodeID     string `json:"code_id" validate:"required" comment:"聚合码ID"`
	Channel    string `json:"channel" validate:"required" comment:"支付渠道"`
	PayMethod  string `json:"pay_method" validate:"required" comment:"支付方式"`
	ClientIP   string `json:"client_ip" comment:"客户端IP"`
	UserAgent  string `json:"user_agent" comment:"用户代理"`
	OpenID     string `json:"open_id,omitempty" comment:"用户OpenID（微信支付需要）"`
	ExtraData  string `json:"extra_data,omitempty" comment:"扩展数据"`
}

// PaymentResponse 发起支付响应
type PaymentResponse struct {
	OrderNo       string                 `json:"order_no" comment:"订单号"`
	Channel       string                 `json:"channel" comment:"支付渠道"`
	PayMethod     string                 `json:"pay_method" comment:"支付方式"`
	Amount        int64                  `json:"amount" comment:"支付金额（分）"`
	PayData       map[string]interface{} `json:"pay_data" comment:"支付数据"`
	QRCodeURL     string                 `json:"qr_code_url,omitempty" comment:"支付二维码URL"`
	PayURL        string                 `json:"pay_url,omitempty" comment:"支付链接"`
	ExpireTime    time.Time              `json:"expire_time" comment:"订单过期时间"`
}

// SmartRouteRequest 智能路由请求
type SmartRouteRequest struct {
	CodeID    string `json:"code_id" validate:"required" comment:"聚合码ID"`
	UserAgent string `json:"user_agent" comment:"用户代理"`
	ClientIP  string `json:"client_ip" comment:"客户端IP"`
}

// SmartRouteResponse 智能路由响应
type SmartRouteResponse struct {
	RecommendedChannel string   `json:"recommended_channel" comment:"推荐的支付渠道"`
	AvailableChannels  []string `json:"available_channels" comment:"可用的支付渠道"`
	RouteReason        string   `json:"route_reason" comment:"路由原因"`
}

// PaymentPageData 支付页面数据
type PaymentPageData struct {
	CodeID            string       `json:"code_id" comment:"聚合码ID"`
	Subject           string       `json:"subject" comment:"商品标题"`
	Body              string       `json:"body" comment:"商品描述"`
	Amount            int64        `json:"amount" comment:"支付金额（分）"`
	AmountYuan        string       `json:"amount_yuan" comment:"支付金额（元）"`
	Currency          string       `json:"currency" comment:"货币类型"`
	AvailableChannels []ChannelInfo `json:"available_channels" comment:"可用支付渠道"`
	RecommendedChannel string      `json:"recommended_channel,omitempty" comment:"推荐支付渠道"`
	BrandConfig       *BrandConfig `json:"brand_config,omitempty" comment:"品牌定制配置"`
	ExpireTime        time.Time    `json:"expire_time" comment:"过期时间"`
	IsExpired         bool         `json:"is_expired" comment:"是否已过期"`
}

// ChannelInfo 支付渠道信息
type ChannelInfo struct {
	Code        string `json:"code" comment:"渠道代码"`
	Name        string `json:"name" comment:"渠道名称"`
	Logo        string `json:"logo" comment:"渠道Logo"`
	Description string `json:"description" comment:"渠道描述"`
	Available   bool   `json:"available" comment:"是否可用"`
	FeeRate     int    `json:"fee_rate" comment:"费率（万分之几）"`
}

// UpdateAggregateCodeRequest 更新聚合码请求
type UpdateAggregateCodeRequest struct {
	CodeID            string       `json:"code_id" validate:"required" comment:"聚合码ID"`
	Subject           string       `json:"subject,omitempty" validate:"max=200" comment:"商品标题"`
	Body              string       `json:"body,omitempty" validate:"max=500" comment:"商品描述"`
	SupportedChannels []string     `json:"supported_channels,omitempty" comment:"支持的支付渠道"`
	BrandConfig       *BrandConfig `json:"brand_config,omitempty" comment:"品牌定制配置"`
	NotifyURL         string       `json:"notify_url,omitempty" validate:"max=255,url" comment:"异步通知地址"`
	ReturnURL         string       `json:"return_url,omitempty" validate:"max=255,url" comment:"同步跳转地址"`
	Status            *int         `json:"status,omitempty" comment:"状态"`
	Remark            string       `json:"remark,omitempty" validate:"max=200" comment:"备注"`
}

// ListAggregateCodesRequest 聚合码列表请求
type ListAggregateCodesRequest struct {
	MerchantID uint   `json:"merchant_id,omitempty" comment:"商户ID"`
	AppID      string `json:"app_id,omitempty" comment:"应用ID"`
	Status     *int   `json:"status,omitempty" comment:"状态"`
	Page       int    `json:"page" validate:"min=1" comment:"页码"`
	PageSize   int    `json:"page_size" validate:"min=1,max=100" comment:"每页数量"`
}

// ListAggregateCodesResponse 聚合码列表响应
type ListAggregateCodesResponse struct {
	List     []GetAggregateCodeResponse `json:"list" comment:"聚合码列表"`
	Total    int64                      `json:"total" comment:"总数"`
	Page     int                        `json:"page" comment:"当前页码"`
	PageSize int                        `json:"page_size" comment:"每页数量"`
}
