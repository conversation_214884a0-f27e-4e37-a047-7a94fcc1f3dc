import { CSSProperties, PropType } from 'vue';
import { TableData, TableFilterable, TableSortable } from './interface';
import { ClassName } from '../_utils/types';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    dataIndex: StringConstructor;
    title: StringConstructor;
    width: NumberConstructor;
    minWidth: NumberConstructor;
    align: {
        type: PropType<"left" | "right" | "center" | undefined>;
    };
    fixed: {
        type: PropType<"left" | "right" | undefined>;
    };
    ellipsis: {
        type: BooleanConstructor;
        default: boolean;
    };
    sortable: {
        type: PropType<TableSortable>;
        default: undefined;
    };
    filterable: {
        type: PropType<TableFilterable>;
        default: undefined;
    };
    cellClass: {
        type: PropType<ClassName>;
    };
    headerCellClass: {
        type: PropType<ClassName>;
    };
    bodyCellClass: {
        type: PropType<ClassName | ((record: TableData) => ClassName)>;
    };
    summaryCellClass: {
        type: PropType<ClassName | ((record: TableData) => ClassName)>;
    };
    cellStyle: {
        type: PropType<CSSProperties>;
    };
    headerCellStyle: {
        type: PropType<CSSProperties>;
    };
    bodyCellStyle: {
        type: PropType<CSSProperties | ((record: TableData) => CSSProperties)>;
    };
    summaryCellStyle: {
        type: PropType<CSSProperties | ((record: TableData) => CSSProperties)>;
    };
    index: {
        type: NumberConstructor;
    };
    tooltip: {
        type: (ObjectConstructor | BooleanConstructor)[];
        default: boolean;
    };
}>, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>[] | undefined, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    dataIndex: StringConstructor;
    title: StringConstructor;
    width: NumberConstructor;
    minWidth: NumberConstructor;
    align: {
        type: PropType<"left" | "right" | "center" | undefined>;
    };
    fixed: {
        type: PropType<"left" | "right" | undefined>;
    };
    ellipsis: {
        type: BooleanConstructor;
        default: boolean;
    };
    sortable: {
        type: PropType<TableSortable>;
        default: undefined;
    };
    filterable: {
        type: PropType<TableFilterable>;
        default: undefined;
    };
    cellClass: {
        type: PropType<ClassName>;
    };
    headerCellClass: {
        type: PropType<ClassName>;
    };
    bodyCellClass: {
        type: PropType<ClassName | ((record: TableData) => ClassName)>;
    };
    summaryCellClass: {
        type: PropType<ClassName | ((record: TableData) => ClassName)>;
    };
    cellStyle: {
        type: PropType<CSSProperties>;
    };
    headerCellStyle: {
        type: PropType<CSSProperties>;
    };
    bodyCellStyle: {
        type: PropType<CSSProperties | ((record: TableData) => CSSProperties)>;
    };
    summaryCellStyle: {
        type: PropType<CSSProperties | ((record: TableData) => CSSProperties)>;
    };
    index: {
        type: NumberConstructor;
    };
    tooltip: {
        type: (ObjectConstructor | BooleanConstructor)[];
        default: boolean;
    };
}>> & Readonly<{}>, {
    ellipsis: boolean;
    tooltip: boolean | Record<string, any>;
    sortable: TableSortable;
    filterable: TableFilterable;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
