import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    type: {
        type: PropType<"circle" | "line">;
        default: string;
    };
    size: {
        type: PropType<"mini" | "medium" | "large" | "small">;
    };
    percent: {
        type: NumberConstructor;
        default: number;
    };
    steps: {
        type: NumberConstructor;
        default: number;
    };
    animation: {
        type: BooleanConstructor;
        default: boolean;
    };
    strokeWidth: {
        type: NumberConstructor;
    };
    width: {
        type: (StringConstructor | NumberConstructor)[];
    };
    color: {
        type: (ObjectConstructor | StringConstructor)[];
    };
    trackColor: StringConstructor;
    bufferColor: {
        type: (ObjectConstructor | StringConstructor)[];
    };
    showText: {
        type: BooleanConstructor;
        default: boolean;
    };
    status: {
        type: PropType<"normal" | "success" | "warning" | "danger">;
    };
}>, {
    cls: import("vue").ComputedRef<string[]>;
    computedStatus: import("vue").ComputedRef<"normal" | "success" | "warning" | "danger">;
    mergedSize: import("vue").ComputedRef<"mini" | "medium" | "large" | "small">;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    type: {
        type: PropType<"circle" | "line">;
        default: string;
    };
    size: {
        type: PropType<"mini" | "medium" | "large" | "small">;
    };
    percent: {
        type: NumberConstructor;
        default: number;
    };
    steps: {
        type: NumberConstructor;
        default: number;
    };
    animation: {
        type: BooleanConstructor;
        default: boolean;
    };
    strokeWidth: {
        type: NumberConstructor;
    };
    width: {
        type: (StringConstructor | NumberConstructor)[];
    };
    color: {
        type: (ObjectConstructor | StringConstructor)[];
    };
    trackColor: StringConstructor;
    bufferColor: {
        type: (ObjectConstructor | StringConstructor)[];
    };
    showText: {
        type: BooleanConstructor;
        default: boolean;
    };
    status: {
        type: PropType<"normal" | "success" | "warning" | "danger">;
    };
}>> & Readonly<{}>, {
    type: "circle" | "line";
    animation: boolean;
    showText: boolean;
    steps: number;
    percent: number;
}, {}, {
    ProgressLine: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        percent: {
            type: NumberConstructor;
            default: number;
        };
        animation: {
            type: BooleanConstructor;
            default: boolean;
        };
        size: {
            type: PropType<"medium" | "large" | "small">;
            default: string;
        };
        strokeWidth: {
            type: NumberConstructor;
            default: number;
        };
        width: {
            type: (StringConstructor | NumberConstructor)[];
            default: string;
        };
        color: {
            type: (ObjectConstructor | StringConstructor)[];
            default: undefined;
        };
        trackColor: StringConstructor;
        formatText: {
            type: FunctionConstructor;
            default: undefined;
        };
        status: {
            type: PropType<"normal" | "success" | "warning" | "danger">;
        };
        showText: BooleanConstructor;
    }>, {
        prefixCls: string;
        style: import("vue").ComputedRef<{
            width: string | number;
            height: string;
            backgroundColor: string | undefined;
        }>;
        barStyle: import("vue").ComputedRef<{
            width: string;
        } | {
            backgroundImage: string;
            backgroundColor?: undefined;
            width: string;
        } | {
            backgroundColor: string;
            backgroundImage?: undefined;
            width: string;
        }>;
        text: import("vue").ComputedRef<string>;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        percent: {
            type: NumberConstructor;
            default: number;
        };
        animation: {
            type: BooleanConstructor;
            default: boolean;
        };
        size: {
            type: PropType<"medium" | "large" | "small">;
            default: string;
        };
        strokeWidth: {
            type: NumberConstructor;
            default: number;
        };
        width: {
            type: (StringConstructor | NumberConstructor)[];
            default: string;
        };
        color: {
            type: (ObjectConstructor | StringConstructor)[];
            default: undefined;
        };
        trackColor: StringConstructor;
        formatText: {
            type: FunctionConstructor;
            default: undefined;
        };
        status: {
            type: PropType<"normal" | "success" | "warning" | "danger">;
        };
        showText: BooleanConstructor;
    }>> & Readonly<{}>, {
        size: "medium" | "large" | "small";
        color: string | Record<string, any>;
        animation: boolean;
        strokeWidth: number;
        width: string | number;
        showText: boolean;
        percent: number;
        formatText: Function;
    }, {}, {
        IconExclamationCircleFill: any;
    }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    ProgressCircle: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        percent: {
            type: NumberConstructor;
            default: number;
        };
        type: {
            type: StringConstructor;
        };
        size: {
            type: PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        strokeWidth: {
            type: NumberConstructor;
        };
        width: {
            type: NumberConstructor;
            default: undefined;
        };
        color: {
            type: (ObjectConstructor | StringConstructor)[];
            default: undefined;
        };
        trackColor: StringConstructor;
        status: {
            type: StringConstructor;
            default: undefined;
        };
        showText: {
            type: BooleanConstructor;
            default: boolean;
        };
        pathStrokeWidth: {
            type: NumberConstructor;
        };
    }>, {
        prefixCls: string;
        isLinearGradient: boolean;
        radius: import("vue").ComputedRef<number>;
        text: import("vue").ComputedRef<string>;
        perimeter: import("vue").ComputedRef<number>;
        center: import("vue").ComputedRef<number>;
        mergedWidth: import("vue").ComputedRef<number>;
        mergedStrokeWidth: import("vue").ComputedRef<number>;
        mergedPathStrokeWidth: import("vue").ComputedRef<number>;
        linearGradientId: import("vue").ComputedRef<string>;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        percent: {
            type: NumberConstructor;
            default: number;
        };
        type: {
            type: StringConstructor;
        };
        size: {
            type: PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        strokeWidth: {
            type: NumberConstructor;
        };
        width: {
            type: NumberConstructor;
            default: undefined;
        };
        color: {
            type: (ObjectConstructor | StringConstructor)[];
            default: undefined;
        };
        trackColor: StringConstructor;
        status: {
            type: StringConstructor;
            default: undefined;
        };
        showText: {
            type: BooleanConstructor;
            default: boolean;
        };
        pathStrokeWidth: {
            type: NumberConstructor;
        };
    }>> & Readonly<{}>, {
        size: "mini" | "medium" | "large" | "small";
        color: string | Record<string, any>;
        width: number;
        status: string;
        showText: boolean;
        percent: number;
    }, {}, {
        IconExclamation: any;
        IconCheck: any;
    }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    ProgressSteps: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        steps: {
            type: NumberConstructor;
            default: number;
        };
        percent: {
            type: NumberConstructor;
            default: number;
        };
        size: {
            type: StringConstructor;
        };
        color: {
            type: (ObjectConstructor | StringConstructor)[];
            default: undefined;
        };
        trackColor: StringConstructor;
        strokeWidth: {
            type: NumberConstructor;
        };
        status: {
            type: StringConstructor;
            default: undefined;
        };
        showText: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>, {
        prefixCls: string;
        stepList: import("vue").ComputedRef<boolean[]>;
        mergedStrokeWidth: import("vue").ComputedRef<4 | 8>;
        text: import("vue").ComputedRef<string>;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        steps: {
            type: NumberConstructor;
            default: number;
        };
        percent: {
            type: NumberConstructor;
            default: number;
        };
        size: {
            type: StringConstructor;
        };
        color: {
            type: (ObjectConstructor | StringConstructor)[];
            default: undefined;
        };
        trackColor: StringConstructor;
        strokeWidth: {
            type: NumberConstructor;
        };
        status: {
            type: StringConstructor;
            default: undefined;
        };
        showText: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{}>, {
        color: string | Record<string, any>;
        status: string;
        showText: boolean;
        steps: number;
        percent: number;
    }, {}, {
        IconExclamationCircleFill: any;
    }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
