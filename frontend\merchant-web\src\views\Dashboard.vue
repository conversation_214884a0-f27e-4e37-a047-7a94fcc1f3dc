<template>
  <div class="dashboard">
    <h1>商户仪表盘</h1>
    
    <!-- 统计卡片 -->
    <a-row :gutter="16" class="stats-row">
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic title="今日交易额" :value="stats.todayAmount" suffix="元" />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic title="今日订单数" :value="stats.todayOrders" />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic title="账户余额" :value="stats.balance" suffix="元" />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic title="成功率" :value="stats.successRate" suffix="%" />
        </a-card>
      </a-col>
    </a-row>
    
    <!-- 图表区域 -->
    <a-row :gutter="16" class="charts-row">
      <a-col :span="12">
        <a-card title="交易趋势" class="chart-card">
          <div class="chart-placeholder">
            <p>交易趋势图表</p>
            <p>（集成图表库后显示）</p>
          </div>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="支付方式分布" class="chart-card">
          <div class="chart-placeholder">
            <p>支付方式分布图</p>
            <p>（集成图表库后显示）</p>
          </div>
        </a-card>
      </a-col>
    </a-row>
    
    <!-- 最近订单 -->
    <a-card title="最近订单" class="recent-orders">
      <a-table 
        :columns="orderColumns" 
        :data="recentOrders" 
        :pagination="false"
        size="small"
      >
        <template #status="{ record }">
          <a-tag 
            :color="getStatusColor(record.status)"
          >
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template #amount="{ record }">
          ¥{{ record.amount }}
        </template>
        <template #actions="{ record }">
          <a-button type="text" size="small" @click="viewOrder(record.id)">
            查看
          </a-button>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  IconGift,
  IconFile,
  IconUser,
  IconCheckCircle
} from '@arco-design/web-vue/es/icon'

// 统计数据
const stats = ref({
  todayAmount: 125680.50,
  todayOrders: 1234,
  balance: 98765.43,
  successRate: 98.5
})

// 订单表格列定义
const orderColumns = [
  {
    title: '订单号',
    dataIndex: 'orderNo',
    width: 200
  },
  {
    title: '金额',
    dataIndex: 'amount',
    slotName: 'amount',
    width: 120
  },
  {
    title: '支付方式',
    dataIndex: 'payMethod',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180
  },
  {
    title: '操作',
    slotName: 'actions',
    width: 100
  }
]

// 最近订单数据
const recentOrders = ref([
  {
    id: 1,
    orderNo: 'PAY202401010001',
    amount: 299.00,
    payMethod: '微信支付',
    status: 'success',
    createTime: '2024-01-01 10:30:00'
  },
  {
    id: 2,
    orderNo: 'PAY202401010002',
    amount: 158.50,
    payMethod: '支付宝',
    status: 'success',
    createTime: '2024-01-01 10:25:00'
  },
  {
    id: 3,
    orderNo: 'PAY202401010003',
    amount: 88.00,
    payMethod: '微信支付',
    status: 'pending',
    createTime: '2024-01-01 10:20:00'
  },
  {
    id: 4,
    orderNo: 'PAY202401010004',
    amount: 666.66,
    payMethod: '支付宝',
    status: 'failed',
    createTime: '2024-01-01 10:15:00'
  },
  {
    id: 5,
    orderNo: 'PAY202401010005',
    amount: 1299.00,
    payMethod: '银联支付',
    status: 'success',
    createTime: '2024-01-01 10:10:00'
  }
])

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    success: 'green',
    pending: 'orange',
    failed: 'red'
  }
  return colorMap[status] || 'gray'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    success: '成功',
    pending: '处理中',
    failed: '失败'
  }
  return textMap[status] || '未知'
}

// 查看订单详情
const viewOrder = (orderId: number) => {
  console.log('查看订单:', orderId)
  // TODO: 跳转到订单详情页
}

// 页面加载时获取数据
onMounted(() => {
  // TODO: 从API获取实际数据
  console.log('加载仪表盘数据')
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
}

.charts-row {
  margin-bottom: 24px;
}

.chart-card {
  height: 300px;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 4px;
  color: #999;
}

.recent-orders {
  margin-bottom: 24px;
}
</style>
