# 支付网关配置文件

# 服务配置
server:
  # 服务名称
  name: "payment-gateway"
  # 服务版本
  version: "1.0.0"
  # 运行模式: debug, release, test
  mode: "debug"
  # HTTP服务端口
  port: 8080
  # 读取超时时间(秒)
  read_timeout: 60
  # 写入超时时间(秒)
  write_timeout: 60

# 数据库配置
database:
  # 数据库类型
  type: "mysql"
  # 数据库主机
  host: "localhost"
  # 数据库端口
  port: 3306
  # 数据库用户名
  username: "root"
  # 数据库密码
  password: "123456"
  # 数据库名称
  database: "payment_gateway"
  # 字符集
  charset: "utf8mb4"
  # 时区
  timezone: "Asia/Shanghai"
  # 最大空闲连接数
  max_idle_conns: 10
  # 最大打开连接数
  max_open_conns: 100
  # 连接最大生存时间(分钟)
  conn_max_lifetime: 60
  # 是否启用日志
  log_mode: true

# Redis配置
redis:
  # Redis主机
  host: "localhost"
  # Redis端口
  port: 6379
  # Redis密码
  # password: ""
  # 数据库索引
  db: 0
  # 连接池大小
  pool_size: 10
  # 最小空闲连接数
  min_idle_conns: 5
  # 连接超时时间(秒)
  dial_timeout: 5
  # 读取超时时间(秒)
  read_timeout: 3
  # 写入超时时间(秒)
  write_timeout: 3

# RabbitMQ配置
rabbitmq:
  # 连接URL
  url: "amqp://admin:admin123@localhost:5672/"
  # 交换机配置
  exchanges:
    # 支付通知交换机
    payment_notify:
      name: "payment.notify"
      type: "direct"
      durable: true
    # 订单状态交换机
    order_status:
      name: "order.status"
      type: "topic"
      durable: true
  # 队列配置
  queues:
    # 支付成功通知队列
    payment_success:
      name: "payment.success"
      routing_key: "payment.success"
      durable: true
    # 支付失败通知队列
    payment_failed:
      name: "payment.failed"
      routing_key: "payment.failed"
      durable: true
    # 退款通知队列
    refund_notify:
      name: "refund.notify"
      routing_key: "refund.*"
      durable: true

# JWT配置
jwt:
  # JWT密钥
  secret: "payment-gateway-jwt-secret-key-2024"
  # Token过期时间(小时)
  expire_hours: 24
  # 刷新Token过期时间(小时)
  refresh_expire_hours: 168
  # 签发者
  issuer: "payment-gateway"

# 日志配置
log:
  # 日志级别: debug, info, warn, error
  level: "info"
  # 日志格式: json, console
  format: "json"
  # 日志文件路径
  filename: "logs/payment-gateway.log"
  # 单个日志文件最大大小(MB)
  max_size: 100
  # 保留的日志文件数量
  max_backups: 30
  # 日志文件保留天数
  max_age: 7
  # 是否压缩日志文件
  compress: true

# 支付渠道配置
payment:
  # 微信支付配置
  wechat:
    # 是否启用
    enabled: true
    # 应用ID
    app_id: ""
    # 商户号
    mch_id: ""
    # API密钥
    api_key: ""
    # API证书路径
    cert_path: ""
    # API私钥路径
    key_path: ""
    # 支付回调地址
    notify_url: "http://localhost:8080/api/v1/pay/notify/wechat"
    # 支付环境: sandbox, production
    env: "sandbox"
  
  # 支付宝配置
  alipay:
    # 是否启用
    enabled: true
    # 应用ID
    app_id: ""
    # 应用私钥
    private_key: ""
    # 支付宝公钥
    public_key: ""
    # 支付回调地址
    notify_url: "http://localhost:8080/api/v1/pay/notify/alipay"
    # 支付环境: sandbox, production
    env: "sandbox"

# 安全配置
security:
  # API签名配置
  signature:
    # 是否启用签名验证
    enabled: true
    # 签名算法: md5, sha256
    algorithm: "md5"
    # 签名有效期(秒)
    expire_seconds: 300
  
  # IP白名单配置
  ip_whitelist:
    # 是否启用IP白名单
    enabled: false
    # 允许的IP列表
    allowed_ips: []
  
  # 限流配置
  rate_limit:
    # 是否启用限流
    enabled: true
    # 每秒请求数限制
    requests_per_second: 100
    # 突发请求数限制
    burst: 200

# 业务配置
business:
  # 订单配置
  order:
    # 订单超时时间(分钟)
    timeout_minutes: 30
    # 订单号前缀
    prefix: "PAY"
    # 订单号长度
    length: 20
  
  # 退款配置
  refund:
    # 退款超时时间(分钟)
    timeout_minutes: 10
    # 退款号前缀
    prefix: "REF"
  
  # 结算配置
  settlement:
    # 结算周期: daily, weekly, monthly
    cycle: "daily"
    # 结算时间(24小时制)
    time: "02:00"
    # 最小结算金额(分)
    min_amount: 100

# 监控配置
monitor:
  # 是否启用监控
  enabled: true
  # 监控端口
  port: 9090
  # 监控路径
  path: "/metrics"

# 开发环境特殊配置
development:
  # 是否启用调试模式
  debug: true
  # 是否启用热重载
  hot_reload: true
  # 是否启用SQL日志
  sql_log: true
