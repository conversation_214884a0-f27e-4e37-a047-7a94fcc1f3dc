import{d as t,c as s,b as o,e as a,w as _,f as l,j as r,_ as c}from"./index-LzkvhPr6.js";const d={class:"login"},p=t({__name:"Login",setup(f){return(i,e)=>{const n=l("a-card");return r(),s("div",d,[e[1]||(e[1]=o("h2",null,"商户登录",-1)),a(n,null,{default:_(()=>e[0]||(e[0]=[o("p",null,"商户登录页面",-1),o("p",null,"功能开发中...",-1)])),_:1,__:[0]})])}}}),m=c(p,[["__scopeId","data-v-fcc40f1d"]]);export{m as default};
