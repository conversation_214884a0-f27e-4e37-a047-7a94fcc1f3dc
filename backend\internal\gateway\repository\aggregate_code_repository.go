package repository

import (
	"context"
	"time"

	"gorm.io/gorm"
	"payment-gateway/internal/gateway/model"
	"payment-gateway/internal/shared/errors"
)

// AggregateCodeRepository 聚合码仓储接口
type AggregateCodeRepository interface {
	// Create 创建聚合码
	Create(ctx context.Context, aggregateCode *model.AggregateCode) error
	
	// GetByCodeID 根据聚合码ID获取聚合码信息
	GetByCodeID(ctx context.Context, codeID string) (*model.AggregateCode, error)
	
	// GetByID 根据主键ID获取聚合码信息
	GetByID(ctx context.Context, id uint) (*model.AggregateCode, error)
	
	// Update 更新聚合码信息
	Update(ctx context.Context, aggregateCode *model.AggregateCode) error
	
	// UpdateStatus 更新聚合码状态
	UpdateStatus(ctx context.Context, codeID string, status int) error
	
	// UpdateScanCount 更新扫码次数
	UpdateScanCount(ctx context.Context, codeID string) error
	
	// UpdatePaymentStats 更新支付统计信息
	UpdatePaymentStats(ctx context.Context, codeID string, amount int64) error
	
	// List 获取聚合码列表
	List(ctx context.Context, merchantID uint, appID string, status *int, page, pageSize int) ([]*model.AggregateCode, int64, error)
	
	// Delete 删除聚合码（软删除）
	Delete(ctx context.Context, codeID string) error
	
	// GetExpiredCodes 获取过期的聚合码
	GetExpiredCodes(ctx context.Context, limit int) ([]*model.AggregateCode, error)
	
	// BatchUpdateExpiredStatus 批量更新过期状态
	BatchUpdateExpiredStatus(ctx context.Context, codeIDs []string) error
}

// aggregateCodeRepository 聚合码仓储实现
type aggregateCodeRepository struct {
	db *gorm.DB
}

// NewAggregateCodeRepository 创建聚合码仓储实例
func NewAggregateCodeRepository(db *gorm.DB) AggregateCodeRepository {
	return &aggregateCodeRepository{
		db: db,
	}
}

// Create 创建聚合码
func (r *aggregateCodeRepository) Create(ctx context.Context, aggregateCode *model.AggregateCode) error {
	if err := r.db.WithContext(ctx).Create(aggregateCode).Error; err != nil {
		return errors.NewDatabaseError("创建聚合码失败", err.Error())
	}
	return nil
}

// GetByCodeID 根据聚合码ID获取聚合码信息
func (r *aggregateCodeRepository) GetByCodeID(ctx context.Context, codeID string) (*model.AggregateCode, error) {
	var aggregateCode model.AggregateCode
	err := r.db.WithContext(ctx).Where("code_id = ?", codeID).First(&aggregateCode).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.NewNotFoundError("聚合码不存在")
		}
		return nil, errors.NewDatabaseError("获取聚合码失败", err.Error())
	}
	return &aggregateCode, nil
}

// GetByID 根据主键ID获取聚合码信息
func (r *aggregateCodeRepository) GetByID(ctx context.Context, id uint) (*model.AggregateCode, error) {
	var aggregateCode model.AggregateCode
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&aggregateCode).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.NewNotFoundError("聚合码不存在")
		}
		return nil, errors.NewDatabaseError("获取聚合码失败", err.Error())
	}
	return &aggregateCode, nil
}

// Update 更新聚合码信息
func (r *aggregateCodeRepository) Update(ctx context.Context, aggregateCode *model.AggregateCode) error {
	if err := r.db.WithContext(ctx).Save(aggregateCode).Error; err != nil {
		return errors.NewDatabaseError("更新聚合码失败", err.Error())
	}
	return nil
}

// UpdateStatus 更新聚合码状态
func (r *aggregateCodeRepository) UpdateStatus(ctx context.Context, codeID string, status int) error {
	result := r.db.WithContext(ctx).Model(&model.AggregateCode{}).
		Where("code_id = ?", codeID).
		Update("status", status)
	
	if result.Error != nil {
		return errors.NewDatabaseError("更新聚合码状态失败", result.Error.Error())
	}
	
	if result.RowsAffected == 0 {
		return errors.NewNotFoundError("聚合码不存在")
	}
	
	return nil
}

// UpdateScanCount 更新扫码次数
func (r *aggregateCodeRepository) UpdateScanCount(ctx context.Context, codeID string) error {
	result := r.db.WithContext(ctx).Model(&model.AggregateCode{}).
		Where("code_id = ?", codeID).
		Updates(map[string]interface{}{
			"scan_count":   gorm.Expr("scan_count + 1"),
			"last_scan_at": time.Now(),
		})
	
	if result.Error != nil {
		return errors.NewDatabaseError("更新扫码次数失败", result.Error.Error())
	}
	
	return nil
}

// UpdatePaymentStats 更新支付统计信息
func (r *aggregateCodeRepository) UpdatePaymentStats(ctx context.Context, codeID string, amount int64) error {
	result := r.db.WithContext(ctx).Model(&model.AggregateCode{}).
		Where("code_id = ?", codeID).
		Updates(map[string]interface{}{
			"pay_count":    gorm.Expr("pay_count + 1"),
			"total_amount": gorm.Expr("total_amount + ?", amount),
			"last_pay_at":  time.Now(),
		})
	
	if result.Error != nil {
		return errors.NewDatabaseError("更新支付统计失败", result.Error.Error())
	}
	
	return nil
}

// List 获取聚合码列表
func (r *aggregateCodeRepository) List(ctx context.Context, merchantID uint, appID string, status *int, page, pageSize int) ([]*model.AggregateCode, int64, error) {
	var aggregateCodes []*model.AggregateCode
	var total int64
	
	query := r.db.WithContext(ctx).Model(&model.AggregateCode{})
	
	// 添加查询条件
	if merchantID > 0 {
		query = query.Where("merchant_id = ?", merchantID)
	}
	if appID != "" {
		query = query.Where("app_id = ?", appID)
	}
	if status != nil {
		query = query.Where("status = ?", *status)
	}
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, errors.NewDatabaseError("获取聚合码总数失败", err.Error())
	}
	
	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&aggregateCodes).Error; err != nil {
		return nil, 0, errors.NewDatabaseError("获取聚合码列表失败", err.Error())
	}
	
	return aggregateCodes, total, nil
}

// Delete 删除聚合码（软删除）
func (r *aggregateCodeRepository) Delete(ctx context.Context, codeID string) error {
	result := r.db.WithContext(ctx).Where("code_id = ?", codeID).Delete(&model.AggregateCode{})
	
	if result.Error != nil {
		return errors.NewDatabaseError("删除聚合码失败", result.Error.Error())
	}
	
	if result.RowsAffected == 0 {
		return errors.NewNotFoundError("聚合码不存在")
	}
	
	return nil
}

// GetExpiredCodes 获取过期的聚合码
func (r *aggregateCodeRepository) GetExpiredCodes(ctx context.Context, limit int) ([]*model.AggregateCode, error) {
	var aggregateCodes []*model.AggregateCode
	
	err := r.db.WithContext(ctx).
		Where("expire_time < ? AND status = ?", time.Now(), model.AggregateCodeStatusEnabled).
		Limit(limit).
		Find(&aggregateCodes).Error
	
	if err != nil {
		return nil, errors.NewDatabaseError("获取过期聚合码失败", err.Error())
	}
	
	return aggregateCodes, nil
}

// BatchUpdateExpiredStatus 批量更新过期状态
func (r *aggregateCodeRepository) BatchUpdateExpiredStatus(ctx context.Context, codeIDs []string) error {
	if len(codeIDs) == 0 {
		return nil
	}
	
	result := r.db.WithContext(ctx).Model(&model.AggregateCode{}).
		Where("code_id IN ?", codeIDs).
		Update("status", model.AggregateCodeStatusExpired)
	
	if result.Error != nil {
		return errors.NewDatabaseError("批量更新过期状态失败", result.Error.Error())
	}
	
	return nil
}
