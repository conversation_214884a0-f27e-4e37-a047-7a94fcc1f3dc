package service

import (
	"context"
	"crypto/md5"
	"fmt"
	"payment-gateway/internal/merchant/model"
	"payment-gateway/internal/merchant/repository"
	"payment-gateway/internal/shared/constants"
	"payment-gateway/internal/shared/errors"
	"payment-gateway/internal/shared/interfaces"
	"payment-gateway/internal/shared/utils"
	"payment-gateway/internal/shared/validator"
	"payment-gateway/pkg/logger"
	"strconv"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// MerchantService 商户服务接口
type MerchantService interface {
	// 商户注册和认证
	RegisterMerchant(ctx context.Context, req *interfaces.RegisterMerchantRequest) (*interfaces.MerchantResponse, error)
	LoginMerchant(ctx context.Context, req *interfaces.LoginMerchantRequest) (*interfaces.LoginResponse, error)
	GetMerchant(ctx context.Context, merchantID uint) (*interfaces.MerchantResponse, error)
	GetMerchantByNo(ctx context.Context, merchantNo string) (*interfaces.MerchantResponse, error)
	UpdateMerchant(ctx context.Context, merchantID uint, req *interfaces.UpdateMerchantRequest) (*interfaces.MerchantResponse, error)

	// 商户审核
	AuditMerchant(ctx context.Context, merchantID uint, req *interfaces.AuditMerchantRequest) error
	GetPendingAuditMerchants(ctx context.Context) ([]*interfaces.MerchantResponse, error)

	// 商户应用管理
	CreateMerchantApp(ctx context.Context, merchantID uint, req *interfaces.CreateMerchantAppRequest) (*interfaces.MerchantAppResponse, error)
	GetMerchantApp(ctx context.Context, appID string) (*interfaces.MerchantAppResponse, error)
	GetMerchantApps(ctx context.Context, merchantID uint) ([]*interfaces.MerchantAppResponse, error)
	UpdateMerchantApp(ctx context.Context, appID string, req *interfaces.UpdateMerchantAppRequest) (*interfaces.MerchantAppResponse, error)
	DeleteMerchantApp(ctx context.Context, appID string) error
	ResetAppSecret(ctx context.Context, appID string) (string, error)

	// 余额管理
	GetMerchantBalance(ctx context.Context, merchantID uint) (*interfaces.MerchantBalanceResponse, error)
	UpdateBalance(ctx context.Context, merchantID uint, changeType int, amount int64, orderNo, description string) error
	GetBalanceLogs(ctx context.Context, merchantID uint, condition *repository.BalanceLogQueryCondition) ([]*interfaces.BalanceLogResponse, int64, error)

	// 查询操作
	QueryMerchants(ctx context.Context, condition *repository.MerchantQueryCondition) ([]*interfaces.MerchantResponse, int64, error)
	GetMerchantStats(ctx context.Context) (*repository.MerchantStats, error)

	// 状态管理
	UpdateMerchantStatus(ctx context.Context, merchantID uint, status int) error
	FreezeMerchant(ctx context.Context, merchantID uint) error
	UnfreezeMerchant(ctx context.Context, merchantID uint) error
}

// merchantService 商户服务实现
type merchantService struct {
	merchantRepo        repository.MerchantRepository
	merchantAppRepo     repository.MerchantAppRepository
	merchantBalanceRepo repository.MerchantBalanceRepository
	balanceLogRepo      repository.BalanceLogRepository
	userRepo            repository.UserRepository
	db                  *gorm.DB
}

// NewMerchantService 创建商户服务
func NewMerchantService(
	merchantRepo repository.MerchantRepository,
	merchantAppRepo repository.MerchantAppRepository,
	merchantBalanceRepo repository.MerchantBalanceRepository,
	balanceLogRepo repository.BalanceLogRepository,
	userRepo repository.UserRepository,
	db *gorm.DB,
) MerchantService {
	return &merchantService{
		merchantRepo:        merchantRepo,
		merchantAppRepo:     merchantAppRepo,
		merchantBalanceRepo: merchantBalanceRepo,
		balanceLogRepo:      balanceLogRepo,
		userRepo:            userRepo,
		db:                  db,
	}
}

// RegisterMerchant 商户注册
func (s *merchantService) RegisterMerchant(ctx context.Context, req *interfaces.RegisterMerchantRequest) (*interfaces.MerchantResponse, error) {
	// 1. 参数验证
	if err := s.validateRegisterMerchantRequest(req); err != nil {
		return nil, err
	}

	// 2. 检查用户名、邮箱、手机号是否已存在
	if s.userRepo.CheckUsernameExists(ctx, req.Username) {
		return nil, errors.NewBusinessError(constants.ErrCodeUserExists, "用户名已存在")
	}

	if req.Email != "" && s.userRepo.CheckEmailExists(ctx, req.Email) {
		return nil, errors.NewBusinessError(constants.ErrCodeUserExists, "邮箱已存在")
	}

	if s.userRepo.CheckPhoneExists(ctx, req.Phone) {
		return nil, errors.NewBusinessError(constants.ErrCodeUserExists, "手机号已存在")
	}

	// 3. 开启事务
	var merchant *model.Merchant
	err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 3.1 创建用户
		salt := utils.RandomString(16)
		password := s.hashPassword(req.Password, salt)

		user := &model.User{
			Username: req.Username,
			Email:    req.Email,
			Phone:    req.Phone,
			Password: password,
			Salt:     salt,
			Nickname: req.ContactName,
			RealName: req.ContactName,
			Status:   model.UserStatusNormal,
		}

		if err := s.userRepo.Create(ctx, user); err != nil {
			return err
		}

		// 3.2 生成商户号
		merchantNo := s.generateMerchantNo()

		// 3.3 创建商户
		merchant = &model.Merchant{
			MerchantNo:      merchantNo,
			UserID:          user.ID,
			CompanyName:     req.CompanyName,
			CompanyType:     req.CompanyType,
			BusinessLicense: req.BusinessLicense,
			LegalPerson:     req.LegalPerson,
			LegalIDCard:     req.LegalIDCard,
			ContactName:     req.ContactName,
			ContactPhone:    req.Phone,
			ContactEmail:    req.Email,
			Province:        req.Province,
			City:            req.City,
			District:        req.District,
			Address:         req.Address,
			Industry:        req.Industry,
			Website:         req.Website,
			Description:     req.Description,
			Status:          model.MerchantStatusPending,
			AuditStatus:     model.AuditStatusPending,
		}

		if err := s.merchantRepo.Create(ctx, merchant); err != nil {
			return err
		}

		// 3.4 创建商户余额记录
		balance := &model.MerchantBalance{
			MerchantID: merchant.ID,
		}

		return s.merchantBalanceRepo.Create(ctx, balance)
	})

	if err != nil {
		logger.Error("商户注册失败", zap.Error(err), zap.String("username", req.Username))
		return nil, errors.ErrInternalError
	}

	logger.Info("商户注册成功",
		zap.String("merchant_no", merchant.MerchantNo),
		zap.String("company_name", merchant.CompanyName))

	return s.convertToMerchantResponse(merchant), nil
}

// LoginMerchant 商户登录
func (s *merchantService) LoginMerchant(ctx context.Context, req *interfaces.LoginMerchantRequest) (*interfaces.LoginResponse, error) {
	// 1. 参数验证
	if err := s.validateLoginMerchantRequest(req); err != nil {
		return nil, err
	}

	// 2. 获取用户信息
	user, err := s.userRepo.GetByUsername(ctx, req.Username)
	if err != nil {
		return nil, errors.ErrUserNotFound
	}

	// 3. 验证密码
	if !s.verifyPassword(req.Password, user.Salt, user.Password) {
		return nil, errors.ErrPasswordIncorrect
	}

	// 4. 检查用户状态
	if user.Status != model.UserStatusNormal {
		return nil, errors.ErrUserDisabled
	}

	// 5. 获取商户信息
	merchant, err := s.merchantRepo.GetByUserID(ctx, user.ID)
	if err != nil {
		return nil, errors.ErrMerchantNotFound
	}

	// 6. 检查商户状态
	if merchant.Status == model.MerchantStatusFrozen {
		return nil, errors.NewBusinessError(constants.ErrCodeMerchantFrozen, "商户已被冻结")
	}

	// 7. 更新登录信息
	s.userRepo.UpdateLoginInfo(ctx, user.ID, req.LoginIP)

	// 8. 生成JWT Token（这里简化处理）
	token := s.generateToken(user.ID, merchant.ID)

	logger.Info("商户登录成功",
		zap.String("username", req.Username),
		zap.String("merchant_no", merchant.MerchantNo),
		zap.String("login_ip", req.LoginIP))

	return &interfaces.LoginResponse{
		Token:    token,
		UserID:   user.ID,
		Username: user.Username,
		Merchant: s.convertToMerchantResponse(merchant),
	}, nil
}

// GetMerchant 获取商户信息
func (s *merchantService) GetMerchant(ctx context.Context, merchantID uint) (*interfaces.MerchantResponse, error) {
	merchant, err := s.merchantRepo.GetByID(ctx, merchantID)
	if err != nil {
		return nil, errors.ErrMerchantNotFound
	}

	return s.convertToMerchantResponse(merchant), nil
}

// GetMerchantByNo 根据商户号获取商户信息
func (s *merchantService) GetMerchantByNo(ctx context.Context, merchantNo string) (*interfaces.MerchantResponse, error) {
	merchant, err := s.merchantRepo.GetByMerchantNo(ctx, merchantNo)
	if err != nil {
		return nil, errors.ErrMerchantNotFound
	}

	return s.convertToMerchantResponse(merchant), nil
}

// UpdateMerchant 更新商户信息
func (s *merchantService) UpdateMerchant(ctx context.Context, merchantID uint, req *interfaces.UpdateMerchantRequest) (*interfaces.MerchantResponse, error) {
	// 1. 参数验证
	if err := s.validateUpdateMerchantRequest(req); err != nil {
		return nil, err
	}

	// 2. 获取商户信息
	merchant, err := s.merchantRepo.GetByID(ctx, merchantID)
	if err != nil {
		return nil, errors.ErrMerchantNotFound
	}

	// 3. 更新商户信息
	if req.CompanyName != "" {
		merchant.CompanyName = req.CompanyName
	}
	if req.LegalPerson != "" {
		merchant.LegalPerson = req.LegalPerson
	}
	if req.LegalIDCard != "" {
		merchant.LegalIDCard = req.LegalIDCard
	}
	if req.ContactName != "" {
		merchant.ContactName = req.ContactName
	}
	if req.ContactPhone != "" {
		merchant.ContactPhone = req.ContactPhone
	}
	if req.ContactEmail != "" {
		merchant.ContactEmail = req.ContactEmail
	}
	if req.Province != "" {
		merchant.Province = req.Province
	}
	if req.City != "" {
		merchant.City = req.City
	}
	if req.District != "" {
		merchant.District = req.District
	}
	if req.Address != "" {
		merchant.Address = req.Address
	}
	if req.Industry != "" {
		merchant.Industry = req.Industry
	}
	if req.Website != "" {
		merchant.Website = req.Website
	}
	if req.Description != "" {
		merchant.Description = req.Description
	}
	if req.BankName != "" {
		merchant.BankName = req.BankName
	}
	if req.BankAccount != "" {
		merchant.BankAccount = req.BankAccount
	}
	if req.BankAccountName != "" {
		merchant.BankAccountName = req.BankAccountName
	}

	// 4. 保存更新
	if err := s.merchantRepo.Update(ctx, merchant); err != nil {
		logger.Error("更新商户信息失败", zap.Error(err), zap.Uint("merchant_id", merchantID))
		return nil, errors.ErrInternalError
	}

	logger.Info("更新商户信息成功", zap.Uint("merchant_id", merchantID))

	return s.convertToMerchantResponse(merchant), nil
}

// 辅助方法

// validateRegisterMerchantRequest 验证商户注册请求
func (s *merchantService) validateRegisterMerchantRequest(req *interfaces.RegisterMerchantRequest) error {
	v := validator.New()

	v.Required("username", req.Username).MinLength("username", req.Username, 3).MaxLength("username", req.Username, 20).
		Required("password", req.Password).Password("password", req.Password).
		Required("phone", req.Phone).Phone("phone", req.Phone).
		Required("company_name", req.CompanyName).MaxLength("company_name", req.CompanyName, 100).
		Required("contact_name", req.ContactName).MaxLength("contact_name", req.ContactName, 50)

	if req.Email != "" {
		v.Email("email", req.Email)
	}

	if v.HasErrors() {
		return v.ToBusinessError()
	}

	return nil
}

// validateLoginMerchantRequest 验证商户登录请求
func (s *merchantService) validateLoginMerchantRequest(req *interfaces.LoginMerchantRequest) error {
	v := validator.New()

	v.Required("username", req.Username).
		Required("password", req.Password)

	if v.HasErrors() {
		return v.ToBusinessError()
	}

	return nil
}

// validateUpdateMerchantRequest 验证更新商户请求
func (s *merchantService) validateUpdateMerchantRequest(req *interfaces.UpdateMerchantRequest) error {
	v := validator.New()

	if req.CompanyName != "" {
		v.MaxLength("company_name", req.CompanyName, 100)
	}
	if req.ContactName != "" {
		v.MaxLength("contact_name", req.ContactName, 50)
	}
	if req.ContactPhone != "" {
		v.Phone("contact_phone", req.ContactPhone)
	}
	if req.ContactEmail != "" {
		v.Email("contact_email", req.ContactEmail)
	}

	if v.HasErrors() {
		return v.ToBusinessError()
	}

	return nil
}

// generateMerchantNo 生成商户号
func (s *merchantService) generateMerchantNo() string {
	return "M" + time.Now().Format("20060102") + utils.RandomString(8)
}

// hashPassword 密码加密
func (s *merchantService) hashPassword(password, salt string) string {
	hash := md5.Sum([]byte(password + salt))
	return fmt.Sprintf("%x", hash)
}

// verifyPassword 验证密码
func (s *merchantService) verifyPassword(password, salt, hashedPassword string) bool {
	return s.hashPassword(password, salt) == hashedPassword
}

// generateToken 生成JWT Token（简化实现）
func (s *merchantService) generateToken(userID, merchantID uint) string {
	return "token_" + strconv.Itoa(int(userID)) + "_" + strconv.Itoa(int(merchantID)) + "_" + utils.RandomString(16)
}

// AuditMerchant 审核商户
func (s *merchantService) AuditMerchant(ctx context.Context, merchantID uint, req *interfaces.AuditMerchantRequest) error {
	// 1. 参数验证
	if req.AuditStatus != model.AuditStatusApproved && req.AuditStatus != model.AuditStatusRejected {
		return errors.NewBusinessError(constants.ErrCodeInvalidParam, "无效的审核状态")
	}

	// 2. 获取商户信息
	merchant, err := s.merchantRepo.GetByID(ctx, merchantID)
	if err != nil {
		return errors.ErrMerchantNotFound
	}

	// 3. 检查审核状态
	if merchant.AuditStatus != model.AuditStatusPending {
		return errors.NewBusinessError(constants.ErrCodeInvalidParam, "商户不在待审核状态")
	}

	// 4. 更新审核状态
	err = s.merchantRepo.UpdateAuditStatus(ctx, merchantID, req.AuditStatus, req.AuditRemark, req.AuditorID, req.AuditorName)
	if err != nil {
		logger.Error("更新商户审核状态失败", zap.Error(err), zap.Uint("merchant_id", merchantID))
		return errors.ErrInternalError
	}

	logger.Info("商户审核完成",
		zap.Uint("merchant_id", merchantID),
		zap.Int("audit_status", req.AuditStatus),
		zap.String("auditor_name", req.AuditorName))

	return nil
}

// GetPendingAuditMerchants 获取待审核商户
func (s *merchantService) GetPendingAuditMerchants(ctx context.Context) ([]*interfaces.MerchantResponse, error) {
	merchants, err := s.merchantRepo.GetPendingAuditMerchants(ctx)
	if err != nil {
		return nil, errors.ErrInternalError
	}

	var responses []*interfaces.MerchantResponse
	for _, merchant := range merchants {
		responses = append(responses, s.convertToMerchantResponse(merchant))
	}

	return responses, nil
}

// CreateMerchantApp 创建商户应用
func (s *merchantService) CreateMerchantApp(ctx context.Context, merchantID uint, req *interfaces.CreateMerchantAppRequest) (*interfaces.MerchantAppResponse, error) {
	// 1. 参数验证
	if err := s.validateCreateMerchantAppRequest(req); err != nil {
		return nil, err
	}

	// 2. 检查商户是否存在
	merchant, err := s.merchantRepo.GetByID(ctx, merchantID)
	if err != nil {
		return nil, errors.ErrMerchantNotFound
	}

	// 3. 检查商户状态
	if merchant.Status != model.MerchantStatusNormal {
		return nil, errors.NewBusinessError(constants.ErrCodeMerchantNotActive, "商户未激活")
	}

	// 4. 生成应用ID和密钥
	appID := s.generateAppID()
	appSecret := s.generateAppSecret()

	// 5. 检查应用ID是否重复
	for s.merchantAppRepo.CheckAppIDExists(ctx, appID) {
		appID = s.generateAppID()
	}

	// 6. 创建应用
	app := &model.MerchantApp{
		AppID:       appID,
		MerchantID:  merchantID,
		AppName:     req.AppName,
		AppType:     req.AppType,
		AppSecret:   appSecret,
		Description: req.Description,
		Status:      1, // 默认启用
	}

	if err := s.merchantAppRepo.Create(ctx, app); err != nil {
		logger.Error("创建商户应用失败", zap.Error(err), zap.Uint("merchant_id", merchantID))
		return nil, errors.ErrInternalError
	}

	logger.Info("创建商户应用成功",
		zap.String("app_id", appID),
		zap.Uint("merchant_id", merchantID),
		zap.String("app_name", req.AppName))

	return s.convertToMerchantAppResponse(app), nil
}

// GetMerchantApp 获取商户应用
func (s *merchantService) GetMerchantApp(ctx context.Context, appID string) (*interfaces.MerchantAppResponse, error) {
	app, err := s.merchantAppRepo.GetByAppID(ctx, appID)
	if err != nil {
		return nil, errors.NewBusinessError(constants.ErrCodeAppNotFound, "应用不存在")
	}

	return s.convertToMerchantAppResponse(app), nil
}

// GetMerchantApps 获取商户应用列表
func (s *merchantService) GetMerchantApps(ctx context.Context, merchantID uint) ([]*interfaces.MerchantAppResponse, error) {
	apps, err := s.merchantAppRepo.GetByMerchantID(ctx, merchantID)
	if err != nil {
		return nil, errors.ErrInternalError
	}

	var responses []*interfaces.MerchantAppResponse
	for _, app := range apps {
		responses = append(responses, s.convertToMerchantAppResponse(app))
	}

	return responses, nil
}

// UpdateMerchantApp 更新商户应用
func (s *merchantService) UpdateMerchantApp(ctx context.Context, appID string, req *interfaces.UpdateMerchantAppRequest) (*interfaces.MerchantAppResponse, error) {
	// 1. 获取应用信息
	app, err := s.merchantAppRepo.GetByAppID(ctx, appID)
	if err != nil {
		return nil, errors.NewBusinessError(constants.ErrCodeAppNotFound, "应用不存在")
	}

	// 2. 更新应用信息
	if req.AppName != "" {
		app.AppName = req.AppName
	}
	if req.Description != "" {
		app.Description = req.Description
	}
	if req.Status != nil {
		app.Status = *req.Status
	}

	// 3. 保存更新
	if err := s.merchantAppRepo.Update(ctx, app); err != nil {
		logger.Error("更新商户应用失败", zap.Error(err), zap.String("app_id", appID))
		return nil, errors.ErrInternalError
	}

	logger.Info("更新商户应用成功", zap.String("app_id", appID))

	return s.convertToMerchantAppResponse(app), nil
}

// DeleteMerchantApp 删除商户应用
func (s *merchantService) DeleteMerchantApp(ctx context.Context, appID string) error {
	// 1. 获取应用信息
	app, err := s.merchantAppRepo.GetByAppID(ctx, appID)
	if err != nil {
		return errors.NewBusinessError(constants.ErrCodeAppNotFound, "应用不存在")
	}

	// 2. 删除应用
	if err := s.merchantAppRepo.Delete(ctx, app.ID); err != nil {
		logger.Error("删除商户应用失败", zap.Error(err), zap.String("app_id", appID))
		return errors.ErrInternalError
	}

	logger.Info("删除商户应用成功", zap.String("app_id", appID))

	return nil
}

// ResetAppSecret 重置应用密钥
func (s *merchantService) ResetAppSecret(ctx context.Context, appID string) (string, error) {
	// 1. 获取应用信息
	app, err := s.merchantAppRepo.GetByAppID(ctx, appID)
	if err != nil {
		return "", errors.NewBusinessError(constants.ErrCodeAppNotFound, "应用不存在")
	}

	// 2. 生成新密钥
	newSecret := s.generateAppSecret()

	// 3. 更新密钥
	if err := s.merchantAppRepo.UpdateAppSecret(ctx, app.ID, newSecret); err != nil {
		logger.Error("重置应用密钥失败", zap.Error(err), zap.String("app_id", appID))
		return "", errors.ErrInternalError
	}

	logger.Info("重置应用密钥成功", zap.String("app_id", appID))

	return newSecret, nil
}

// convertToMerchantResponse 转换为商户响应
func (s *merchantService) convertToMerchantResponse(merchant *model.Merchant) *interfaces.MerchantResponse {
	return &interfaces.MerchantResponse{
		ID:              merchant.ID,
		MerchantNo:      merchant.MerchantNo,
		UserID:          merchant.UserID,
		CompanyName:     merchant.CompanyName,
		CompanyType:     merchant.CompanyType,
		BusinessLicense: merchant.BusinessLicense,
		LegalPerson:     merchant.LegalPerson,
		LegalIDCard:     merchant.LegalIDCard,
		ContactName:     merchant.ContactName,
		ContactPhone:    merchant.ContactPhone,
		ContactEmail:    merchant.ContactEmail,
		Province:        merchant.Province,
		City:            merchant.City,
		District:        merchant.District,
		Address:         merchant.Address,
		Industry:        merchant.Industry,
		Website:         merchant.Website,
		Logo:            merchant.Logo,
		Description:     merchant.Description,
		Status:          merchant.Status,
		AuditStatus:     merchant.AuditStatus,
		AuditRemark:     merchant.AuditRemark,
		AuditTime:       merchant.AuditTime,
		AuditorName:     merchant.AuditorName,
		SettleType:      merchant.SettleType,
		SettleRate:      merchant.SettleRate,
		MinSettleAmount: merchant.MinSettleAmount,
		MaxSettleAmount: merchant.MaxSettleAmount,
		BankName:        merchant.BankName,
		BankAccount:     merchant.BankAccount,
		BankAccountName: merchant.BankAccountName,
		CreatedAt:       merchant.CreatedAt,
		UpdatedAt:       merchant.UpdatedAt,
	}
}

// GetMerchantBalance 获取商户余额
func (s *merchantService) GetMerchantBalance(ctx context.Context, merchantID uint) (*interfaces.MerchantBalanceResponse, error) {
	balance, err := s.merchantBalanceRepo.GetByMerchantID(ctx, merchantID)
	if err != nil {
		return nil, errors.ErrInternalError
	}

	return &interfaces.MerchantBalanceResponse{
		MerchantID:       balance.MerchantID,
		AvailableBalance: balance.AvailableBalance,
		FrozenBalance:    balance.FrozenBalance,
		TotalIncome:      balance.TotalIncome,
		TotalExpense:     balance.TotalExpense,
		TodayIncome:      balance.TodayIncome,
		TodayExpense:     balance.TodayExpense,
		UpdatedAt:        balance.UpdatedAt,
	}, nil
}

// UpdateBalance 更新商户余额
func (s *merchantService) UpdateBalance(ctx context.Context, merchantID uint, changeType int, amount int64, orderNo, description string) error {
	return s.merchantBalanceRepo.UpdateBalance(ctx, merchantID, changeType, amount, orderNo, description)
}

// GetBalanceLogs 获取余额变动日志
func (s *merchantService) GetBalanceLogs(ctx context.Context, merchantID uint, condition *repository.BalanceLogQueryCondition) ([]*interfaces.BalanceLogResponse, int64, error) {
	// 设置商户ID
	condition.MerchantID = &merchantID

	logs, total, err := s.balanceLogRepo.List(ctx, condition)
	if err != nil {
		return nil, 0, errors.ErrInternalError
	}

	var responses []*interfaces.BalanceLogResponse
	for _, log := range logs {
		responses = append(responses, &interfaces.BalanceLogResponse{
			ID:            log.ID,
			MerchantID:    log.MerchantID,
			Type:          log.Type,
			Amount:        log.Amount,
			BeforeBalance: log.BeforeBalance,
			AfterBalance:  log.AfterBalance,
			OrderNo:       log.OrderNo,
			Description:   log.Description,
			CreatedAt:     log.CreatedAt,
		})
	}

	return responses, total, nil
}

// QueryMerchants 查询商户
func (s *merchantService) QueryMerchants(ctx context.Context, condition *repository.MerchantQueryCondition) ([]*interfaces.MerchantResponse, int64, error) {
	merchants, total, err := s.merchantRepo.List(ctx, condition)
	if err != nil {
		return nil, 0, errors.ErrInternalError
	}

	var responses []*interfaces.MerchantResponse
	for _, merchant := range merchants {
		responses = append(responses, s.convertToMerchantResponse(merchant))
	}

	return responses, total, nil
}

// GetMerchantStats 获取商户统计
func (s *merchantService) GetMerchantStats(ctx context.Context) (*repository.MerchantStats, error) {
	return s.merchantRepo.GetMerchantStats(ctx)
}

// UpdateMerchantStatus 更新商户状态
func (s *merchantService) UpdateMerchantStatus(ctx context.Context, merchantID uint, status int) error {
	return s.merchantRepo.UpdateStatus(ctx, merchantID, status)
}

// FreezeMerchant 冻结商户
func (s *merchantService) FreezeMerchant(ctx context.Context, merchantID uint) error {
	return s.UpdateMerchantStatus(ctx, merchantID, model.MerchantStatusFrozen)
}

// UnfreezeMerchant 解冻商户
func (s *merchantService) UnfreezeMerchant(ctx context.Context, merchantID uint) error {
	return s.UpdateMerchantStatus(ctx, merchantID, model.MerchantStatusNormal)
}

// 辅助方法

// validateCreateMerchantAppRequest 验证创建应用请求
func (s *merchantService) validateCreateMerchantAppRequest(req *interfaces.CreateMerchantAppRequest) error {
	v := validator.New()

	v.Required("app_name", req.AppName).MaxLength("app_name", req.AppName, 50).
		Required("app_type", req.AppType)

	if v.HasErrors() {
		return v.ToBusinessError()
	}

	return nil
}

// generateAppID 生成应用ID
func (s *merchantService) generateAppID() string {
	return "APP" + time.Now().Format("20060102") + utils.RandomString(16)
}

// generateAppSecret 生成应用密钥
func (s *merchantService) generateAppSecret() string {
	return utils.RandomString(32)
}

// convertToMerchantAppResponse 转换为应用响应
func (s *merchantService) convertToMerchantAppResponse(app *model.MerchantApp) *interfaces.MerchantAppResponse {
	return &interfaces.MerchantAppResponse{
		ID:          app.ID,
		AppID:       app.AppID,
		MerchantID:  app.MerchantID,
		AppName:     app.AppName,
		AppType:     app.AppType,
		AppSecret:   app.AppSecret,
		PublicKey:   app.PublicKey,
		PrivateKey:  app.PrivateKey,
		Description: app.Description,
		Status:      app.Status,
		CreatedAt:   app.CreatedAt,
		UpdatedAt:   app.UpdatedAt,
	}
}
