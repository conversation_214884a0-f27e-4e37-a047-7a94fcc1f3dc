import type { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _PageHeader from './page-header';
declare const PageHeader: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
        title: StringConstructor;
        subtitle: StringConstructor;
        showBack: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{
        onBack?: ((...args: any[]) => any) | undefined;
    }>, {
        prefixCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        handleBack: (e: Event) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "back"[], import("vue").PublicProps, {
        showBack: boolean;
    }, true, {}, {}, {
        AIconHover: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            prefix: {
                type: StringConstructor;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
                default: string;
            };
            disabled: {
                type: BooleanConstructor;
                default: boolean;
            };
        }>, {
            prefixCls: string;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            prefix: {
                type: StringConstructor;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
                default: string;
            };
            disabled: {
                type: BooleanConstructor;
                default: boolean;
            };
        }>> & Readonly<{}>, {
            disabled: boolean;
            size: "mini" | "medium" | "large" | "small";
        }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        IconLeft: any;
    } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        title: StringConstructor;
        subtitle: StringConstructor;
        showBack: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{
        onBack?: ((...args: any[]) => any) | undefined;
    }>, {
        prefixCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        handleBack: (e: Event) => void;
    }, {}, {}, {}, {
        showBack: boolean;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    title: StringConstructor;
    subtitle: StringConstructor;
    showBack: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onBack?: ((...args: any[]) => any) | undefined;
}>, {
    prefixCls: string;
    cls: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    handleBack: (e: Event) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "back"[], "back", {
    showBack: boolean;
}, {}, string, {}, {
    AIconHover: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>, {
        prefixCls: string;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{}>, {
        disabled: boolean;
        size: "mini" | "medium" | "large" | "small";
    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    IconLeft: any;
} & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type PageHeaderInstance = InstanceType<typeof _PageHeader>;
export default PageHeader;
