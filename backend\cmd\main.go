package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"

	"payment-gateway/pkg/config"
	"payment-gateway/pkg/database"
	dbmigrate "payment-gateway/internal/shared/database"
	"payment-gateway/pkg/logger"
	"go.uber.org/zap"

	// 导入各个域的路由
	"payment-gateway/internal/gateway"
	"payment-gateway/internal/merchant"
	"payment-gateway/internal/admin"
	"payment-gateway/internal/notification"
	"payment-gateway/internal/settlement"
	"payment-gateway/internal/shared/middleware"

	// 导入Gateway域的组件
	"payment-gateway/internal/gateway/repository"
	"payment-gateway/internal/gateway/service"
	"payment-gateway/internal/gateway/handler"
)

var (
	// configPath 配置文件路径
	configPath = flag.String("config", "configs/config.yaml", "配置文件路径")
	// version 显示版本信息
	version = flag.Bool("version", false, "显示版本信息")
)

// 版本信息
var (
	Version   = "1.0.0"
	BuildTime = "unknown"
	GitCommit = "unknown"
)

func main() {
	// 解析命令行参数
	flag.Parse()

	// 显示版本信息
	if *version {
		fmt.Printf("Payment Gateway Version: %s\n", Version)
		fmt.Printf("Build Time: %s\n", BuildTime)
		fmt.Printf("Git Commit: %s\n", GitCommit)
		return
	}

	// 初始化配置
	if err := config.Init(*configPath); err != nil {
		fmt.Printf("初始化配置失败: %v\n", err)
		os.Exit(1)
	}

	// 初始化日志
	if err := logger.Init(&config.Conf.Log); err != nil {
		fmt.Printf("初始化日志失败: %v\n", err)
		os.Exit(1)
	}
	defer logger.Close()

	logger.Info("支付网关服务启动中...",
		zap.String("version", Version),
		zap.String("config", *configPath),
	)

	// 初始化数据库
	if err := database.Init(&config.Conf.Database); err != nil {
		logger.Fatal("初始化数据库失败", zap.Error(err))
	}
	defer database.Close()

	// 执行数据库迁移
	if err := dbmigrate.AutoMigrate(database.DB); err != nil {
		logger.Fatal("数据库迁移失败", zap.Error(err))
	}

	// 初始化基础数据
	if err := dbmigrate.InitData(database.DB); err != nil {
		logger.Fatal("初始化基础数据失败", zap.Error(err))
	}

	// 初始化Redis
	if err := database.InitRedis(&config.Conf.Redis); err != nil {
		logger.Fatal("初始化Redis失败", zap.Error(err))
	}
	defer database.CloseRedis()

	// 设置Gin运行模式
	gin.SetMode(config.Conf.Server.Mode)

	// 创建Gin引擎
	engine := gin.New()

	// 注册中间件
	registerMiddleware(engine)

	// 注册路由
	registerRoutes(engine)

	// 注册页面路由
	registerPageRoutes(engine)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", config.Conf.Server.Port),
		Handler:      engine,
		ReadTimeout:  time.Duration(config.Conf.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(config.Conf.Server.WriteTimeout) * time.Second,
	}

	// 启动服务器
	go func() {
		logger.Info("HTTP服务器启动",
			zap.String("addr", server.Addr),
			zap.String("mode", config.Conf.Server.Mode),
		)

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("HTTP服务器启动失败", zap.Error(err))
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("支付网关服务正在关闭...")

	// 优雅关闭服务器
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.Error("服务器关闭失败", zap.Error(err))
	}

	logger.Info("支付网关服务已关闭")
}

// registerMiddleware 注册中间件
func registerMiddleware(engine *gin.Engine) {
	// 恢复中间件
	engine.Use(gin.Recovery())

	// 日志中间件
	engine.Use(middleware.LoggerMiddleware())

	// CORS中间件
	engine.Use(middleware.CORSMiddleware())

	// 请求ID中间件
	engine.Use(middleware.RequestIDMiddleware())

	// 限流中间件（如果启用）
	if config.Conf.Security.RateLimit.Enabled {
		engine.Use(middleware.RateLimitMiddleware())
	}
}

// registerRoutes 注册路由
func registerRoutes(engine *gin.Engine) {
	// 健康检查
	engine.GET("/health", healthCheck)

	// API路由组
	v1 := engine.Group("/api/v1")

	// 获取数据库连接
	db := database.DB

	// 创建服务实例（按依赖顺序）
	// 1. 先创建基础服务
	merchantService := merchant.GetMerchantService(db)
	notificationService := notification.GetNotificationService(db)

	// 2. 创建依赖其他服务的服务
	settlementService := settlement.GetSettlementService(db, merchantService, notificationService)

	// 注册各域路由
	gateway.RegisterRoutes(v1, db, merchantService, notificationService)
	merchant.RegisterRoutes(v1, db)
	admin.RegisterRoutes(v1, db)
	notification.RegisterRoutes(v1, db)
	settlement.RegisterRoutes(v1, db, merchantService, notificationService)
}

// registerPageRoutes 注册页面路由
func registerPageRoutes(engine *gin.Engine) {
	// 获取数据库连接
	db := database.DB

	// 创建聚合码服务实例
	aggregateCodeRepo := repository.NewAggregateCodeRepository(db)
	channelConfigRepo := repository.NewPaymentChannelConfigRepository(db)
	paymentChannelRepo := repository.NewPaymentChannelRepository(db)

	aggregateCodeService := service.NewAggregateCodeService(
		aggregateCodeRepo,
		channelConfigRepo,
		paymentChannelRepo,
	)

	// 创建页面处理器
	pageHandler := handler.NewPaymentPageHandler(aggregateCodeService)

	// 注册页面路由
	pageHandler.RegisterPageRoutes(engine)
}

// healthCheck 健康检查
func healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"timestamp": time.Now().Unix(),
		"version":   Version,
	})
}
