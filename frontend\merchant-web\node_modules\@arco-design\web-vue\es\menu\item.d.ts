declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, {
    menuContext: Partial<Readonly<Pick<import("./interface").InternalMenuProps, "tooltipProps" | "mode" | "theme" | "triggerProps" | "popupMaxHeight" | "levelIndent" | "autoScrollIntoView" | "scrollConfig" | "inTrigger"> & {
        selectedKeys: string[];
        openKeys: string[];
        prefixCls: string;
        collapsed: boolean;
        expandIconDown?: (() => import("vue").VNodeTypes) | undefined;
        expandIconRight?: (() => import("vue").VNodeTypes) | undefined;
        onSubMenuClick?: ((key: string, level: number) => void) | undefined;
        onMenuItemClick?: ((key: string) => void) | undefined;
    }>>;
    level: import("vue").ComputedRef<number>;
    isSelected: import("vue").ComputedRef<boolean>;
    refItemElement: import("vue").Ref<HTMLDivElement | undefined, HTMLDivElement | undefined>;
    onClick(e: MouseEvent): void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "click"[], "click", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onClick?: ((...args: any[]) => any) | undefined;
}>, {
    disabled: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
