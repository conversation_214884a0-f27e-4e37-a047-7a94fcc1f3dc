# 聚合码功能实现指南

## 功能概述

聚合码功能是支付网关的核心功能之一，实现了"一码多付"的智能聚合支付解决方案。用户扫描一个二维码即可选择多种支付方式（微信支付、支付宝、QQ钱包等）进行支付。

## 技术架构

### 1. 核心组件

#### 1.1 数据模型 (Model Layer)
- **AggregateCode**: 聚合支付码主模型
- **PaymentChannelConfig**: 支付渠道配置模型

#### 1.2 数据访问层 (Repository Layer)
- **AggregateCodeRepository**: 聚合码数据访问接口
- **PaymentChannelConfigRepository**: 支付渠道配置数据访问接口

#### 1.3 业务逻辑层 (Service Layer)
- **AggregateCodeService**: 聚合码业务逻辑服务
- **PaymentChannelService**: 支付渠道服务（gopay SDK集成）

#### 1.4 控制器层 (Handler Layer)
- **AggregateCodeHandler**: 聚合码HTTP API处理器
- **PaymentPageHandler**: 支付页面处理器

### 2. 核心功能

#### 2.1 聚合码生成
```go
// 创建聚合码
POST /api/v1/aggregate-codes
{
    "merchant_id": 1,
    "app_id": "app_123",
    "subject": "商品购买",
    "amount": 10000,  // 分为单位
    "supported_channels": ["wechat", "alipay", "qqpay"],
    "expire_time": "2024-01-01T12:00:00Z",
    "brand_config": {
        "logo_url": "https://example.com/logo.png",
        "theme_color": "#1890ff",
        "merchant_name": "示例商户"
    }
}
```

#### 2.2 智能路由
根据用户User-Agent自动检测客户端类型，推荐最适合的支付方式：
- 微信客户端 → 微信支付
- 支付宝客户端 → 支付宝
- QQ客户端 → QQ钱包
- 其他浏览器 → 显示所有支持的支付方式

#### 2.3 支付页面
提供品牌定制的支付页面，支持：
- 商户Logo和品牌色彩
- 倒计时功能
- 多支付方式选择
- 响应式设计

## 实现状态

### ✅ 已完成
1. **数据模型设计**
   - AggregateCode模型（支持一码多付、品牌定制、统计功能）
   - PaymentChannelConfig模型（支付渠道配置管理）

2. **Repository层实现**
   - AggregateCodeRepository完整实现
   - PaymentChannelConfigRepository完整实现
   - 包含CRUD操作、状态管理、统计查询等

3. **Service层实现**
   - AggregateCodeService核心业务逻辑
   - 智能路由算法实现
   - PaymentChannelService gopay SDK集成

4. **Handler层实现**
   - RESTful API端点完整实现
   - Swagger文档注解
   - PaymentPageHandler支付页面渲染

5. **前端模板**
   - 支付页面HTML模板
   - 错误页面模板
   - QR码展示页面模板

6. **路由配置**
   - Gateway域路由注册
   - 页面路由集成

### 🔄 进行中
1. **编译错误修复**
   - 正在修复interfaces结构体定义缺失问题
   - 正在修复各service层的类型不匹配问题
   - 正在修复logger调用格式问题

### ⏳ 待完成
1. **gopay SDK完整集成**
   - 微信支付真实API调用
   - 支付宝真实API调用
   - QQ钱包真实API调用

2. **测试验证**
   - 单元测试编写
   - 集成测试验证
   - 端到端测试

3. **文档完善**
   - API文档生成
   - 部署指南
   - 使用说明

## 技术特点

### 1. 智能路由算法
```go
func (s *aggregateCodeService) detectPaymentChannel(userAgent string, supportedChannels []string) (string, string) {
    userAgent = strings.ToLower(userAgent)
    
    // 微信客户端检测
    if strings.Contains(userAgent, "micromessenger") {
        if contains(supportedChannels, model.ChannelWechat) {
            return model.ChannelWechat, "检测到微信客户端"
        }
    }
    
    // 支付宝客户端检测
    if strings.Contains(userAgent, "alipayclient") {
        if contains(supportedChannels, model.ChannelAlipay) {
            return model.ChannelAlipay, "检测到支付宝客户端"
        }
    }
    
    // QQ客户端检测
    if strings.Contains(userAgent, "qq/") {
        if contains(supportedChannels, model.ChannelQQPay) {
            return model.ChannelQQPay, "检测到QQ客户端"
        }
    }
    
    // 默认返回第一个支持的渠道
    if len(supportedChannels) > 0 {
        return supportedChannels[0], "默认推荐"
    }
    
    return "", "无可用支付渠道"
}
```

### 2. 品牌定制支持
- 支持商户Logo自定义
- 支持主题色彩配置
- 支持商户名称展示
- 响应式页面设计

### 3. 统计功能
- 扫码次数统计
- 支付成功次数统计
- 支付金额统计
- 最后扫码/支付时间记录

## 数据库设计

### aggregate_codes表
```sql
CREATE TABLE aggregate_codes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code_id VARCHAR(32) UNIQUE NOT NULL COMMENT '聚合码ID',
    merchant_id BIGINT NOT NULL COMMENT '商户ID',
    app_id VARCHAR(32) NOT NULL COMMENT '应用ID',
    subject VARCHAR(200) NOT NULL COMMENT '支付主题',
    amount BIGINT NOT NULL COMMENT '支付金额(分)',
    supported_channels JSON NOT NULL COMMENT '支持的支付渠道',
    brand_config JSON COMMENT '品牌配置',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,2禁用,3过期',
    expire_time DATETIME COMMENT '过期时间',
    scan_count INT DEFAULT 0 COMMENT '扫码次数',
    pay_count INT DEFAULT 0 COMMENT '支付次数',
    total_amount BIGINT DEFAULT 0 COMMENT '总支付金额',
    last_scan_at DATETIME COMMENT '最后扫码时间',
    last_pay_at DATETIME COMMENT '最后支付时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at DATETIME COMMENT '软删除时间',
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_app_id (app_id),
    INDEX idx_status (status),
    INDEX idx_expire_time (expire_time)
);
```

### payment_channel_configs表
```sql
CREATE TABLE payment_channel_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    merchant_id BIGINT NOT NULL COMMENT '商户ID',
    channel_code VARCHAR(20) NOT NULL COMMENT '渠道代码',
    channel_name VARCHAR(50) NOT NULL COMMENT '渠道名称',
    app_id VARCHAR(100) NOT NULL COMMENT '应用ID',
    mch_id VARCHAR(100) COMMENT '商户号',
    api_key VARCHAR(200) COMMENT 'API密钥',
    private_key TEXT COMMENT '私钥',
    public_key TEXT COMMENT '公钥',
    cert_path VARCHAR(200) COMMENT '证书路径',
    is_prod BOOLEAN DEFAULT FALSE COMMENT '是否生产环境',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,2禁用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_merchant_channel (merchant_id, channel_code),
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_channel_code (channel_code)
);
```

## API接口

### 1. 创建聚合码
- **URL**: `POST /api/v1/aggregate-codes`
- **功能**: 创建新的聚合支付码
- **返回**: 聚合码信息和二维码URL

### 2. 获取聚合码信息
- **URL**: `GET /api/v1/aggregate-codes/{code_id}`
- **功能**: 获取聚合码详细信息

### 3. 智能路由
- **URL**: `POST /api/v1/aggregate-codes/{code_id}/smart-route`
- **功能**: 根据User-Agent推荐支付方式

### 4. 支付页面
- **URL**: `GET /pay/{code_id}`
- **功能**: 显示品牌定制的支付页面

### 5. 发起支付
- **URL**: `POST /api/v1/aggregate-codes/{code_id}/pay`
- **功能**: 发起具体支付渠道的支付

## 下一步计划

1. **修复编译错误** - 完善interfaces定义，确保项目可以正常编译
2. **集成测试** - 编写测试用例验证聚合码功能
3. **真实支付集成** - 配置真实的支付渠道参数
4. **性能优化** - 优化数据库查询和缓存策略
5. **监控告警** - 添加业务监控和异常告警

## 注意事项

1. **安全性**: 支付渠道配置信息需要加密存储
2. **并发控制**: 支付操作需要考虑并发安全
3. **幂等性**: 支付接口需要保证幂等性
4. **监控**: 需要完善的业务监控和日志记录
5. **容错**: 需要完善的异常处理和降级策略
