import { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _Split from './split';
declare const Split: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<{
        component: string;
        direction: "horizontal" | "vertical";
        size: string | number | undefined;
        defaultSize: string | number;
        min: string | number | undefined;
        max: string | number | undefined;
        disabled: boolean;
    }> & Readonly<{
        onMoving?: ((ev: MouseEvent) => any) | undefined;
        onMoveStart?: ((ev: MouseEvent) => any) | undefined;
        onMoveEnd?: ((ev: MouseEvent) => any) | undefined;
        "onUpdate:size"?: ((size: string | number) => any) | undefined;
    }>, {
        prefixCls: string;
        classNames: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        isHorizontal: import("vue").ComputedRef<boolean>;
        wrapperRef: import("vue").Ref<HTMLDivElement | undefined, HTMLDivElement | undefined>;
        onMoveStart: (e: MouseEvent) => Promise<void>;
        onTriggerResize: (entry: ResizeObserverEntry) => void;
        firstPaneStyles: import("vue").ComputedRef<{
            flex: string;
        }>;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        moveStart: (ev: MouseEvent) => true;
        moving: (ev: MouseEvent) => true;
        moveEnd: (ev: MouseEvent) => true;
        'update:size': (size: string | number) => true;
    }, import("vue").PublicProps, {
        disabled: boolean;
        size: string | number;
        direction: "horizontal" | "vertical";
        component: string;
        defaultSize: string | number;
    }, true, {}, {}, {
        ResizeTrigger: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            direction: {
                type: import("vue").PropType<"horizontal" | "vertical">;
                default: string;
            };
        }>, {
            classNames: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            onResize: (entry: ResizeObserverEntry) => void;
            isHorizontal: import("vue").ComputedRef<boolean>;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "resize"[], "resize", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            prefixCls: {
                type: StringConstructor;
                required: true;
            };
            direction: {
                type: import("vue").PropType<"horizontal" | "vertical">;
                default: string;
            };
        }>> & Readonly<{
            onResize?: ((...args: any[]) => any) | undefined;
        }>, {
            direction: "horizontal" | "vertical";
        }, {}, {
            ResizeObserver: import("vue").DefineComponent<{}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
                [key: string]: any;
            }> | null, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "resize"[], "resize", import("vue").PublicProps, Readonly<{}> & Readonly<{
                onResize?: ((...args: any[]) => any) | undefined;
            }>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
            IconDragDot: any;
            IconDragDotVertical: any;
        }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<{
        component: string;
        direction: "horizontal" | "vertical";
        size: string | number | undefined;
        defaultSize: string | number;
        min: string | number | undefined;
        max: string | number | undefined;
        disabled: boolean;
    }> & Readonly<{
        onMoving?: ((ev: MouseEvent) => any) | undefined;
        onMoveStart?: ((ev: MouseEvent) => any) | undefined;
        onMoveEnd?: ((ev: MouseEvent) => any) | undefined;
        "onUpdate:size"?: ((size: string | number) => any) | undefined;
    }>, {
        prefixCls: string;
        classNames: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        isHorizontal: import("vue").ComputedRef<boolean>;
        wrapperRef: import("vue").Ref<HTMLDivElement | undefined, HTMLDivElement | undefined>;
        onMoveStart: (e: MouseEvent) => Promise<void>;
        onTriggerResize: (entry: ResizeObserverEntry) => void;
        firstPaneStyles: import("vue").ComputedRef<{
            flex: string;
        }>;
    }, {}, {}, {}, {
        disabled: boolean;
        size: string | number;
        direction: "horizontal" | "vertical";
        component: string;
        defaultSize: string | number;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<{
    component: string;
    direction: "horizontal" | "vertical";
    size: string | number | undefined;
    defaultSize: string | number;
    min: string | number | undefined;
    max: string | number | undefined;
    disabled: boolean;
}> & Readonly<{
    onMoving?: ((ev: MouseEvent) => any) | undefined;
    onMoveStart?: ((ev: MouseEvent) => any) | undefined;
    onMoveEnd?: ((ev: MouseEvent) => any) | undefined;
    "onUpdate:size"?: ((size: string | number) => any) | undefined;
}>, {
    prefixCls: string;
    classNames: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    isHorizontal: import("vue").ComputedRef<boolean>;
    wrapperRef: import("vue").Ref<HTMLDivElement | undefined, HTMLDivElement | undefined>;
    onMoveStart: (e: MouseEvent) => Promise<void>;
    onTriggerResize: (entry: ResizeObserverEntry) => void;
    firstPaneStyles: import("vue").ComputedRef<{
        flex: string;
    }>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    moveStart: (ev: MouseEvent) => true;
    moving: (ev: MouseEvent) => true;
    moveEnd: (ev: MouseEvent) => true;
    'update:size': (size: string | number) => true;
}, string, {
    disabled: boolean;
    size: string | number;
    direction: "horizontal" | "vertical";
    component: string;
    defaultSize: string | number;
}, {}, string, {}, {
    ResizeTrigger: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        prefixCls: {
            type: StringConstructor;
            required: true;
        };
        direction: {
            type: import("vue").PropType<"horizontal" | "vertical">;
            default: string;
        };
    }>, {
        classNames: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        onResize: (entry: ResizeObserverEntry) => void;
        isHorizontal: import("vue").ComputedRef<boolean>;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "resize"[], "resize", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        prefixCls: {
            type: StringConstructor;
            required: true;
        };
        direction: {
            type: import("vue").PropType<"horizontal" | "vertical">;
            default: string;
        };
    }>> & Readonly<{
        onResize?: ((...args: any[]) => any) | undefined;
    }>, {
        direction: "horizontal" | "vertical";
    }, {}, {
        ResizeObserver: import("vue").DefineComponent<{}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
            [key: string]: any;
        }> | null, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "resize"[], "resize", import("vue").PublicProps, Readonly<{}> & Readonly<{
            onResize?: ((...args: any[]) => any) | undefined;
        }>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        IconDragDot: any;
        IconDragDotVertical: any;
    }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
} & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type SplitInstance = InstanceType<typeof _Split>;
export default Split;
