package service

import (
	"context"
	"crypto/md5"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"payment-gateway/internal/admin/model"
	"payment-gateway/internal/admin/repository"
	"payment-gateway/internal/shared/constants"
	"payment-gateway/internal/shared/interfaces"
	"payment-gateway/internal/shared/utils/validator"
	"strconv"
	"time"

	"gorm.io/gorm"
)

// 使用shared/interfaces中的AdminService接口

// adminService 管理员服务实现
type adminService struct {
	db               *gorm.DB
	adminUserRepo    repository.AdminUserRepository
	systemConfigRepo repository.SystemConfigRepository
	systemLogRepo    repository.SystemLogRepository
	systemStatsRepo  repository.SystemStatsRepository
	blacklistIPRepo  repository.BlacklistIPRepository
}

// NewAdminService 创建管理员服务
func NewAdminService(
	db *gorm.DB,
	adminUserRepo repository.AdminUserRepository,
	systemConfigRepo repository.SystemConfigRepository,
	systemLogRepo repository.SystemLogRepository,
	systemStatsRepo repository.SystemStatsRepository,
	blacklistIPRepo repository.BlacklistIPRepository,
) interfaces.AdminService {
	return &adminService{
		db:               db,
		adminUserRepo:    adminUserRepo,
		systemConfigRepo: systemConfigRepo,
		systemLogRepo:    systemLogRepo,
		systemStatsRepo:  systemStatsRepo,
		blacklistIPRepo:  blacklistIPRepo,
	}
}

// CreateAdminUser 创建管理员用户（接口方法）
func (s *adminService) CreateAdminUser(ctx context.Context, req *interfaces.CreateAdminUserRequest) (*interfaces.AdminUserResponse, error) {
	// 转换为CreateAdminRequest
	createReq := &interfaces.CreateAdminRequest{
		Username: req.Username,
		Password: req.Password,
		RealName: req.RealName,
		Email:    req.Email,
		Phone:    req.Phone,
		Status:   req.Status,
	}

	// 调用CreateAdmin方法
	adminResp, err := s.CreateAdmin(ctx, createReq)
	if err != nil {
		return nil, err
	}

	// 转换响应
	return &interfaces.AdminUserResponse{
		ID:        adminResp.ID,
		Username:  adminResp.Username,
		RealName:  adminResp.RealName,
		Email:     adminResp.Email,
		Phone:     adminResp.Phone,
		Status:    adminResp.Status,
		CreatedAt: adminResp.CreatedAt,
		UpdatedAt: adminResp.UpdatedAt,
	}, nil
}

// GetAdminUser 获取管理员用户（接口方法）
func (s *adminService) GetAdminUser(ctx context.Context, userID uint) (*interfaces.AdminUserResponse, error) {
	// 调用GetAdminByID方法
	adminResp, err := s.GetAdminByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 转换响应
	return &interfaces.AdminUserResponse{
		ID:        adminResp.ID,
		Username:  adminResp.Username,
		RealName:  adminResp.RealName,
		Email:     adminResp.Email,
		Phone:     adminResp.Phone,
		Status:    adminResp.Status,
		CreatedAt: adminResp.CreatedAt,
		UpdatedAt: adminResp.UpdatedAt,
	}, nil
}

// DeleteAdminUser 删除管理员用户（接口方法）
func (s *adminService) DeleteAdminUser(ctx context.Context, userID uint) error {
	return s.DeleteAdmin(ctx, userID)
}

// ListAdminUsers 管理员用户列表（接口方法）
func (s *adminService) ListAdminUsers(ctx context.Context, req *interfaces.ListAdminUsersRequest) (*interfaces.ListAdminUsersResponse, error) {
	// 转换为ListAdminsRequest
	listReq := &interfaces.ListAdminsRequest{
		Username: req.Username,
		Status:   req.Status,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	// 调用ListAdmins方法
	adminResp, err := s.ListAdmins(ctx, listReq)
	if err != nil {
		return nil, err
	}

	// 转换响应
	users := make([]*interfaces.AdminUserResponse, len(adminResp.List))
	for i, admin := range adminResp.List {
		users[i] = &interfaces.AdminUserResponse{
			ID:        admin.ID,
			Username:  admin.Username,
			RealName:  admin.RealName,
			Email:     admin.Email,
			Phone:     admin.Phone,
			Status:    admin.Status,
			CreatedAt: admin.CreatedAt,
			UpdatedAt: admin.UpdatedAt,
		}
	}

	return &interfaces.ListAdminUsersResponse{
		List:  users,
		Total: adminResp.Total,
		Page:  adminResp.Page,
		Size:  req.PageSize,
	}, nil
}

// CreateAdmin 创建管理员
func (s *adminService) CreateAdmin(ctx context.Context, req *interfaces.CreateAdminRequest) (*interfaces.AdminResponse, error) {
	// 参数验证
	if err := s.validateCreateAdminRequest(req); err != nil {
		return nil, err
	}

	// 检查用户名、邮箱、手机号唯一性
	if s.adminUserRepo.CheckUsernameExists(ctx, req.Username, 0) {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeValidation, "用户名已存在")
	}

	if req.Email != "" && s.adminUserRepo.CheckEmailExists(ctx, req.Email, 0) {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeValidation, "邮箱已存在")
	}

	if req.Phone != "" && s.adminUserRepo.CheckPhoneExists(ctx, req.Phone, 0) {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeValidation, "手机号已存在")
	}

	// 生成密码盐值和加密密码
	salt := s.generateSalt()
	hashedPassword := s.hashPassword(req.Password, salt)

	// 创建管理员用户
	admin := &model.AdminUser{
		Username: req.Username,
		Email:    req.Email,
		Phone:    req.Phone,
		Password: hashedPassword,
		Salt:     salt,
		RealName: req.RealName,
		Role:     "admin", // 默认角色
		Status:   req.Status,
	}

	if err := s.adminUserRepo.Create(ctx, admin); err != nil {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "创建管理员失败")
	}

	return s.buildAdminResponse(admin), nil
}

// GetAdminByID 根据ID获取管理员
func (s *adminService) GetAdminByID(ctx context.Context, id uint) (*interfaces.AdminResponse, error) {
	admin, err := s.adminUserRepo.GetByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, interfaces.NewBusinessError(interfaces.ErrCodeNotFound, "管理员不存在")
		}
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取管理员失败")
	}

	return s.buildAdminResponse(admin), nil
}

// UpdateAdminUser 更新管理员用户
func (s *adminService) UpdateAdminUser(ctx context.Context, userID uint, req *interfaces.UpdateAdminUserRequest) error {
	// 获取现有管理员
	admin, err := s.adminUserRepo.GetByID(ctx, userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return interfaces.NewBusinessError(interfaces.ErrCodeNotFound, "管理员不存在")
		}
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取管理员失败")
	}

	// 检查唯一性（排除自己）
	if req.Email != "" && req.Email != admin.Email && s.adminUserRepo.CheckEmailExists(ctx, req.Email, userID) {
		return interfaces.NewBusinessError(interfaces.ErrCodeValidation, "邮箱已存在")
	}

	if req.Phone != "" && req.Phone != admin.Phone && s.adminUserRepo.CheckPhoneExists(ctx, req.Phone, userID) {
		return interfaces.NewBusinessError(interfaces.ErrCodeValidation, "手机号已存在")
	}

	// 更新字段
	if req.Email != "" {
		admin.Email = req.Email
	}
	if req.Phone != "" {
		admin.Phone = req.Phone
	}
	if req.RealName != "" {
		admin.RealName = req.RealName
	}
	if req.Status != 0 {
		admin.Status = req.Status
	}

	if err := s.adminUserRepo.Update(ctx, admin); err != nil {
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "更新管理员失败")
	}

	return nil
}

// DeleteAdmin 删除管理员
func (s *adminService) DeleteAdmin(ctx context.Context, id uint) error {
	// 检查管理员是否存在
	_, err := s.adminUserRepo.GetByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return interfaces.NewBusinessError(interfaces.ErrCodeNotFound, "管理员不存在")
		}
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取管理员失败")
	}

	if err := s.adminUserRepo.Delete(ctx, id); err != nil {
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "删除管理员失败")
	}

	return nil
}

// ListAdmins 分页查询管理员
func (s *adminService) ListAdmins(ctx context.Context, req *interfaces.ListAdminsRequest) (*interfaces.ListAdminsResponse, error) {
	condition := &repository.AdminUserQueryCondition{
		Username: &req.Username,
		RealName: &req.RealName,
		Status:   &req.Status,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	admins, total, err := s.adminUserRepo.List(ctx, condition)
	if err != nil {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "查询管理员失败")
	}

	var adminList []*interfaces.AdminResponse
	for _, admin := range admins {
		adminList = append(adminList, s.buildAdminResponse(admin))
	}

	return &interfaces.ListAdminsResponse{
		List:  adminList,
		Total: total,
		Page:  req.Page,
		Size:  req.PageSize,
	}, nil
}

// AdminLogin 管理员登录
func (s *adminService) AdminLogin(ctx context.Context, req *interfaces.AdminLoginRequest) (*interfaces.AdminLoginResponse, error) {
	// 参数验证
	if err := s.validateAdminLoginRequest(req); err != nil {
		return nil, err
	}

	// 获取管理员用户
	admin, err := s.adminUserRepo.GetByUsername(ctx, req.Username)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, interfaces.NewBusinessError(interfaces.ErrCodeValidation, "用户名或密码错误")
		}
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "登录失败")
	}

	// 验证密码
	if !s.verifyPassword(req.Password, admin.Salt, admin.Password) {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeValidation, "用户名或密码错误")
	}

	// 检查用户状态
	if admin.Status == model.AdminStatusDisabled {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeValidation, "账户已被禁用")
	}

	if admin.Status == model.AdminStatusLocked {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeValidation, "账户已被锁定")
	}

	// 更新登录信息（暂时跳过IP记录，因为接口中没有LoginIP字段）
	// if err := s.adminUserRepo.UpdateLoginInfo(ctx, admin.ID, ""); err != nil {
	// 	// 登录信息更新失败不影响登录流程，只记录日志
	// 	// TODO: 记录日志
	// }

	// 生成JWT Token（这里简化处理，实际应该使用JWT库）
	token := s.generateToken(admin)

	// 转换为AdminUserResponse
	userResp := &interfaces.AdminUserResponse{
		ID:        admin.ID,
		Username:  admin.Username,
		RealName:  admin.RealName,
		Email:     admin.Email,
		Phone:     admin.Phone,
		Status:    admin.Status,
		CreatedAt: admin.CreatedAt,
		UpdatedAt: admin.UpdatedAt,
	}

	return &interfaces.AdminLoginResponse{
		Token:     token,
		User:      userResp,
		ExpiresAt: time.Now().Add(24 * time.Hour),
	}, nil
}

// ChangePassword 修改密码
func (s *adminService) ChangePassword(ctx context.Context, req *interfaces.ChangePasswordRequest) error {
	// TODO: 从context中获取当前用户ID
	// 暂时返回未实现错误
	return interfaces.NewBusinessError(fmt.Sprintf("%d", constants.ErrCodeInternalError), "功能暂未实现，需要从JWT token中获取用户ID")
}

// ResetPassword 重置密码
func (s *adminService) ResetPassword(ctx context.Context, req *interfaces.ResetPasswordRequest) error {
	// 检查管理员是否存在
	_, err := s.adminUserRepo.GetByID(ctx, req.UserID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return interfaces.NewBusinessErrorWithCode(constants.ErrCodeNotFound, "管理员不存在")
		}
		return interfaces.NewBusinessErrorWithCode(constants.ErrCodeInternalError, "获取管理员失败")
	}

	// 生成新密码
	salt := s.generateSalt()
	hashedPassword := s.hashPassword(req.NewPassword, salt)

	if err := s.adminUserRepo.UpdatePassword(ctx, req.UserID, hashedPassword, salt); err != nil {
		return interfaces.NewBusinessErrorWithCode(constants.ErrCodeInternalError, "重置密码失败")
	}

	return nil
}

// UpdateAdminStatus 更新管理员状态
func (s *adminService) UpdateAdminStatus(ctx context.Context, id uint, status int) error {
	// 检查管理员是否存在
	_, err := s.adminUserRepo.GetByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return interfaces.NewBusinessError(interfaces.ErrCodeNotFound, "管理员不存在")
		}
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取管理员失败")
	}

	if err := s.adminUserRepo.UpdateStatus(ctx, id, status); err != nil {
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "更新状态失败")
	}

	return nil
}

// GetAdminStats 获取管理员统计
func (s *adminService) GetAdminStats(ctx context.Context) (*interfaces.AdminStatsResponse, error) {
	// TODO: 实现真实的统计逻辑
	// 暂时返回模拟数据
	return &interfaces.AdminStatsResponse{
		TotalUsers:     100,
		TotalMerchants: 50,
		TotalOrders:    1000,
		TotalAmount:    500000,
	}, nil
}

// 辅助方法

// generateSalt 生成密码盐值
func (s *adminService) generateSalt() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// hashPassword 加密密码
func (s *adminService) hashPassword(password, salt string) string {
	hash := md5.Sum([]byte(password + salt))
	return fmt.Sprintf("%x", hash)
}

// verifyPassword 验证密码
func (s *adminService) verifyPassword(password, salt, hashedPassword string) bool {
	return s.hashPassword(password, salt) == hashedPassword
}

// generateToken 生成Token（简化实现）
func (s *adminService) generateToken(admin *model.AdminUser) string {
	// 实际应该使用JWT库生成Token
	return fmt.Sprintf("admin_token_%d_%d", admin.ID, time.Now().Unix())
}

// buildAdminResponse 构建管理员响应
func (s *adminService) buildAdminResponse(admin *model.AdminUser) *interfaces.AdminResponse {
	// 将Role字符串转换为RoleID（暂时使用固定值）
	var roleID uint = 1
	if admin.Role == "super_admin" {
		roleID = 1
	} else if admin.Role == "admin" {
		roleID = 2
	}

	return &interfaces.AdminResponse{
		ID:        admin.ID,
		Username:  admin.Username,
		RealName:  admin.RealName,
		Email:     admin.Email,
		Phone:     admin.Phone,
		RoleID:    roleID,
		Status:    admin.Status,
		CreatedAt: admin.CreatedAt,
		UpdatedAt: admin.UpdatedAt,
	}
}

// validateCreateAdminRequest 验证创建管理员请求
func (s *adminService) validateCreateAdminRequest(req *interfaces.CreateAdminRequest) error {
	v := validator.New()

	v.Required("username", req.Username).MinLength("username", req.Username, 3).MaxLength("username", req.Username, 50).
		Required("password", req.Password).MinLength("password", req.Password, 6).
		Required("real_name", req.RealName).MaxLength("real_name", req.RealName, 50)

	if req.Email != "" {
		v.Email("email", req.Email)
	}

	if req.Phone != "" {
		v.Phone("phone", req.Phone)
	}

	if v.HasErrors() {
		return v.ToBusinessError()
	}

	return nil
}

// validateAdminLoginRequest 验证管理员登录请求
func (s *adminService) validateAdminLoginRequest(req *interfaces.AdminLoginRequest) error {
	v := validator.New()

	v.Required("username", req.Username).
		Required("password", req.Password)

	if v.HasErrors() {
		return v.ToBusinessError()
	}

	return nil
}

// GetSystemConfig 获取系统配置
func (s *adminService) GetSystemConfig(ctx context.Context, configKey string) (*interfaces.SystemConfigResponse, error) {
	config, err := s.systemConfigRepo.GetByKey(ctx, configKey)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, interfaces.NewBusinessError(interfaces.ErrCodeNotFound, "配置不存在")
		}
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取配置失败")
	}

	return s.buildSystemConfigResponse(config), nil
}

// SetSystemConfig 设置系统配置
func (s *adminService) SetSystemConfig(ctx context.Context, req *interfaces.SetSystemConfigRequest) error {
	// 检查配置是否存在
	config, err := s.systemConfigRepo.GetByKey(ctx, req.Key)
	if err != nil && err != gorm.ErrRecordNotFound {
		return interfaces.NewBusinessErrorWithCode(constants.ErrCodeInternalError, "获取配置失败")
	}

	if err == gorm.ErrRecordNotFound {
		// 创建新配置
		config = &model.SystemConfig{
			ConfigKey:   req.Key,
			ConfigValue: req.Value,
			ConfigType:  req.Type,
			Description: req.Description,
		}

		if err := s.systemConfigRepo.Create(ctx, config); err != nil {
			return interfaces.NewBusinessErrorWithCode(constants.ErrCodeInternalError, "创建配置失败")
		}
	} else {
		// 更新现有配置
		config.ConfigValue = req.Value
		if req.Type != "" {
			config.ConfigType = req.Type
		}
		if req.Description != "" {
			config.Description = req.Description
		}

		if err := s.systemConfigRepo.Update(ctx, config); err != nil {
			return interfaces.NewBusinessErrorWithCode(constants.ErrCodeInternalError, "更新配置失败")
		}
	}

	return nil
}

// ListSystemConfigs 分页查询系统配置
func (s *adminService) ListSystemConfigs(ctx context.Context, req *interfaces.ListSystemConfigsRequest) (*interfaces.ListSystemConfigsResponse, error) {
	condition := &repository.SystemConfigQueryCondition{
		ConfigKey:  &req.ConfigKey,
		Category:   req.Category,
		Name:       &req.Name,
		ConfigType: &req.ConfigType,
		IsPublic:   &req.IsPublic,
		Page:       req.Page,
		PageSize:   req.PageSize,
		OrderBy:    req.OrderBy,
	}

	configs, total, err := s.systemConfigRepo.List(ctx, condition)
	if err != nil {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "查询配置失败")
	}

	var configList []*interfaces.SystemConfigResponse
	for _, config := range configs {
		configList = append(configList, s.buildSystemConfigResponse(config))
	}

	return &interfaces.ListSystemConfigsResponse{
		List:     configList,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// GetPublicConfigs 获取公开配置
func (s *adminService) GetPublicConfigs(ctx context.Context) (map[string]interface{}, error) {
	configs, err := s.systemConfigRepo.GetPublicConfigs(ctx)
	if err != nil {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取公开配置失败")
	}

	result := make(map[string]interface{})
	for _, config := range configs {
		result[config.ConfigKey] = config.ConfigValue
	}

	return result, nil
}

// CreateSystemLog 创建系统日志
func (s *adminService) CreateSystemLog(ctx context.Context, req *interfaces.CreateSystemLogRequest) error {
	log := &model.SystemLog{
		Module:       req.Module,
		Action:       req.Action,
		Method:       req.Method,
		URL:          req.URL,
		UserID:       req.UserID,
		Username:     req.Username,
		UserType:     req.UserType,
		IP:           req.IP,
		UserAgent:    req.UserAgent,
		RequestData:  req.RequestData,
		ResponseData: req.ResponseData,
		Status:       req.Status,
		Duration:     int(req.Duration),
		ErrorMsg:     req.ErrorMsg,
		Location:     req.Location,
	}

	if err := s.systemLogRepo.Create(ctx, log); err != nil {
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "创建日志失败")
	}

	return nil
}

// ListSystemLogs 分页查询系统日志
func (s *adminService) ListSystemLogs(ctx context.Context, req *interfaces.ListSystemLogsRequest) (*interfaces.ListSystemLogsResponse, error) {
	condition := &repository.SystemLogQueryCondition{
		Module:    &req.Module,
		StartDate: &req.StartTime,
		EndDate:   &req.EndTime,
		Page:      req.Page,
		PageSize:  req.PageSize,
	}

	logs, total, err := s.systemLogRepo.List(ctx, condition)
	if err != nil {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "查询日志失败")
	}

	var logList []*interfaces.SystemLogResponse
	for _, log := range logs {
		logList = append(logList, s.buildSystemLogResponse(log))
	}

	return &interfaces.ListSystemLogsResponse{
		Items: logList,
		Total: total,
	}, nil
}

// GetLogStats 获取日志统计
func (s *adminService) GetLogStats(ctx context.Context, req *interfaces.GetLogStatsRequest) (*interfaces.LogStatsResponse, error) {
	stats, err := s.systemLogRepo.GetLogStats(ctx, req.Module, req.StartDate, req.EndDate)
	if err != nil {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取日志统计失败")
	}

	return &interfaces.LogStatsResponse{
		TotalCount:   stats.TotalCount,
		SuccessCount: stats.SuccessCount,
		ErrorCount:   stats.ErrorCount,
		SuccessRate:  float64(stats.SuccessRate),
		AvgDuration:  float64(stats.AvgDuration),
	}, nil
}

// buildSystemConfigResponse 构建系统配置响应
func (s *adminService) buildSystemConfigResponse(config *model.SystemConfig) *interfaces.SystemConfigResponse {
	return &interfaces.SystemConfigResponse{
		ID:          config.ID,
		ConfigKey:   config.ConfigKey,
		ConfigValue: config.ConfigValue,
		ConfigType:  config.ConfigType,
		Category:    config.Category,
		Name:        config.Name,
		Description: config.Description,
		IsPublic:    config.IsPublic,
		SortOrder:   config.SortOrder,
		Remark:      config.Remark,
		CreatedAt:   config.CreatedAt,
		UpdatedAt:   config.UpdatedAt,
	}
}

// buildSystemLogResponse 构建系统日志响应
func (s *adminService) buildSystemLogResponse(log *model.SystemLog) *interfaces.SystemLogResponse {
	return &interfaces.SystemLogResponse{
		ID:        log.ID,
		Module:    log.Module,
		Level:     "INFO", // 默认级别
		Message:   log.Action + " " + log.URL,
		Context:   log.RequestData,
		CreatedAt: log.CreatedAt,
	}
}

// GetSystemStats 获取系统统计
func (s *adminService) GetSystemStats(ctx context.Context, req *interfaces.SystemStatsRequest) (*interfaces.SystemStatsResponse, error) {
	// 简化实现，返回基础统计数据
	return &interfaces.SystemStatsResponse{
		TotalMerchants:  100, // 示例数据
		TotalOrders:     1000,
		TotalAmount:     50000.00,
		SuccessOrders:   900,
		SuccessAmount:   45000.00,
		SuccessRate:     0.90,
		TodayOrders:     50,
		TodayAmount:     2500.00,
		YesterdayOrders: 45,
		YesterdayAmount: 2250.00,
	}, nil
}

// UpdateSystemStats 更新系统统计
func (s *adminService) UpdateSystemStats(ctx context.Context, req *interfaces.UpdateSystemStatsRequest) error {
	// 简化实现，暂时返回成功
	return nil
}

// GetDashboardData 获取仪表板数据
func (s *adminService) GetDashboardData(ctx context.Context) (*interfaces.DashboardDataResponse, error) {
	// 简化实现，返回模拟数据
	return &interfaces.DashboardDataResponse{
		TotalUsers:     1000,
		TotalMerchants: 50,
		TotalOrders:    5000,
		TotalAmount:    1000000,
		TodayOrders:    100,
		TodayAmount:    20000,
	}, nil
}

// AddBlacklistIP 添加IP黑名单
func (s *adminService) AddBlacklistIP(ctx context.Context, req *interfaces.AddBlacklistIPRequest) error {
	// 检查IP是否已存在
	_, err := s.blacklistIPRepo.GetByIP(ctx, req.IP)
	if err == nil {
		return interfaces.NewBusinessError(interfaces.ErrCodeValidation, "IP已在黑名单中")
	}
	if err != gorm.ErrRecordNotFound {
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "检查IP失败")
	}

	blacklist := &model.BlacklistIP{
		IP:     req.IP,
		Reason: req.Reason,
		Status: 1, // 默认启用
	}

	if err := s.blacklistIPRepo.Create(ctx, blacklist); err != nil {
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "添加IP黑名单失败")
	}

	return nil
}

// RemoveBlacklistIP 移除IP黑名单
func (s *adminService) RemoveBlacklistIP(ctx context.Context, id uint) error {
	// 检查记录是否存在
	_, err := s.blacklistIPRepo.GetByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return interfaces.NewBusinessError(interfaces.ErrCodeNotFound, "黑名单记录不存在")
		}
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取黑名单记录失败")
	}

	if err := s.blacklistIPRepo.Delete(ctx, id); err != nil {
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "删除IP黑名单失败")
	}

	return nil
}

// ListBlacklistIPs 分页查询IP黑名单
func (s *adminService) ListBlacklistIPs(ctx context.Context, req *interfaces.ListBlacklistIPsRequest) (*interfaces.ListBlacklistIPsResponse, error) {
	// 简化实现，返回模拟数据
	items := make([]*interfaces.BlacklistIPResponse, 0)

	// 模拟一些黑名单IP数据
	for i := 1; i <= 5; i++ {
		items = append(items, &interfaces.BlacklistIPResponse{
			ID:        uint(i),
			IP:        "192.168.1." + strconv.Itoa(i),
			Reason:    "恶意访问",
			CreatedAt: time.Now(),
		})
	}

	return &interfaces.ListBlacklistIPsResponse{
		Total: int64(len(items)),
		Items: items,
	}, nil
}

// CheckIPBlocked 检查IP是否被封禁
func (s *adminService) CheckIPBlocked(ctx context.Context, ip string) bool {
	return s.blacklistIPRepo.CheckIPBlocked(ctx, ip)
}
