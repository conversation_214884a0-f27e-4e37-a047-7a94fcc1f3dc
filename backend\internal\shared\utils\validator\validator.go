package validator

import (
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"strings"
)

// ValidationError 验证错误
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

// Validator 验证器
type Validator struct {
	errors []ValidationError
}

// New 创建新的验证器
func New() *Validator {
	return &Validator{
		errors: make([]ValidationError, 0),
	}
}

// AddError 添加错误
func (v *Validator) AddError(field, message string) {
	v.errors = append(v.errors, ValidationError{
		Field:   field,
		Message: message,
	})
}

// HasErrors 是否有错误
func (v *Validator) HasErrors() bool {
	return len(v.errors) > 0
}

// GetErrors 获取错误列表
func (v *Validator) GetErrors() []ValidationError {
	return v.errors
}

// ToBusinessError 转换为业务错误
func (v *Validator) ToBusinessError() error {
	if !v.HasErrors() {
		return nil
	}

	// 返回第一个错误的消息
	if len(v.errors) > 0 {
		return fmt.Errorf(v.errors[0].Message)
	}

	return fmt.Errorf("验证失败")
}

// GetErrorMap 获取错误映射
func (v *Validator) GetErrorMap() map[string]string {
	errorMap := make(map[string]string)
	for _, err := range v.errors {
		errorMap[err.Field] = err.Message
	}
	return errorMap
}

// Required 必填验证
func (v *Validator) Required(field string, value interface{}) *Validator {
	if isEmpty(value) {
		v.AddError(field, "该字段为必填项")
	}
	return v
}

// MinLength 最小长度验证
func (v *Validator) MinLength(field string, value string, min int) *Validator {
	if len(value) < min {
		v.AddError(field, fmt.Sprintf("长度不能少于%d个字符", min))
	}
	return v
}

// MaxLength 最大长度验证
func (v *Validator) MaxLength(field string, value string, max int) *Validator {
	if len(value) > max {
		v.AddError(field, fmt.Sprintf("长度不能超过%d个字符", max))
	}
	return v
}

// Email 邮箱验证
func (v *Validator) Email(field string, value string) *Validator {
	if value == "" {
		return v
	}
	
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(value) {
		v.AddError(field, "邮箱格式不正确")
	}
	return v
}

// Phone 手机号验证
func (v *Validator) Phone(field string, value string) *Validator {
	if value == "" {
		return v
	}
	
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	if !phoneRegex.MatchString(value) {
		v.AddError(field, "手机号格式不正确")
	}
	return v
}

// Min 最小值验证
func (v *Validator) Min(field string, value interface{}, min float64) *Validator {
	val := toFloat64(value)
	if val < min {
		v.AddError(field, fmt.Sprintf("值不能小于%.2f", min))
	}
	return v
}

// Max 最大值验证
func (v *Validator) Max(field string, value interface{}, max float64) *Validator {
	val := toFloat64(value)
	if val > max {
		v.AddError(field, fmt.Sprintf("值不能大于%.2f", max))
	}
	return v
}

// In 枚举值验证
func (v *Validator) In(field string, value interface{}, validValues ...interface{}) *Validator {
	for _, validValue := range validValues {
		if value == validValue {
			return v
		}
	}
	
	var validStrings []string
	for _, val := range validValues {
		validStrings = append(validStrings, fmt.Sprintf("%v", val))
	}
	
	v.AddError(field, fmt.Sprintf("值必须是以下之一: %s", strings.Join(validStrings, ", ")))
	return v
}

// Pattern 正则表达式验证
func (v *Validator) Pattern(field string, value string, pattern string, message string) *Validator {
	if value == "" {
		return v
	}
	
	regex := regexp.MustCompile(pattern)
	if !regex.MatchString(value) {
		if message == "" {
			message = "格式不正确"
		}
		v.AddError(field, message)
	}
	return v
}

// Custom 自定义验证
func (v *Validator) Custom(field string, condition bool, message string) *Validator {
	if !condition {
		v.AddError(field, message)
	}
	return v
}

// isEmpty 检查值是否为空
func isEmpty(value interface{}) bool {
	if value == nil {
		return true
	}
	
	v := reflect.ValueOf(value)
	switch v.Kind() {
	case reflect.String:
		return v.String() == ""
	case reflect.Slice, reflect.Array, reflect.Map:
		return v.Len() == 0
	case reflect.Ptr:
		return v.IsNil()
	default:
		return false
	}
}

// toFloat64 转换为float64
func toFloat64(value interface{}) float64 {
	switch v := value.(type) {
	case int:
		return float64(v)
	case int32:
		return float64(v)
	case int64:
		return float64(v)
	case float32:
		return float64(v)
	case float64:
		return v
	case string:
		if f, err := strconv.ParseFloat(v, 64); err == nil {
			return f
		}
	}
	return 0
}

// ValidateStruct 结构体验证（简单实现）
func ValidateStruct(s interface{}) map[string]string {
	errors := make(map[string]string)
	
	v := reflect.ValueOf(s)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	
	if v.Kind() != reflect.Struct {
		return errors
	}
	
	t := v.Type()
	for i := 0; i < v.NumField(); i++ {
		field := t.Field(i)
		value := v.Field(i)
		
		// 检查required标签
		if tag := field.Tag.Get("validate"); tag != "" {
			if strings.Contains(tag, "required") && isEmpty(value.Interface()) {
				errors[field.Name] = "该字段为必填项"
			}
		}
	}
	
	return errors
}
