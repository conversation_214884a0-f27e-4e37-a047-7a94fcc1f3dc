package middleware

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"payment-gateway/internal/shared/constants"
	"payment-gateway/pkg/logger"
	"payment-gateway/internal/shared/response"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// CORS 跨域中间件
func CORS() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		
		// 允许的域名列表，生产环境应该配置具体的域名
		allowedOrigins := []string{
			"http://localhost:3000",
			"http://localhost:8080",
			"https://admin.example.com",
			"https://merchant.example.com",
		}
		
		// 检查是否为允许的域名
		allowed := false
		for _, allowedOrigin := range allowedOrigins {
			if origin == allowedOrigin {
				allowed = true
				break
			}
		}
		
		if allowed {
			c.Header("Access-Control-Allow-Origin", origin)
		}
		
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-Request-ID, X-Timestamp, X-Signature")
		c.Header("Access-Control-Expose-Headers", "Content-Length, X-Request-ID")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	})
}

// RequestID 请求ID中间件
func RequestID() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = uuid.New().String()
		}
		
		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)
		c.Next()
	})
}

// Logger 日志中间件
func Logger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] %s %s %d %s %s %s\n",
			param.TimeStamp.Format(constants.TimeFormatDateTime),
			param.Method,
			param.Path,
			param.StatusCode,
			param.Latency,
			param.ClientIP,
			param.ErrorMessage,
		)
	})
}

// Recovery 恢复中间件
func Recovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		requestID := c.GetString("request_id")
		
		logger.Error("系统发生panic",
			zap.String("request_id", requestID),
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.String("client_ip", c.ClientIP()),
			zap.Any("panic", recovered),
		)
		
		response.Error(c, constants.ErrCodeInternalError, "系统内部错误")
	})
}

// APILogger API访问日志中间件
func APILogger() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		start := time.Now()
		requestID := c.GetString("request_id")
		
		// 读取请求体
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}
		
		// 创建响应写入器来捕获响应
		writer := &responseWriter{
			ResponseWriter: c.Writer,
			body:          &bytes.Buffer{},
		}
		c.Writer = writer
		
		c.Next()
		
		duration := time.Since(start)
		
		// 记录API访问日志
		logger.Info("API访问日志",
			zap.String("request_id", requestID),
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.String("query", c.Request.URL.RawQuery),
			zap.String("client_ip", c.ClientIP()),
			zap.String("user_agent", c.Request.UserAgent()),
			zap.Int("status_code", c.Writer.Status()),
			zap.Duration("duration", duration),
			zap.String("request_body", string(requestBody)),
			zap.String("response_body", writer.body.String()),
		)
	})
}

// responseWriter 响应写入器
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// RateLimit 限流中间件
func RateLimit() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// TODO: 实现基于Redis的限流逻辑
		// 这里先简单实现，后续可以根据需要完善
		c.Next()
	})
}

// Auth 认证中间件
func Auth() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		token := c.GetHeader("Authorization")
		if token == "" {
			response.Error(c, constants.ErrCodeUnauthorized, "未授权访问")
			c.Abort()
			return
		}
		
		// 移除Bearer前缀
		if strings.HasPrefix(token, "Bearer ") {
			token = strings.TrimPrefix(token, "Bearer ")
		}
		
		// TODO: 实现JWT token验证逻辑
		// 这里先简单实现，后续需要完善
		if token == "" {
			response.Error(c, constants.ErrCodeTokenInvalid, "Token无效")
			c.Abort()
			return
		}
		
		// 设置用户信息到上下文
		c.Set("user_id", 1)
		c.Set("user_type", constants.UserTypeMerchant)
		
		c.Next()
	})
}

// AdminAuth 管理员认证中间件
func AdminAuth() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		token := c.GetHeader("Authorization")
		if token == "" {
			response.Error(c, constants.ErrCodeUnauthorized, "未授权访问")
			c.Abort()
			return
		}
		
		// 移除Bearer前缀
		if strings.HasPrefix(token, "Bearer ") {
			token = strings.TrimPrefix(token, "Bearer ")
		}
		
		// TODO: 实现管理员JWT token验证逻辑
		if token == "" {
			response.Error(c, constants.ErrCodeTokenInvalid, "Token无效")
			c.Abort()
			return
		}
		
		// 设置管理员信息到上下文
		c.Set("admin_id", 1)
		c.Set("admin_role", "super_admin")
		
		c.Next()
	})
}

// SignatureVerify 签名验证中间件
func SignatureVerify() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// 获取签名相关参数
		timestamp := c.GetHeader("X-Timestamp")
		signature := c.GetHeader("X-Signature")
		appID := c.GetHeader("X-App-ID")
		
		if timestamp == "" || signature == "" || appID == "" {
			response.Error(c, constants.ErrCodeSignatureInvalid, "签名验证失败")
			c.Abort()
			return
		}
		
		// 验证时间戳
		ts, err := strconv.ParseInt(timestamp, 10, 64)
		if err != nil {
			response.Error(c, constants.ErrCodeTimestampExpired, "时间戳过期")
			c.Abort()
			return
		}
		
		// 检查时间戳是否在有效期内（5分钟）
		now := time.Now().Unix()
		if now-ts > 300 || ts-now > 300 {
			response.Error(c, constants.ErrCodeTimestampExpired, "时间戳过期")
			c.Abort()
			return
		}
		
		// TODO: 实现签名验证逻辑
		// 1. 根据appID获取商户密钥
		// 2. 构建签名字符串
		// 3. 计算签名并比较
		
		c.Set("app_id", appID)
		c.Set("timestamp", ts)
		
		c.Next()
	})
}

// IPWhitelist IP白名单中间件
func IPWhitelist() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		clientIP := c.ClientIP()
		
		// TODO: 从数据库或缓存中获取IP白名单
		// 这里先简单实现
		allowedIPs := []string{
			"127.0.0.1",
			"::1",
		}
		
		allowed := false
		for _, ip := range allowedIPs {
			if clientIP == ip {
				allowed = true
				break
			}
		}
		
		if !allowed {
			logger.Warn("IP访问被拒绝",
				zap.String("client_ip", clientIP),
				zap.String("path", c.Request.URL.Path),
			)
			response.Error(c, constants.ErrCodeForbidden, "禁止访问")
			c.Abort()
			return
		}
		
		c.Next()
	})
}

// Timeout 超时中间件
func Timeout(timeout time.Duration) gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// 设置请求超时
		ctx := c.Request.Context()
		ctx, cancel := context.WithTimeout(ctx, timeout)
		defer cancel()
		
		c.Request = c.Request.WithContext(ctx)
		c.Next()
	})
}

// ContentType 内容类型验证中间件
func ContentType(allowedTypes ...string) gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
			contentType := c.GetHeader("Content-Type")
			
			allowed := false
			for _, allowedType := range allowedTypes {
				if strings.Contains(contentType, allowedType) {
					allowed = true
					break
				}
			}
			
			if !allowed {
				response.Error(c, constants.ErrCodeInvalidParams, "不支持的Content-Type")
				c.Abort()
				return
			}
		}
		
		c.Next()
	})
}
