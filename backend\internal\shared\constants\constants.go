package constants

// HTTP状态码常量
const (
	StatusOK                  = 200
	StatusCreated             = 201
	StatusBadRequest          = 400
	StatusUnauthorized        = 401
	StatusForbidden           = 403
	StatusNotFound            = 404
	StatusMethodNotAllowed    = 405
	StatusConflict            = 409
	StatusUnprocessableEntity = 422
	StatusInternalServerError = 500
	StatusBadGateway          = 502
	StatusServiceUnavailable  = 503
)

// 业务错误码常量
const (
	// 通用错误码 (1000-1999)
	ErrCodeSuccess            = 0    // 成功
	ErrCodeInvalidParams      = 1001 // 参数错误
	ErrCodeValidation         = 1001 // 验证错误（别名）
	ErrCodeUnauthorized       = 1002 // 未授权
	ErrCodeForbidden          = 1003 // 禁止访问
	ErrCodeNotFound           = 1004 // 资源不存在
	ErrCodeInternalError      = 1005 // 内部错误
	ErrCodeServiceUnavailable = 1006 // 服务不可用
	ErrCodeRateLimitExceeded  = 1007 // 请求频率超限
	ErrCodeSignatureInvalid   = 1008 // 签名验证失败
	ErrCodeTimestampExpired   = 1009 // 时间戳过期
	ErrCodeConflict           = 1010 // 资源冲突

	// 用户相关错误码 (2000-2999)
	ErrCodeUserNotFound      = 2001 // 用户不存在
	ErrCodeUserExists        = 2002 // 用户已存在
	ErrCodePasswordIncorrect = 2003 // 密码错误
	ErrCodeUserDisabled      = 2004 // 用户已禁用
	ErrCodeUserNotVerified   = 2005 // 用户未验证
	ErrCodeTokenExpired      = 2006 // Token已过期
	ErrCodeTokenInvalid      = 2007 // Token无效
	ErrCodePermissionDenied  = 2008 // 权限不足

	// 商户相关错误码 (3000-3999)
	ErrCodeMerchantNotFound    = 3001 // 商户不存在
	ErrCodeMerchantExists      = 3002 // 商户已存在
	ErrCodeMerchantDisabled    = 3003 // 商户已禁用
	ErrCodeMerchantFrozen      = 3004 // 商户已冻结
	ErrCodeMerchantNotApproved = 3005 // 商户未审核通过
	ErrCodeAppNotFound         = 3006 // 应用不存在
	ErrCodeAppDisabled         = 3007 // 应用已禁用
	ErrCodeBalanceInsufficient = 3008 // 余额不足
	ErrCodeBalanceError        = 3009 // 余额操作失败

	// 支付相关错误码 (4000-4999)
	ErrCodeOrderNotFound       = 4001 // 订单不存在
	ErrCodeOrderExists         = 4002 // 订单已存在
	ErrCodeOrderStatusInvalid  = 4003 // 订单状态无效
	ErrCodeOrderExpired        = 4004 // 订单已过期
	ErrCodeOrderCancelled      = 4005 // 订单已取消
	ErrCodeOrderPaid           = 4006 // 订单已支付
	ErrCodeAmountInvalid       = 4007 // 金额无效
	ErrCodeChannelNotSupported = 4008 // 支付渠道不支持
	ErrCodeChannelDisabled     = 4009 // 支付渠道已禁用
	ErrCodePaymentFailed       = 4010 // 支付失败
	ErrCodeRefundFailed        = 4011 // 退款失败
	ErrCodeRefundNotAllowed    = 4012 // 不允许退款

	// 通知相关错误码 (5000-5999)
	ErrCodeNotificationFailed = 5001 // 通知发送失败
	ErrCodeEmailFailed        = 5002 // 邮件发送失败
	ErrCodeSMSFailed          = 5003 // 短信发送失败
	ErrCodeTemplateNotFound   = 5004 // 模板不存在
	ErrCodeTemplateInvalid    = 5005 // 模板无效

	// 结算相关错误码 (6000-6999)
	ErrCodeSettlementFailed     = 6001 // 结算失败
	ErrCodeWithdrawFailed       = 6002 // 提现失败
	ErrCodeWithdrawNotAllowed   = 6003 // 不允许提现
	ErrCodeReconciliationFailed = 6004 // 对账失败
)

// 用户类型常量
const (
	UserTypeMerchant = "merchant" // 商户用户
	UserTypeAdmin    = "admin"    // 管理员用户
	UserTypeSystem   = "system"   // 系统用户
)

// 用户状态常量
const (
	UserStatusDisabled = 0 // 禁用
	UserStatusEnabled  = 1 // 启用
	UserStatusPending  = 2 // 待审核
)

// 商户状态常量
const (
	MerchantStatusPending  = 0 // 待审核
	MerchantStatusApproved = 1 // 已审核
	MerchantStatusRejected = 2 // 已拒绝
	MerchantStatusDisabled = 3 // 已禁用
)

// 支付渠道常量
const (
	ChannelWechatNative = "wechat_native" // 微信扫码支付
	ChannelWechatJSAPI  = "wechat_jsapi"  // 微信公众号支付
	ChannelWechatH5     = "wechat_h5"     // 微信H5支付
	ChannelWechatApp    = "wechat_app"    // 微信APP支付
	ChannelAlipayNative = "alipay_native" // 支付宝扫码支付
	ChannelAlipayWap    = "alipay_wap"    // 支付宝手机网站支付
	ChannelAlipayApp    = "alipay_app"    // 支付宝APP支付
	ChannelUnionPay     = "unionpay"      // 银联支付
	ChannelQQPay        = "qqpay"         // QQ钱包支付
	ChannelPayPal       = "paypal"        // PayPal支付
	ChannelApplePay     = "applepay"      // Apple Pay
)

// 支付方式常量
const (
	PayMethodNative = "native" // 扫码支付
	PayMethodJSAPI  = "jsapi"  // 公众号支付
	PayMethodH5     = "h5"     // H5支付
	PayMethodApp    = "app"    // APP支付
	PayMethodWap    = "wap"    // 手机网站支付
)

// 订单状态常量
const (
	OrderStatusPending   = 0 // 待支付
	OrderStatusPaid      = 1 // 已支付
	OrderStatusCancelled = 2 // 已取消
	OrderStatusExpired   = 3 // 已过期
	OrderStatusRefunded  = 4 // 已退款
	OrderStatusClosed    = 5 // 已关闭
)

// 退款状态常量
const (
	RefundStatusPending   = 0 // 退款中
	RefundStatusSuccess   = 1 // 退款成功
	RefundStatusFailed    = 2 // 退款失败
	RefundStatusCancelled = 3 // 退款取消
)

// 通知状态常量
const (
	NotifyStatusPending   = 0 // 待发送
	NotifyStatusSuccess   = 1 // 发送成功
	NotifyStatusFailed    = 2 // 发送失败
	NotifyStatusCancelled = 3 // 已取消
)

// 结算状态常量
const (
	SettlementStatusPending    = 0 // 待结算
	SettlementStatusProcessing = 1 // 结算中
	SettlementStatusSuccess    = 2 // 结算成功
	SettlementStatusFailed     = 3 // 结算失败
)

// 提现状态常量
const (
	WithdrawStatusPending    = 0 // 待审核
	WithdrawStatusApproved   = 1 // 审核通过
	WithdrawStatusProcessing = 2 // 处理中
	WithdrawStatusSuccess    = 3 // 提现成功
	WithdrawStatusFailed     = 4 // 提现失败
	WithdrawStatusRejected   = 5 // 审核拒绝
)

// 签名类型常量
const (
	SignTypeMD5 = "MD5" // MD5签名
	SignTypeRSA = "RSA" // RSA签名
)

// 时间格式常量
const (
	TimeFormatDate     = "2006-01-02"                // 日期格式
	TimeFormatDateTime = "2006-01-02 15:04:05"       // 日期时间格式
	TimeFormatISO      = "2006-01-02T15:04:05Z07:00" // ISO格式
)

// 缓存键前缀常量
const (
	CacheKeyUserToken    = "user:token:"    // 用户Token缓存
	CacheKeyUserInfo     = "user:info:"     // 用户信息缓存
	CacheKeyMerchantInfo = "merchant:info:" // 商户信息缓存
	CacheKeyPaymentOrder = "payment:order:" // 支付订单缓存
	CacheKeyRateLimit    = "rate:limit:"    // 限流缓存
	CacheKeySystemConfig = "system:config:" // 系统配置缓存
	CacheKeyNotifyQueue  = "notify:queue:"  // 通知队列缓存
	CacheKeySettlement   = "settlement:"    // 结算缓存
)

// 缓存过期时间常量（秒）
const (
	CacheExpireUserToken    = 7200  // 用户Token缓存2小时
	CacheExpireUserInfo     = 3600  // 用户信息缓存1小时
	CacheExpireMerchantInfo = 3600  // 商户信息缓存1小时
	CacheExpirePaymentOrder = 1800  // 支付订单缓存30分钟
	CacheExpireSystemConfig = 86400 // 系统配置缓存24小时
)

// 队列名称常量
const (
	QueuePaymentNotify = "payment.notify"  // 支付通知队列
	QueueRefundNotify  = "refund.notify"   // 退款通知队列
	QueueEmailSend     = "email.send"      // 邮件发送队列
	QueueSMSSend       = "sms.send"        // 短信发送队列
	QueueSettlement    = "settlement.auto" // 自动结算队列
	QueueReconcile     = "reconcile.auto"  // 自动对账队列
)

// 默认配置常量
const (
	DefaultPageSize     = 20   // 默认分页大小
	MaxPageSize         = 100  // 最大分页大小
	DefaultTimeout      = 30   // 默认超时时间（秒）
	DefaultRetryCount   = 3    // 默认重试次数
	DefaultOrderTimeout = 1800 // 默认订单超时时间（秒）
	DefaultNotifyRetry  = 5    // 默认通知重试次数
)

// 文件上传常量
const (
	MaxFileSize     = 10 * 1024 * 1024                  // 最大文件大小10MB
	AllowedImageExt = ".jpg,.jpeg,.png,.gif,.bmp"       // 允许的图片扩展名
	AllowedDocExt   = ".pdf,.doc,.docx,.xls,.xlsx,.txt" // 允许的文档扩展名
)

// 正则表达式常量
const (
	RegexEmail    = `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	RegexPhone    = `^1[3-9]\d{9}$`
	RegexPassword = `^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$`
	RegexUsername = `^[a-zA-Z0-9_]{3,20}$`
	RegexOrderNo  = `^[A-Z0-9]{20,32}$`
)

// API版本常量
const (
	APIVersionV1 = "v1"
	APIVersionV2 = "v2"
)

// 环境常量
const (
	EnvDevelopment = "development"
	EnvTesting     = "testing"
	EnvProduction  = "production"
)
