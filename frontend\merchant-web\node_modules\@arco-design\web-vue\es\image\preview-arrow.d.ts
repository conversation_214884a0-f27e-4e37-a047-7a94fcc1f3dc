declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    onPrev: {
        type: FunctionConstructor;
    };
    onNext: {
        type: FunctionConstructor;
    };
}>, {
    prefixCls: string;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    onPrev: {
        type: FunctionConstructor;
    };
    onNext: {
        type: FunctionConstructor;
    };
}>> & Readonly<{}>, {}, {}, {
    IconLeft: any;
    IconRight: any;
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
