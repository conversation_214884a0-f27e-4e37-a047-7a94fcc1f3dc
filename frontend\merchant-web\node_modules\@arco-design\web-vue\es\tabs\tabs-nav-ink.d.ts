import type { CSSProperties, PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    activeTabRef: {
        type: PropType<HTMLElement>;
    };
    direction: {
        type: PropType<"horizontal" | "vertical">;
    };
    disabled: BooleanConstructor;
    animation: BooleanConstructor;
}>, {
    prefixCls: string;
    cls: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    style: import("vue").ComputedRef<CSSProperties>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    activeTabRef: {
        type: PropType<HTMLElement>;
    };
    direction: {
        type: PropType<"horizontal" | "vertical">;
    };
    disabled: BooleanConstructor;
    animation: BooleanConstructor;
}>> & <PERSON>only<{}>, {
    disabled: boolean;
    animation: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
