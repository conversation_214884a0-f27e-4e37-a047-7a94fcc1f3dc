declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    level: {
        type: NumberConstructor;
        default: number;
    };
}>, {
    prefixCls: string;
    levelIndent: import("vue").Ref<number | undefined, number | undefined>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    level: {
        type: NumberConstructor;
        default: number;
    };
}>> & Readonly<{}>, {
    level: number;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
