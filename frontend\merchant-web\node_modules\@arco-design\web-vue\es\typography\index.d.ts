import type { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _Typography from './typography';
import _TypographyParagraph from './paragraph';
import _TypographyTitle from './title';
import _TypographyText from './text';
export type { EllipsisConfig } from './interface';
declare const Typography: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<{}> & Readonly<{}>, {
        classNames: string[];
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, import("vue").PublicProps, {}, true, {}, {}, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<{}> & Readonly<{}>, {
        classNames: string[];
    }, {}, {}, {}, {}>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<{}> & Readonly<{}>, {
    classNames: string[];
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, {}, {}, string, {}, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    Paragraph: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        blockquote: {
            type: BooleanConstructor;
        };
        spacing: {
            type: import("vue").PropType<"default" | "close">;
            default: string;
        };
    }>, {
        component: import("vue").ComputedRef<"div" | "blockquote">;
        classNames: import("vue").ComputedRef<{
            [x: string]: boolean;
        }[]>;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        blockquote: {
            type: BooleanConstructor;
        };
        spacing: {
            type: import("vue").PropType<"default" | "close">;
            default: string;
        };
    }>> & Readonly<{}>, {
        blockquote: boolean;
        spacing: "default" | "close";
    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    Title: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        heading: {
            type: import("vue").PropType<1 | 2 | 4 | 3 | 6 | 5>;
            default: number;
        };
    }>, {
        component: import("vue").ComputedRef<keyof HTMLElementTagNameMap>;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        heading: {
            type: import("vue").PropType<1 | 2 | 4 | 3 | 6 | 5>;
            default: number;
        };
    }>> & Readonly<{}>, {
        heading: 1 | 2 | 4 | 3 | 6 | 5;
    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    Text: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        ellipsis: {
            type: import("vue").PropType<boolean | import("./interface").EllipsisConfig>;
            default: boolean;
        };
    }>, {
        component: import("vue").ComputedRef<"div" | "span">;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        ellipsis: {
            type: import("vue").PropType<boolean | import("./interface").EllipsisConfig>;
            default: boolean;
        };
    }>> & Readonly<{}>, {
        ellipsis: boolean | import("./interface").EllipsisConfig;
    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type TypographyInstance = InstanceType<typeof _Typography>;
export declare type TypographyParagraphInstance = InstanceType<typeof _TypographyParagraph>;
export declare type TypographyTitleInstance = InstanceType<typeof _TypographyTitle>;
export declare type TypographyTextInstance = InstanceType<typeof _TypographyText>;
export { _TypographyParagraph as TypographyParagraph, _TypographyTitle as TypographyTitle, _TypographyText as TypographyText, };
export default Typography;
