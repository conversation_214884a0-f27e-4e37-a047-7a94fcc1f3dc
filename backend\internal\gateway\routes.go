package gateway

import (
	"payment-gateway/internal/gateway/handler"
	"payment-gateway/internal/gateway/repository"
	"payment-gateway/internal/gateway/service"
	"payment-gateway/internal/shared/interfaces"
	"payment-gateway/internal/shared/validator"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// RegisterRoutes 注册Gateway域路由
func RegisterRoutes(r *gin.RouterGroup, db *gorm.DB, merchantService interfaces.MerchantService, notificationService interfaces.NotificationService) {
	// 创建Repository层
	paymentOrderRepo := repository.NewPaymentOrderRepository(db)
	refundOrderRepo := repository.NewRefundOrderRepository(db)
	paymentChannelRepo := repository.NewPaymentChannelRepository(db)
	paymentRecordRepo := repository.NewPaymentRecordRepository(db)
	aggregateCodeRepo := repository.NewAggregateCodeRepository(db)
	channelConfigRepo := repository.NewPaymentChannelConfigRepository(db)

	// 创建Service层
	paymentService := service.NewPaymentService(
		db,
		paymentOrderRepo,
		refundOrderRepo,
		paymentChannelRepo,
		paymentRecordRepo,
		merchantService,
		notificationService,
	)

	// 创建聚合码服务实例
	aggregateCodeService := service.NewAggregateCodeService(
		aggregateCodeRepo,
		channelConfigRepo,
		paymentChannelRepo,
	)

	// 创建支付渠道服务实例
	paymentChannelService := service.NewPaymentChannelService(
		aggregateCodeRepo,
		channelConfigRepo,
		paymentOrderRepo,
		paymentRecordRepo,
	)

	// 创建验证器
	validator := validator.NewValidator()

	// 创建Handler层
	paymentHandler := handler.NewPaymentHandler(paymentService)
	aggregateCodeHandler := handler.NewAggregateCodeHandler(aggregateCodeService, paymentChannelService, validator)

	// 注册原有支付路由
	paymentHandler.RegisterRoutes(r)

	// 注册聚合码相关路由
	aggregateGroup := r.Group("/aggregate-codes")
	{
		aggregateGroup.POST("", aggregateCodeHandler.CreateAggregateCode)         // 创建聚合码
		aggregateGroup.GET("/:code_id", aggregateCodeHandler.GetAggregateCode)    // 获取聚合码信息
		aggregateGroup.PUT("/:code_id", aggregateCodeHandler.UpdateAggregateCode) // 更新聚合码
		aggregateGroup.DELETE("/:code_id", aggregateCodeHandler.DeleteAggregateCode) // 删除聚合码
		aggregateGroup.GET("", aggregateCodeHandler.ListAggregateCodes)           // 获取聚合码列表
		aggregateGroup.POST("/smart-route", aggregateCodeHandler.SmartRoute)      // 智能路由
		aggregateGroup.GET("/:code_id/payment-page", aggregateCodeHandler.GetPaymentPage) // 获取支付页面数据
		aggregateGroup.POST("/payment", aggregateCodeHandler.CreatePayment)       // 创建支付订单
	}
}

// GetPaymentService 获取支付服务实例（供其他域使用）
func GetPaymentService(db *gorm.DB, merchantService interfaces.MerchantService, notificationService interfaces.NotificationService) interfaces.PaymentService {
	// 创建Repository层
	paymentOrderRepo := repository.NewPaymentOrderRepository(db)
	refundOrderRepo := repository.NewRefundOrderRepository(db)
	paymentChannelRepo := repository.NewPaymentChannelRepository(db)
	paymentRecordRepo := repository.NewPaymentRecordRepository(db)
	
	// 创建Service层
	return service.NewPaymentService(
		db,
		paymentOrderRepo,
		refundOrderRepo,
		paymentChannelRepo,
		paymentRecordRepo,
		merchantService,
		notificationService,
	)
}
