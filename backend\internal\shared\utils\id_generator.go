package utils

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"strings"
	"time"
)

// GenerateID 生成唯一ID
// prefix: ID前缀，如 "PAY", "REF", "AGG" 等
// 返回格式: PREFIX + YYYYMMDD + HHMMSS + 6位随机数
func GenerateID(prefix string) string {
	now := time.Now()
	
	// 格式化时间部分
	datePart := now.Format("20060102")   // YYYYMMDD
	timePart := now.Format("150405")     // HHMMSS
	
	// 生成6位随机数
	randomPart := generateRandomNumber(6)
	
	// 组合ID
	id := fmt.Sprintf("%s%s%s%s", strings.ToUpper(prefix), datePart, timePart, randomPart)
	
	return id
}

// GenerateOrderNo 生成订单号
// 格式: YYYYMMDDHHMMSS + 8位随机数
func GenerateOrderNo() string {
	now := time.Now()
	
	// 格式化时间部分
	timePart := now.Format("20060102150405") // YYYYMMDDHHMMSS
	
	// 生成8位随机数
	randomPart := generateRandomNumber(8)
	
	// 组合订单号
	orderNo := fmt.Sprintf("%s%s", timePart, randomPart)
	
	return orderNo
}

// GenerateRandomString 生成指定长度的随机字符串
// length: 字符串长度
// 字符集: 0-9, A-Z, a-z
func GenerateRandomString(length int) string {
	const charset = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
	
	result := make([]byte, length)
	charsetLen := big.NewInt(int64(len(charset)))
	
	for i := range result {
		randomIndex, err := rand.Int(rand.Reader, charsetLen)
		if err != nil {
			// 如果随机数生成失败，使用时间戳作为后备方案
			result[i] = charset[time.Now().UnixNano()%int64(len(charset))]
		} else {
			result[i] = charset[randomIndex.Int64()]
		}
	}
	
	return string(result)
}

// GenerateRandomNumber 生成指定长度的随机数字字符串
// length: 数字字符串长度
func GenerateRandomNumber(length int) string {
	const charset = "0123456789"
	
	result := make([]byte, length)
	charsetLen := big.NewInt(int64(len(charset)))
	
	for i := range result {
		randomIndex, err := rand.Int(rand.Reader, charsetLen)
		if err != nil {
			// 如果随机数生成失败，使用时间戳作为后备方案
			result[i] = charset[time.Now().UnixNano()%int64(len(charset))]
		} else {
			result[i] = charset[randomIndex.Int64()]
		}
	}
	
	return string(result)
}

// generateRandomNumber 内部方法，生成指定长度的随机数字字符串
func generateRandomNumber(length int) string {
	return GenerateRandomNumber(length)
}

// GenerateNonce 生成随机字符串（用于签名等场景）
// length: 字符串长度，默认32位
func GenerateNonce(length ...int) string {
	l := 32
	if len(length) > 0 && length[0] > 0 {
		l = length[0]
	}
	
	return GenerateRandomString(l)
}

// GenerateTraceID 生成追踪ID
// 格式: 时间戳(10位) + 随机字符串(22位)，总长度32位
func GenerateTraceID() string {
	timestamp := fmt.Sprintf("%010d", time.Now().Unix())
	randomPart := GenerateRandomString(22)
	
	return timestamp + randomPart
}

// IsValidID 验证ID格式是否有效
// 检查ID是否符合生成规则
func IsValidID(id string, prefix string) bool {
	if len(id) < len(prefix)+14 { // prefix + YYYYMMDD + HHMMSS 最少长度
		return false
	}
	
	// 检查前缀
	if !strings.HasPrefix(strings.ToUpper(id), strings.ToUpper(prefix)) {
		return false
	}
	
	// 检查时间部分格式
	timePart := id[len(prefix) : len(prefix)+14] // YYYYMMDDHHMMSS
	if _, err := time.Parse("20060102150405", timePart); err != nil {
		return false
	}
	
	return true
}

// ParseIDTimestamp 从ID中解析时间戳
func ParseIDTimestamp(id string, prefix string) (time.Time, error) {
	if !IsValidID(id, prefix) {
		return time.Time{}, fmt.Errorf("invalid ID format")
	}
	
	timePart := id[len(prefix) : len(prefix)+14] // YYYYMMDDHHMMSS
	return time.Parse("20060102150405", timePart)
}

// RandomString 生成指定长度的随机字符串（别名）
func RandomString(length int) string {
	return GenerateRandomString(length)
}
