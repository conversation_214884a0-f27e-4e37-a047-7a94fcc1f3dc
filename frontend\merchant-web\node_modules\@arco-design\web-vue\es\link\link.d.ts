import type { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    href: StringConstructor;
    status: {
        type: PropType<"normal" | "success" | "warning" | "danger">;
        default: string;
    };
    hoverable: {
        type: BooleanConstructor;
        default: boolean;
    };
    icon: BooleanConstructor;
    loading: BooleanConstructor;
    disabled: BooleanConstructor;
}>, {
    cls: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    prefixCls: string;
    showIcon: import("vue").ComputedRef<boolean>;
    handleClick: (ev: MouseEvent) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    click: (ev: MouseEvent) => true;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    href: StringConstructor;
    status: {
        type: PropType<"normal" | "success" | "warning" | "danger">;
        default: string;
    };
    hoverable: {
        type: BooleanConstructor;
        default: boolean;
    };
    icon: BooleanConstructor;
    loading: BooleanConstructor;
    disabled: BooleanConstructor;
}>> & Readonly<{
    onClick?: ((ev: MouseEvent) => any) | undefined;
}>, {
    disabled: boolean;
    icon: boolean;
    loading: boolean;
    status: "normal" | "success" | "warning" | "danger";
    hoverable: boolean;
}, {}, {
    IconLink: any;
    IconLoading: any;
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
