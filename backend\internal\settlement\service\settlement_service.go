package service

import (
	"context"
	"crypto/md5"
	"fmt"
	"payment-gateway/internal/settlement/model"
	"payment-gateway/internal/settlement/repository"
	"payment-gateway/internal/shared/interfaces"
	"payment-gateway/internal/shared/utils/logger"
	"payment-gateway/internal/shared/utils/validator"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// SettlementService 结算服务实现
type settlementService struct {
	db                  *gorm.DB
	settlementOrderRepo repository.SettlementOrderRepository
	withdrawRecordRepo  repository.WithdrawRecordRepository
	reconciliationRepo  repository.ReconciliationRepository
	merchantService     interfaces.MerchantService
	notificationService interfaces.NotificationService
}

// NewSettlementService 创建结算服务
func NewSettlementService(
	db *gorm.DB,
	settlementOrderRepo repository.SettlementOrderRepository,
	withdrawRecordRepo repository.WithdrawRecordRepository,
	reconciliationRepo repository.ReconciliationRepository,
	merchantService interfaces.MerchantService,
	notificationService interfaces.NotificationService,
) interfaces.SettlementService {
	return &settlementService{
		db:                  db,
		settlementOrderRepo: settlementOrderRepo,
		withdrawRecordRepo:  withdrawRecordRepo,
		reconciliationRepo:  reconciliationRepo,
		merchantService:     merchantService,
		notificationService: notificationService,
	}
}

// CreateSettlement 创建结算订单
func (s *settlementService) CreateSettlement(ctx context.Context, req *interfaces.CreateSettlementRequest) (*interfaces.SettlementResponse, error) {
	// 验证请求参数
	if err := s.validateCreateSettlementRequest(req); err != nil {
		return nil, err
	}

	// 检查商户余额
	balance, err := s.merchantService.GetMerchantBalance(ctx, req.MerchantID)
	if err != nil {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取商户余额失败")
	}

	if int64(balance.AvailableBalance) < req.SettleAmount {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeValidation, "可用余额不足")
	}

	// 生成结算单号
	settlementNo := s.generateSettlementNo(req.MerchantID)

	// 计算手续费
	feeAmountFloat := s.calculateSettlementFee(float64(req.SettleAmount), req.SettleType)
	feeAmount := int64(feeAmountFloat)
	actualAmount := req.SettleAmount - feeAmount

	// 创建结算订单
	order := &model.SettlementOrder{
		SettlementNo: settlementNo,
		MerchantID:   req.MerchantID,
		SettleAmount: req.SettleAmount,
		FeeAmount:    feeAmount,
		ActualAmount: actualAmount,
		SettleType:   req.SettleType,
		BankCode:     req.BankCode,
		BankAccount:  req.BankAccount,
		BankName:     req.BankName,
		AccountName:  req.AccountName,
		Status:       model.SettlementStatusPending,
		Remark:       req.Remark,
	}

	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建结算订单
	if err := s.settlementOrderRepo.Create(ctx, order); err != nil {
		tx.Rollback()
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "创建结算订单失败")
	}

	// 冻结商户余额
	if err := s.merchantService.FreezeBalance(ctx, req.MerchantID, float64(req.SettleAmount), "结算冻结", settlementNo); err != nil {
		tx.Rollback()
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "冻结余额失败")
	}

	tx.Commit()

	// 异步发送通知
	go func() {
		if err := s.notificationService.SendPaymentNotification(context.Background(), settlementNo, 4); err != nil {
			logger.Error(context.Background(), "发送结算通知失败", zap.String("settlement_no", settlementNo), zap.Error(err))
		}
	}()

	return &interfaces.SettlementResponse{
		ID:           order.ID,
		SettlementNo: settlementNo,
		MerchantID:   req.MerchantID,
		Amount:       float64(req.SettleAmount),
		Fee:          float64(feeAmount),
		ActualAmount: float64(actualAmount),
		Status:       model.SettlementStatusPending,
		CreatedAt:    order.CreatedAt,
	}, nil
}

// ProcessSettlement 处理结算订单
func (s *settlementService) ProcessSettlement(ctx context.Context, settlementNo string) error {
	// 获取结算订单
	order, err := s.settlementOrderRepo.GetBySettlementNo(ctx, settlementNo)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return interfaces.NewBusinessError(interfaces.ErrCodeNotFound, "结算订单不存在")
		}
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取结算订单失败")
	}

	// 检查订单状态
	if order.Status != model.SettlementStatusPending {
		return interfaces.NewBusinessError(interfaces.ErrCodeValidation, "订单状态不允许处理")
	}

	// 更新状态为处理中
	if err := s.settlementOrderRepo.UpdateStatus(ctx, settlementNo, model.SettlementStatusProcessing); err != nil {
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "更新订单状态失败")
	}

	// TODO: 调用银行API进行实际转账
	// 这里模拟银行转账处理
	success := s.processBankTransfer(order)

	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if success {
		// 转账成功，更新订单状态
		if err := s.settlementOrderRepo.UpdateStatus(ctx, settlementNo, model.SettlementStatusSuccess); err != nil {
			tx.Rollback()
			return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "更新订单状态失败")
		}

		// 扣减冻结余额
		if err := s.merchantService.DeductFrozenBalance(ctx, order.MerchantID, float64(order.SettleAmount), "结算成功", settlementNo); err != nil {
			tx.Rollback()
			return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "扣减冻结余额失败")
		}
	} else {
		// 转账失败，更新订单状态
		if err := s.settlementOrderRepo.UpdateStatus(ctx, settlementNo, model.SettlementStatusFailed); err != nil {
			tx.Rollback()
			return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "更新订单状态失败")
		}

		// 解冻余额
		if err := s.merchantService.UnfreezeBalance(ctx, order.MerchantID, float64(order.SettleAmount), "结算失败", settlementNo); err != nil {
			tx.Rollback()
			return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "解冻余额失败")
		}
	}

	tx.Commit()

	// 异步发送通知
	go func() {
		notifyType := 5 // 结算成功通知
		if !success {
			notifyType = 6 // 结算失败通知
		}
		if err := s.notificationService.SendPaymentNotification(context.Background(), settlementNo, notifyType); err != nil {
			logger.Error(context.Background(), "发送结算结果通知失败", zap.String("settlement_no", settlementNo), zap.Error(err))
		}
	}()

	return nil
}

// CreateWithdraw 创建提现申请
func (s *settlementService) CreateWithdraw(ctx context.Context, req *interfaces.CreateWithdrawRequest) (*interfaces.WithdrawResponse, error) {
	// 验证请求参数
	if err := s.validateCreateWithdrawRequest(req); err != nil {
		return nil, err
	}

	// 风控检查
	if err := s.performWithdrawRiskCheck(ctx, req); err != nil {
		return nil, err
	}

	// 检查商户余额
	balance, err := s.merchantService.GetMerchantBalance(ctx, req.MerchantID)
	if err != nil {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取商户余额失败")
	}

	if balance.AvailableBalance < req.Amount {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeValidation, "可用余额不足")
	}

	// 生成提现单号
	withdrawNo := s.generateWithdrawNo(req.MerchantID)

	// 计算手续费
	feeAmount := s.calculateWithdrawFee(req.Amount, 1) // 默认提现类型为1
	actualAmount := req.Amount - feeAmount

	// 创建提现记录
	record := &model.WithdrawRecord{
		WithdrawNo:   withdrawNo,
		MerchantID:   req.MerchantID,
		Amount:       int64(req.Amount * 100),   // 转换为分
		FeeAmount:    int64(feeAmount * 100),    // 转换为分
		ActualAmount: int64(actualAmount * 100), // 转换为分
		WithdrawType: 1,                         // 默认提现类型
		BankCode:     req.BankAccount,           // 使用BankAccount作为BankCode
		BankAccount:  req.BankAccount,
		BankName:     req.BankName,
		AccountName:  req.AccountName,
		Status:       model.WithdrawStatusPending,
		Remark:       req.Remark,
	}

	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建提现记录
	if err := s.withdrawRecordRepo.Create(ctx, record); err != nil {
		tx.Rollback()
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "创建提现记录失败")
	}

	// 冻结商户余额
	if err := s.merchantService.FreezeBalance(ctx, req.MerchantID, req.Amount, "提现冻结", withdrawNo); err != nil {
		tx.Rollback()
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "冻结余额失败")
	}

	tx.Commit()

	return &interfaces.WithdrawResponse{
		WithdrawNo:   withdrawNo,
		MerchantID:   req.MerchantID,
		Amount:       req.Amount,
		FeeAmount:    feeAmount,
		ActualAmount: actualAmount,
		Status:       model.WithdrawStatusPending,
		CreatedAt:    record.CreatedAt,
	}, nil
}

// ProcessWithdraw 处理提现申请
func (s *settlementService) ProcessWithdraw(ctx context.Context, withdrawNo string) error {
	// 获取提现记录
	record, err := s.withdrawRecordRepo.GetByWithdrawNo(ctx, withdrawNo)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return interfaces.NewBusinessError(interfaces.ErrCodeNotFound, "提现记录不存在")
		}
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取提现记录失败")
	}

	// 检查记录状态
	if record.Status != model.WithdrawStatusPending {
		return interfaces.NewBusinessError(interfaces.ErrCodeValidation, "提现状态不允许处理")
	}

	// 更新状态为处理中
	if err := s.withdrawRecordRepo.UpdateStatus(ctx, withdrawNo, model.WithdrawStatusProcessing); err != nil {
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "更新提现状态失败")
	}

	// TODO: 调用银行API进行实际转账
	// 这里模拟银行转账处理
	success := s.processBankWithdraw(record)

	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if success {
		// 转账成功，更新记录状态
		if err := s.withdrawRecordRepo.UpdateStatus(ctx, withdrawNo, model.WithdrawStatusSuccess); err != nil {
			tx.Rollback()
			return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "更新提现状态失败")
		}

		// 扣减冻结余额
		if err := s.merchantService.DeductFrozenBalance(ctx, record.MerchantID, record.Amount, "提现成功", withdrawNo); err != nil {
			tx.Rollback()
			return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "扣减冻结余额失败")
		}
	} else {
		// 转账失败，更新记录状态
		if err := s.withdrawRecordRepo.UpdateStatus(ctx, withdrawNo, model.WithdrawStatusFailed); err != nil {
			tx.Rollback()
			return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "更新提现状态失败")
		}

		// 解冻余额
		if err := s.merchantService.UnfreezeBalance(ctx, record.MerchantID, record.Amount, "提现失败", withdrawNo); err != nil {
			tx.Rollback()
			return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "解冻余额失败")
		}
	}

	tx.Commit()

	return nil
}

// CreateReconciliation 创建对账记录
func (s *settlementService) CreateReconciliation(ctx context.Context, req *interfaces.CreateReconciliationRequest) (*interfaces.ReconciliationResponse, error) {
	// 验证请求参数
	if err := s.validateCreateReconciliationRequest(req); err != nil {
		return nil, err
	}

	// 生成对账单号
	reconciliationNo := s.generateReconciliationNo(req.MerchantID, req.Channel)

	// 计算差异金额
	differenceAmount := req.PlatformAmount - req.ChannelAmount

	// 创建对账记录
	record := &model.ReconciliationRecord{
		ReconciliationNo: reconciliationNo,
		MerchantID:       req.MerchantID,
		Channel:          req.Channel,
		ReconcileDate:    req.ReconcileDate,
		ReconcileType:    req.ReconcileType,
		TotalCount:       req.TotalCount,
		TotalAmount:      req.TotalAmount,
		PlatformCount:    req.PlatformCount,
		PlatformAmount:   req.PlatformAmount,
		ChannelCount:     req.ChannelCount,
		ChannelAmount:    req.ChannelAmount,
		DifferenceAmount: differenceAmount,
		Status:           model.ReconciliationStatusPending,
		Remark:           req.Remark,
	}

	// 创建对账记录
	if err := s.reconciliationRepo.Create(ctx, record); err != nil {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "创建对账记录失败")
	}

	return &interfaces.ReconciliationResponse{
		ReconciliationNo: reconciliationNo,
		MerchantID:       req.MerchantID,
		Channel:          req.Channel,
		ReconcileDate:    req.ReconcileDate,
		DifferenceAmount: differenceAmount,
		Status:           model.ReconciliationStatusPending,
		CreatedAt:        record.CreatedAt,
	}, nil
}

// ProcessReconciliation 处理对账记录
func (s *settlementService) ProcessReconciliation(ctx context.Context, reconciliationNo string) error {
	// 获取对账记录
	record, err := s.reconciliationRepo.GetByReconciliationNo(ctx, reconciliationNo)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return interfaces.NewBusinessError(interfaces.ErrCodeNotFound, "对账记录不存在")
		}
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取对账记录失败")
	}

	// 检查记录状态
	if record.Status != model.ReconciliationStatusPending {
		return interfaces.NewBusinessError(interfaces.ErrCodeValidation, "对账状态不允许处理")
	}

	// 更新状态为处理中
	if err := s.reconciliationRepo.UpdateStatus(ctx, reconciliationNo, model.ReconciliationStatusProcessing); err != nil {
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "更新对账状态失败")
	}

	// 处理对账差异
	success := s.processReconciliationDifference(record)

	// 更新最终状态
	finalStatus := model.ReconciliationStatusSuccess
	if !success {
		finalStatus = model.ReconciliationStatusFailed
	}

	if err := s.reconciliationRepo.UpdateStatus(ctx, reconciliationNo, finalStatus); err != nil {
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "更新对账状态失败")
	}

	return nil
}

// GetSettlementStats 获取结算统计
func (s *settlementService) GetSettlementStats(ctx context.Context, merchantID uint, startDate, endDate time.Time) (*interfaces.SettlementStatsResponse, error) {
	stats, err := s.settlementOrderRepo.GetSettlementStats(ctx, merchantID, startDate, endDate)
	if err != nil {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取结算统计失败")
	}

	return &interfaces.SettlementStatsResponse{
		TotalCount:    stats.TotalCount,
		PendingCount:  stats.PendingCount,
		SuccessCount:  stats.SuccessCount,
		FailedCount:   stats.FailedCount,
		TotalAmount:   stats.TotalAmount,
		SettledAmount: stats.SettledAmount,
		PendingAmount: stats.PendingAmount,
		SuccessRate:   stats.SuccessRate,
	}, nil
}

// GetWithdrawStats 获取提现统计
func (s *settlementService) GetWithdrawStats(ctx context.Context, merchantID uint, startDate, endDate time.Time) (*interfaces.WithdrawStatsResponse, error) {
	stats, err := s.withdrawRecordRepo.GetWithdrawStats(ctx, merchantID, startDate, endDate)
	if err != nil {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取提现统计失败")
	}

	return &interfaces.WithdrawStatsResponse{
		TotalCount:    stats.TotalCount,
		PendingCount:  stats.PendingCount,
		SuccessCount:  stats.SuccessCount,
		FailedCount:   stats.FailedCount,
		TotalAmount:   stats.TotalAmount,
		SuccessAmount: stats.SuccessAmount,
		PendingAmount: stats.PendingAmount,
		SuccessRate:   stats.SuccessRate,
		AvgAmount:     stats.AvgAmount,
	}, nil
}

// GetReconciliationStats 获取对账统计
func (s *settlementService) GetReconciliationStats(ctx context.Context, merchantID uint, startDate, endDate time.Time) (*interfaces.ReconciliationStatsResponse, error) {
	stats, err := s.reconciliationRepo.GetReconciliationStats(ctx, merchantID, startDate, endDate)
	if err != nil {
		return nil, interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取对账统计失败")
	}

	return &interfaces.ReconciliationStatsResponse{
		TotalCount:       stats.TotalCount,
		SuccessCount:     stats.SuccessCount,
		FailedCount:      stats.FailedCount,
		DifferenceCount:  stats.DifferenceCount,
		TotalAmount:      stats.TotalAmount,
		PlatformAmount:   stats.PlatformAmount,
		ChannelAmount:    stats.ChannelAmount,
		DifferenceAmount: stats.DifferenceAmount,
		SuccessRate:      stats.SuccessRate,
		DifferenceRate:   stats.DifferenceRate,
	}, nil
}

// 验证创建结算请求
func (s *settlementService) validateCreateSettlementRequest(req *interfaces.CreateSettlementRequest) error {
	v := validator.New()

	v.Check(req.MerchantID > 0, "merchant_id", "商户ID不能为空")
	v.Check(req.SettleAmount > 0, "settle_amount", "结算金额必须大于0")
	v.Check(req.SettleType >= 0, "settle_type", "结算类型无效")
	v.Check(req.BankCode != "", "bank_code", "银行代码不能为空")
	v.Check(req.BankAccount != "", "bank_account", "银行账号不能为空")
	v.Check(req.BankName != "", "bank_name", "银行名称不能为空")
	v.Check(req.AccountName != "", "account_name", "账户名称不能为空")

	if !v.Valid() {
		return interfaces.NewValidationError(v.Errors)
	}

	return nil
}

// 验证创建提现请求
func (s *settlementService) validateCreateWithdrawRequest(req *interfaces.CreateWithdrawRequest) error {
	v := validator.New()

	v.Check(req.MerchantID > 0, "merchant_id", "商户ID不能为空")
	v.Check(req.Amount > 0, "amount", "提现金额必须大于0")
	v.Check(req.WithdrawType >= 0, "withdraw_type", "提现类型无效")
	v.Check(req.BankCode != "", "bank_code", "银行代码不能为空")
	v.Check(req.BankAccount != "", "bank_account", "银行账号不能为空")
	v.Check(req.BankName != "", "bank_name", "银行名称不能为空")
	v.Check(req.AccountName != "", "account_name", "账户名称不能为空")

	if !v.Valid() {
		return interfaces.NewValidationError(v.Errors)
	}

	return nil
}

// 验证创建对账请求
func (s *settlementService) validateCreateReconciliationRequest(req *interfaces.CreateReconciliationRequest) error {
	v := validator.New()

	v.Check(req.MerchantID > 0, "merchant_id", "商户ID不能为空")
	v.Check(req.Channel != "", "channel", "渠道不能为空")
	v.Check(!req.ReconcileDate.IsZero(), "reconcile_date", "对账日期不能为空")
	v.Check(req.ReconcileType >= 0, "reconcile_type", "对账类型无效")
	v.Check(req.TotalCount >= 0, "total_count", "总笔数不能为负数")
	v.Check(req.TotalAmount >= 0, "total_amount", "总金额不能为负数")
	v.Check(req.PlatformCount >= 0, "platform_count", "平台笔数不能为负数")
	v.Check(req.PlatformAmount >= 0, "platform_amount", "平台金额不能为负数")
	v.Check(req.ChannelCount >= 0, "channel_count", "渠道笔数不能为负数")
	v.Check(req.ChannelAmount >= 0, "channel_amount", "渠道金额不能为负数")

	if !v.Valid() {
		return interfaces.NewValidationError(v.Errors)
	}

	return nil
}

// 生成结算单号
func (s *settlementService) generateSettlementNo(merchantID uint) string {
	timestamp := time.Now().Format("20060102150405")
	hash := md5.Sum([]byte(fmt.Sprintf("%d%s", merchantID, timestamp)))
	return fmt.Sprintf("ST%s%x", timestamp, hash[:4])
}

// 生成提现单号
func (s *settlementService) generateWithdrawNo(merchantID uint) string {
	timestamp := time.Now().Format("20060102150405")
	hash := md5.Sum([]byte(fmt.Sprintf("%d%s", merchantID, timestamp)))
	return fmt.Sprintf("WD%s%x", timestamp, hash[:4])
}

// 生成对账单号
func (s *settlementService) generateReconciliationNo(merchantID uint, channel string) string {
	timestamp := time.Now().Format("20060102150405")
	hash := md5.Sum([]byte(fmt.Sprintf("%d%s%s", merchantID, channel, timestamp)))
	return fmt.Sprintf("RC%s%x", timestamp, hash[:4])
}

// 计算结算手续费
func (s *settlementService) calculateSettlementFee(amount float64, settleType int) float64 {
	// 根据结算类型计算手续费
	switch settleType {
	case 0: // T+0结算
		return amount * 0.006 // 0.6%
	case 1: // T+1结算
		return amount * 0.003 // 0.3%
	default:
		return amount * 0.003
	}
}

// 计算提现手续费
func (s *settlementService) calculateWithdrawFee(amount float64, withdrawType int) float64 {
	// 根据提现类型计算手续费
	switch withdrawType {
	case 0: // 实时提现
		return 5.0 // 固定5元
	case 1: // 普通提现
		return 2.0 // 固定2元
	default:
		return 2.0
	}
}

// 提现风控检查
func (s *settlementService) performWithdrawRiskCheck(ctx context.Context, req *interfaces.CreateWithdrawRequest) error {
	// 检查重复提现
	if s.withdrawRecordRepo.CheckDuplicateWithdraw(ctx, req.MerchantID, req.Amount, 5) {
		return interfaces.NewBusinessError(interfaces.ErrCodeValidation, "5分钟内不能重复提现相同金额")
	}

	// 检查日提现限额
	today := time.Now()
	dailyAmount, err := s.withdrawRecordRepo.GetDailyWithdrawAmount(ctx, req.MerchantID, today)
	if err != nil {
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取日提现金额失败")
	}

	if dailyAmount+req.Amount > 100000 { // 日限额10万
		return interfaces.NewBusinessError(interfaces.ErrCodeValidation, "超过日提现限额")
	}

	return nil
}

// 模拟银行转账处理
func (s *settlementService) processBankTransfer(order *model.SettlementOrder) bool {
	// 这里应该调用实际的银行API
	// 目前模拟90%成功率
	return time.Now().UnixNano()%10 < 9
}

// 模拟银行提现处理
func (s *settlementService) processBankWithdraw(record *model.WithdrawRecord) bool {
	// 这里应该调用实际的银行API
	// 目前模拟90%成功率
	return time.Now().UnixNano()%10 < 9
}

// 处理对账差异
func (s *settlementService) processReconciliationDifference(record *model.ReconciliationRecord) bool {
	// 如果没有差异，直接成功
	if record.DifferenceAmount == 0 {
		return true
	}

	// 如果有差异，需要人工处理
	// 这里模拟处理结果
	return true
}
