import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<{
    component: string;
    direction: "horizontal" | "vertical";
    size: string | number | undefined;
    defaultSize: string | number;
    min: string | number | undefined;
    max: string | number | undefined;
    disabled: boolean;
}, {
    prefixCls: string;
    classNames: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    isHorizontal: import("vue").ComputedRef<boolean>;
    wrapperRef: import("vue").Ref<HTMLDivElement | undefined, HTMLDivElement | undefined>;
    onMoveStart: (e: MouseEvent) => Promise<void>;
    onTriggerResize: (entry: ResizeObserverEntry) => void;
    firstPaneStyles: import("vue").ComputedRef<{
        flex: string;
    }>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    moveStart: (ev: MouseEvent) => true;
    moving: (ev: MouseEvent) => true;
    moveEnd: (ev: MouseEvent) => true;
    'update:size': (size: number | string) => true;
}, string, import("vue").PublicProps, Readonly<{
    component: string;
    direction: "horizontal" | "vertical";
    size: string | number | undefined;
    defaultSize: string | number;
    min: string | number | undefined;
    max: string | number | undefined;
    disabled: boolean;
}> & Readonly<{
    onMoving?: ((ev: MouseEvent) => any) | undefined;
    onMoveStart?: ((ev: MouseEvent) => any) | undefined;
    onMoveEnd?: ((ev: MouseEvent) => any) | undefined;
    "onUpdate:size"?: ((size: string | number) => any) | undefined;
}>, {
    disabled: boolean;
    size: string | number;
    direction: "horizontal" | "vertical";
    component: string;
    defaultSize: string | number;
}, {}, {
    ResizeTrigger: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        prefixCls: {
            type: StringConstructor;
            required: true;
        };
        direction: {
            type: PropType<"horizontal" | "vertical">;
            default: string;
        };
    }>, {
        classNames: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        onResize: (entry: ResizeObserverEntry) => void;
        isHorizontal: import("vue").ComputedRef<boolean>;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "resize"[], "resize", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        prefixCls: {
            type: StringConstructor;
            required: true;
        };
        direction: {
            type: PropType<"horizontal" | "vertical">;
            default: string;
        };
    }>> & Readonly<{
        onResize?: ((...args: any[]) => any) | undefined;
    }>, {
        direction: "horizontal" | "vertical";
    }, {}, {
        ResizeObserver: import("vue").DefineComponent<{}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
            [key: string]: any;
        }> | null, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "resize"[], "resize", import("vue").PublicProps, Readonly<{}> & Readonly<{
            onResize?: ((...args: any[]) => any) | undefined;
        }>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        IconDragDot: any;
        IconDragDotVertical: any;
    }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
