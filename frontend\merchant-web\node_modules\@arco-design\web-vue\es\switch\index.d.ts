import type { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _Switch from './switch';
declare const Switch: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
        modelValue: {
            type: (BooleanConstructor | StringConstructor | NumberConstructor)[];
            default: undefined;
        };
        defaultChecked: {
            type: BooleanConstructor;
            default: boolean;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
        loading: {
            type: BooleanConstructor;
            default: boolean;
        };
        type: {
            type: import("vue").PropType<"round" | "circle" | "line">;
            default: string;
        };
        size: {
            type: import("vue").PropType<"medium" | "small">;
        };
        checkedValue: {
            type: (BooleanConstructor | StringConstructor | NumberConstructor)[];
            default: boolean;
        };
        uncheckedValue: {
            type: (BooleanConstructor | StringConstructor | NumberConstructor)[];
            default: boolean;
        };
        checkedColor: {
            type: StringConstructor;
        };
        uncheckedColor: {
            type: StringConstructor;
        };
        beforeChange: {
            type: import("vue").PropType<(newValue: string | number | boolean) => boolean | void | Promise<boolean | void>>;
        };
        checkedText: {
            type: StringConstructor;
        };
        uncheckedText: {
            type: StringConstructor;
        };
    }>> & Readonly<{
        onFocus?: ((ev: FocusEvent) => any) | undefined;
        onChange?: ((value: string | number | boolean, ev: Event) => any) | undefined;
        onBlur?: ((ev: FocusEvent) => any) | undefined;
        "onUpdate:modelValue"?: ((value: string | number | boolean) => any) | undefined;
    }>, {
        prefixCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: string | boolean | undefined;
        })[]>;
        mergedDisabled: import("vue").ComputedRef<boolean>;
        buttonStyle: import("vue").ComputedRef<{
            '--custom-color': string;
            backgroundColor?: undefined;
        } | {
            backgroundColor: string;
            '--custom-color'?: undefined;
        } | undefined>;
        computedCheck: import("vue").ComputedRef<boolean>;
        computedLoading: import("vue").ComputedRef<boolean>;
        handleClick: (ev: Event) => Promise<void>;
        handleFocus: (ev: FocusEvent) => void;
        handleBlur: (ev: FocusEvent) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        'update:modelValue': (value: string | number | boolean) => true;
        change: (value: string | number | boolean, ev: Event) => true;
        focus: (ev: FocusEvent) => true;
        blur: (ev: FocusEvent) => true;
    }, import("vue").PublicProps, {
        disabled: boolean;
        type: "round" | "circle" | "line";
        modelValue: string | number | boolean;
        loading: boolean;
        defaultChecked: boolean;
        checkedValue: string | number | boolean;
        uncheckedValue: string | number | boolean;
    }, true, {}, {}, {
        IconLoading: any;
    } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        modelValue: {
            type: (BooleanConstructor | StringConstructor | NumberConstructor)[];
            default: undefined;
        };
        defaultChecked: {
            type: BooleanConstructor;
            default: boolean;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
        loading: {
            type: BooleanConstructor;
            default: boolean;
        };
        type: {
            type: import("vue").PropType<"round" | "circle" | "line">;
            default: string;
        };
        size: {
            type: import("vue").PropType<"medium" | "small">;
        };
        checkedValue: {
            type: (BooleanConstructor | StringConstructor | NumberConstructor)[];
            default: boolean;
        };
        uncheckedValue: {
            type: (BooleanConstructor | StringConstructor | NumberConstructor)[];
            default: boolean;
        };
        checkedColor: {
            type: StringConstructor;
        };
        uncheckedColor: {
            type: StringConstructor;
        };
        beforeChange: {
            type: import("vue").PropType<(newValue: string | number | boolean) => boolean | void | Promise<boolean | void>>;
        };
        checkedText: {
            type: StringConstructor;
        };
        uncheckedText: {
            type: StringConstructor;
        };
    }>> & Readonly<{
        onFocus?: ((ev: FocusEvent) => any) | undefined;
        onChange?: ((value: string | number | boolean, ev: Event) => any) | undefined;
        onBlur?: ((ev: FocusEvent) => any) | undefined;
        "onUpdate:modelValue"?: ((value: string | number | boolean) => any) | undefined;
    }>, {
        prefixCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: string | boolean | undefined;
        })[]>;
        mergedDisabled: import("vue").ComputedRef<boolean>;
        buttonStyle: import("vue").ComputedRef<{
            '--custom-color': string;
            backgroundColor?: undefined;
        } | {
            backgroundColor: string;
            '--custom-color'?: undefined;
        } | undefined>;
        computedCheck: import("vue").ComputedRef<boolean>;
        computedLoading: import("vue").ComputedRef<boolean>;
        handleClick: (ev: Event) => Promise<void>;
        handleFocus: (ev: FocusEvent) => void;
        handleBlur: (ev: FocusEvent) => void;
    }, {}, {}, {}, {
        disabled: boolean;
        type: "round" | "circle" | "line";
        modelValue: string | number | boolean;
        loading: boolean;
        defaultChecked: boolean;
        checkedValue: string | number | boolean;
        uncheckedValue: string | number | boolean;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    modelValue: {
        type: (BooleanConstructor | StringConstructor | NumberConstructor)[];
        default: undefined;
    };
    defaultChecked: {
        type: BooleanConstructor;
        default: boolean;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    loading: {
        type: BooleanConstructor;
        default: boolean;
    };
    type: {
        type: import("vue").PropType<"round" | "circle" | "line">;
        default: string;
    };
    size: {
        type: import("vue").PropType<"medium" | "small">;
    };
    checkedValue: {
        type: (BooleanConstructor | StringConstructor | NumberConstructor)[];
        default: boolean;
    };
    uncheckedValue: {
        type: (BooleanConstructor | StringConstructor | NumberConstructor)[];
        default: boolean;
    };
    checkedColor: {
        type: StringConstructor;
    };
    uncheckedColor: {
        type: StringConstructor;
    };
    beforeChange: {
        type: import("vue").PropType<(newValue: string | number | boolean) => boolean | void | Promise<boolean | void>>;
    };
    checkedText: {
        type: StringConstructor;
    };
    uncheckedText: {
        type: StringConstructor;
    };
}>> & Readonly<{
    onFocus?: ((ev: FocusEvent) => any) | undefined;
    onChange?: ((value: string | number | boolean, ev: Event) => any) | undefined;
    onBlur?: ((ev: FocusEvent) => any) | undefined;
    "onUpdate:modelValue"?: ((value: string | number | boolean) => any) | undefined;
}>, {
    prefixCls: string;
    cls: import("vue").ComputedRef<(string | {
        [x: string]: string | boolean | undefined;
    })[]>;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    buttonStyle: import("vue").ComputedRef<{
        '--custom-color': string;
        backgroundColor?: undefined;
    } | {
        backgroundColor: string;
        '--custom-color'?: undefined;
    } | undefined>;
    computedCheck: import("vue").ComputedRef<boolean>;
    computedLoading: import("vue").ComputedRef<boolean>;
    handleClick: (ev: Event) => Promise<void>;
    handleFocus: (ev: FocusEvent) => void;
    handleBlur: (ev: FocusEvent) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    'update:modelValue': (value: string | number | boolean) => true;
    change: (value: string | number | boolean, ev: Event) => true;
    focus: (ev: FocusEvent) => true;
    blur: (ev: FocusEvent) => true;
}, string, {
    disabled: boolean;
    type: "round" | "circle" | "line";
    modelValue: string | number | boolean;
    loading: boolean;
    defaultChecked: boolean;
    checkedValue: string | number | boolean;
    uncheckedValue: string | number | boolean;
}, {}, string, {}, {
    IconLoading: any;
} & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type SwitchInstance = InstanceType<typeof _Switch>;
export default Switch;
