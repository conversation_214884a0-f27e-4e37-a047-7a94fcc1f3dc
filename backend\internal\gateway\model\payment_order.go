package model

import (
	"time"

	"gorm.io/gorm"
)

// BaseModel 基础模型，包含公共字段
type BaseModel struct {
	ID        uint           `gorm:"primarykey" json:"id"`                                 // 主键ID
	CreatedAt time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"` // 创建时间
	UpdatedAt time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"` // 更新时间
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`                    // 软删除时间
}

// PaymentOrder 支付订单模型
type PaymentOrder struct {
	BaseModel
	OrderNo         string    `gorm:"uniqueIndex;size:32;not null" json:"order_no"`                       // 订单号
	MerchantID      uint      `gorm:"not null;index" json:"merchant_id"`                                  // 商户ID
	MerchantOrderNo string    `gorm:"size:64;not null;index:idx_merchant_order" json:"merchant_order_no"` // 商户订单号
	OutOrderNo      string    `gorm:"size:64;not null;index:idx_out_order" json:"out_order_no"`           // 外部订单号（别名）
	AppID           string    `gorm:"size:32" json:"app_id"`                                              // 应用ID
	Subject         string    `gorm:"size:200;not null" json:"subject"`                                   // 订单标题
	Body            string    `gorm:"size:500" json:"body"`                                               // 订单描述
	Amount          int64     `gorm:"not null" json:"amount"`                                             // 订单金额（分）
	Currency        string    `gorm:"size:3;default:'CNY'" json:"currency"`                               // 货币类型
	Channel         string    `gorm:"size:20;not null" json:"channel"`                                    // 支付渠道
	PayMethod       string    `gorm:"size:20;not null" json:"pay_method"`                                 // 支付方式
	Status          int       `gorm:"default:0;index" json:"status"`                                      // 订单状态
	PayStatus       int       `gorm:"default:0" json:"pay_status"`                                        // 支付状态：0-未支付，1-已支付，2-支付失败
	NotifyURL       string    `gorm:"size:255" json:"notify_url"`                                         // 异步通知地址
	ReturnURL       string    `gorm:"size:255" json:"return_url"`                                         // 同步跳转地址
	ClientIP        string    `gorm:"size:45" json:"client_ip"`                                           // 客户端IP
	Device          string    `gorm:"size:50" json:"device"`                                              // 设备类型
	UserAgent       string    `gorm:"size:500" json:"user_agent"`                                         // 用户代理
	OpenID          string    `gorm:"size:64" json:"open_id"`                                             // 用户OpenID
	ExpireTime      time.Time `gorm:"not null" json:"expire_time"`                                        // 过期时间
	ExpireAt        time.Time `gorm:"not null" json:"expire_at"`                                          // 过期时间（别名）
	PaidAt          time.Time `json:"paid_at"`                                                            // 支付时间
	ClosedAt        time.Time `json:"closed_at"`                                                          // 关闭时间
	PayURL          string    `gorm:"size:500" json:"pay_url"`                                            // 支付链接
	QRCode          string    `gorm:"size:500" json:"qr_code"`                                            // 二维码链接
	FeeAmount       int64     `gorm:"default:0" json:"fee_amount"`                                        // 手续费（分）
	ActualAmount    int64     `gorm:"default:0" json:"actual_amount"`                                     // 实际到账金额（分）
	ExtraData       string    `gorm:"type:text" json:"extra_data"`                                        // 扩展数据，JSON格式
	Extra           string    `gorm:"type:text" json:"extra"`                                             // 扩展参数（别名）
	Remark          string    `gorm:"size:200" json:"remark"`                                             // 备注
}

// TableName 指定表名
func (PaymentOrder) TableName() string {
	return "payment_orders"
}

// PaymentChannel 支付渠道模型
type PaymentChannel struct {
	BaseModel
	ChannelCode string `gorm:"uniqueIndex;size:20;not null" json:"channel_code"` // 渠道代码
	ChannelName string `gorm:"size:50;not null" json:"channel_name"`             // 渠道名称
	ChannelType string `gorm:"size:20;not null" json:"channel_type"`             // 渠道类型
	Logo        string `gorm:"size:255" json:"logo"`                             // 渠道Logo
	Description string `gorm:"size:200" json:"description"`                      // 渠道描述
	Config      string `gorm:"type:text" json:"config"`                          // 渠道配置，JSON格式
	FeeRate     int    `gorm:"default:30" json:"fee_rate"`                       // 费率（万分之几）
	MinAmount   int64  `gorm:"default:1" json:"min_amount"`                      // 最小金额（分）
	MaxAmount   int64  `gorm:"default:100000000" json:"max_amount"`              // 最大金额（分）
	Status      int    `gorm:"default:1" json:"status"`                          // 状态：0-禁用，1-启用
	SortOrder   int    `gorm:"default:0" json:"sort_order"`                      // 排序
	Remark      string `gorm:"size:200" json:"remark"`                           // 备注
}

// TableName 指定表名
func (PaymentChannel) TableName() string {
	return "payment_channels"
}

// PaymentRecord 支付记录模型
type PaymentRecord struct {
	BaseModel
	RecordNo        string    `gorm:"uniqueIndex;size:32;not null" json:"record_no"` // 支付记录号
	OrderID         uint      `gorm:"not null;index" json:"order_id"`                // 订单ID
	OrderNo         string    `gorm:"size:32;not null;index" json:"order_no"`        // 订单号
	MerchantID      uint      `gorm:"not null;index" json:"merchant_id"`             // 商户ID
	Channel         string    `gorm:"size:20;not null" json:"channel"`               // 支付渠道
	PayMethod       string    `gorm:"size:20;not null" json:"pay_method"`            // 支付方式
	Amount          int64     `gorm:"not null" json:"amount"`                        // 支付金额（分）
	Currency        string    `gorm:"size:3;default:'CNY'" json:"currency"`          // 货币类型
	Status          int       `gorm:"default:0" json:"status"`                       // 支付状态：0-处理中，1-成功，2-失败
	ChannelOrderNo  string    `gorm:"size:64" json:"channel_order_no"`               // 渠道订单号
	ChannelTradeNo  string    `gorm:"size:64" json:"channel_trade_no"`               // 渠道交易号
	ChannelResponse string    `gorm:"type:text" json:"channel_response"`             // 渠道响应数据
	FeeAmount       int64     `gorm:"default:0" json:"fee_amount"`                   // 手续费（分）
	ActualAmount    int64     `gorm:"default:0" json:"actual_amount"`                // 实际到账金额（分）
	PayerInfo       string    `gorm:"type:text" json:"payer_info"`                   // 付款人信息，JSON格式
	PayTime         time.Time `json:"pay_time"`                                      // 支付时间
	NotifyTime      time.Time `json:"notify_time"`                                   // 通知时间
	ErrorCode       string    `gorm:"size:50" json:"error_code"`                     // 错误代码
	ErrorMsg        string    `gorm:"size:200" json:"error_msg"`                     // 错误信息
	Remark          string    `gorm:"size:200" json:"remark"`                        // 备注
}

// TableName 指定表名
func (PaymentRecord) TableName() string {
	return "payment_records"
}

// RefundOrder 退款订单模型
type RefundOrder struct {
	BaseModel
	RefundNo         string    `gorm:"uniqueIndex;size:32;not null" json:"refund_no"` // 退款单号
	OrderID          uint      `gorm:"not null;index" json:"order_id"`                // 原订单ID
	OrderNo          string    `gorm:"size:32;not null;index" json:"order_no"`        // 原订单号
	MerchantID       uint      `gorm:"not null;index" json:"merchant_id"`             // 商户ID
	MerchantRefundNo string    `gorm:"size:64;not null" json:"merchant_refund_no"`    // 商户退款单号
	Channel          string    `gorm:"size:20;not null" json:"channel"`               // 支付渠道
	RefundAmount     int64     `gorm:"not null" json:"refund_amount"`                 // 退款金额（分）
	TotalAmount      int64     `gorm:"not null" json:"total_amount"`                  // 订单总金额（分）
	Currency         string    `gorm:"size:3;default:'CNY'" json:"currency"`          // 货币类型
	Reason           string    `gorm:"size:200" json:"reason"`                        // 退款原因
	Status           int       `gorm:"default:0" json:"status"`                       // 退款状态
	ChannelRefundNo  string    `gorm:"size:64" json:"channel_refund_no"`              // 渠道退款单号
	ChannelResponse  string    `gorm:"type:text" json:"channel_response"`             // 渠道响应数据
	RefundTime       time.Time `json:"refund_time"`                                   // 退款时间
	NotifyTime       time.Time `json:"notify_time"`                                   // 通知时间
	ErrorCode        string    `gorm:"size:50" json:"error_code"`                     // 错误代码
	ErrorMsg         string    `gorm:"size:200" json:"error_msg"`                     // 错误信息
	OperatorID       uint      `json:"operator_id"`                                   // 操作员ID
	OperatorName     string    `gorm:"size:50" json:"operator_name"`                  // 操作员姓名
	Remark           string    `gorm:"size:200" json:"remark"`                        // 备注
}

// TableName 指定表名
func (RefundOrder) TableName() string {
	return "refund_orders"
}

// QRCode 聚合码模型
type QRCode struct {
	BaseModel
	QRCodeID    string    `gorm:"uniqueIndex;size:32;not null" json:"qr_code_id"` // 二维码ID
	MerchantID  uint      `gorm:"not null;index" json:"merchant_id"`              // 商户ID
	Name        string    `gorm:"size:100;not null" json:"name"`                  // 二维码名称
	Amount      int64     `gorm:"default:0" json:"amount"`                        // 固定金额（分），0表示不固定
	Description string    `gorm:"size:200" json:"description"`                    // 描述
	QRCodeURL   string    `gorm:"size:255;not null" json:"qr_code_url"`           // 二维码图片URL
	PayURL      string    `gorm:"size:255;not null" json:"pay_url"`               // 支付链接
	Channels    string    `gorm:"size:100" json:"channels"`                       // 支持的支付渠道，逗号分隔
	Status      int       `gorm:"default:1" json:"status"`                        // 状态：0-禁用，1-启用
	ExpireTime  time.Time `json:"expire_time"`                                    // 过期时间
	ScanCount   int       `gorm:"default:0" json:"scan_count"`                    // 扫码次数
	PayCount    int       `gorm:"default:0" json:"pay_count"`                     // 支付次数
	PayAmount   int64     `gorm:"default:0" json:"pay_amount"`                    // 支付金额（分）
	LastScanAt  time.Time `json:"last_scan_at"`                                   // 最后扫码时间
	LastPayAt   time.Time `json:"last_pay_at"`                                    // 最后支付时间
	Remark      string    `gorm:"size:200" json:"remark"`                         // 备注
}

// TableName 指定表名
func (QRCode) TableName() string {
	return "qr_codes"
}

// AggregateCode 聚合支付码模型 - 支持一码多付的智能聚合码
type AggregateCode struct {
	BaseModel
	CodeID            string    `gorm:"uniqueIndex;size:32;not null" json:"code_id"` // 聚合码唯一标识
	MerchantID        uint      `gorm:"not null;index" json:"merchant_id"`           // 商户ID
	AppID             string    `gorm:"size:32;not null;index" json:"app_id"`        // 应用ID
	Subject           string    `gorm:"size:200;not null" json:"subject"`            // 商品标题
	Body              string    `gorm:"size:500" json:"body"`                        // 商品描述
	Amount            int64     `gorm:"not null" json:"amount"`                      // 支付金额（分）
	Currency          string    `gorm:"size:3;default:'CNY'" json:"currency"`        // 货币类型
	SupportedChannels string    `gorm:"size:200;not null" json:"supported_channels"` // 支持的支付渠道，JSON数组格式
	BrandConfig       string    `gorm:"type:text" json:"brand_config"`               // 品牌定制配置，JSON格式
	NotifyURL         string    `gorm:"size:255" json:"notify_url"`                  // 异步通知地址
	ReturnURL         string    `gorm:"size:255" json:"return_url"`                  // 同步跳转地址
	ExpireTime        time.Time `gorm:"not null" json:"expire_time"`                 // 过期时间
	Status            int       `gorm:"default:1;index" json:"status"`               // 状态：0-禁用，1-启用，2-已过期
	QRCodeURL         string    `gorm:"size:255" json:"qr_code_url"`                 // 二维码图片URL
	PayURL            string    `gorm:"size:255;not null" json:"pay_url"`            // 支付页面URL
	ScanCount         int       `gorm:"default:0" json:"scan_count"`                 // 扫码次数
	PayCount          int       `gorm:"default:0" json:"pay_count"`                  // 支付成功次数
	TotalAmount       int64     `gorm:"default:0" json:"total_amount"`               // 累计支付金额（分）
	LastScanAt        time.Time `json:"last_scan_at"`                                // 最后扫码时间
	LastPayAt         time.Time `json:"last_pay_at"`                                 // 最后支付时间
	ExtraData         string    `gorm:"type:text" json:"extra_data"`                 // 扩展数据，JSON格式
	Remark            string    `gorm:"size:200" json:"remark"`                      // 备注
}

// TableName 指定表名
func (AggregateCode) TableName() string {
	return "aggregate_codes"
}

// PaymentChannelConfig 支付渠道配置模型 - 存储各支付渠道的配置信息
type PaymentChannelConfig struct {
	BaseModel
	MerchantID  uint   `gorm:"not null;index" json:"merchant_id"`                               // 商户ID
	ChannelCode string `gorm:"size:20;not null;index:idx_merchant_channel" json:"channel_code"` // 渠道代码
	ChannelName string `gorm:"size:50;not null" json:"channel_name"`                            // 渠道名称
	AppID       string `gorm:"size:64" json:"app_id"`                                           // 应用ID
	MchID       string `gorm:"size:64" json:"mch_id"`                                           // 商户号
	APIKey      string `gorm:"size:128" json:"api_key"`                                         // API密钥
	PrivateKey  string `gorm:"type:text" json:"private_key"`                                    // 私钥
	PublicKey   string `gorm:"type:text" json:"public_key"`                                     // 公钥
	CertPath    string `gorm:"size:255" json:"cert_path"`                                       // 证书路径
	CertContent string `gorm:"type:text" json:"cert_content"`                                   // 证书内容
	NotifyURL   string `gorm:"size:255" json:"notify_url"`                                      // 异步通知地址
	ReturnURL   string `gorm:"size:255" json:"return_url"`                                      // 同步跳转地址
	IsProd      bool   `gorm:"default:false" json:"is_prod"`                                    // 是否生产环境
	Status      int    `gorm:"default:1" json:"status"`                                         // 状态：0-禁用，1-启用
	FeeRate     int    `gorm:"default:30" json:"fee_rate"`                                      // 费率（万分之几）
	ExtraConfig string `gorm:"type:text" json:"extra_config"`                                   // 额外配置，JSON格式
	Remark      string `gorm:"size:200" json:"remark"`                                          // 备注
}

// TableName 指定表名
func (PaymentChannelConfig) TableName() string {
	return "payment_channel_configs"
}

// 订单状态常量
const (
	OrderStatusPending   = 0 // 待支付
	OrderStatusPaid      = 1 // 已支付
	OrderStatusCancelled = 2 // 已取消
	OrderStatusExpired   = 3 // 已过期
	OrderStatusRefunded  = 4 // 已退款
)

// 退款状态常量
const (
	RefundStatusPending   = 0 // 退款中
	RefundStatusSuccess   = 1 // 退款成功
	RefundStatusFailed    = 2 // 退款失败
	RefundStatusCancelled = 3 // 退款取消
)

// 支付渠道类型常量
const (
	ChannelWechat   = "wechat"   // 微信支付
	ChannelAlipay   = "alipay"   // 支付宝
	ChannelQQPay    = "qqpay"    // QQ钱包
	ChannelUnionPay = "unionpay" // 银联支付
	ChannelPayPal   = "paypal"   // PayPal
	ChannelApplePay = "applepay" // Apple Pay
)

// 支付方式常量
const (
	PayMethodNative      = "native"      // 扫码支付
	PayMethodJSAPI       = "jsapi"       // 公众号支付
	PayMethodApp         = "app"         // APP支付
	PayMethodH5          = "h5"          // H5支付
	PayMethodMiniProgram = "miniprogram" // 小程序支付
	PayMethodMicro       = "micro"       // 付款码支付
	PayMethodFace        = "face"        // 刷脸支付
)

// 聚合码状态常量
const (
	AggregateCodeStatusDisabled = 0 // 禁用
	AggregateCodeStatusEnabled  = 1 // 启用
	AggregateCodeStatusExpired  = 2 // 已过期
)

// 支付渠道配置状态常量
const (
	ChannelConfigStatusDisabled = 0 // 禁用
	ChannelConfigStatusEnabled  = 1 // 启用
)

// 用户代理检测常量 - 用于智能路由
const (
	UserAgentWechat  = "MicroMessenger" // 微信客户端
	UserAgentAlipay  = "AlipayClient"   // 支付宝客户端
	UserAgentQQ      = "QQ/"            // QQ客户端
	UserAgentMobile  = "Mobile"         // 移动端浏览器
	UserAgentAndroid = "Android"        // Android系统
	UserAgentIOS     = "iPhone|iPad"    // iOS系统
)
