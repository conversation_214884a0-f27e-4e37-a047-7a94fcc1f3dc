declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    hasItemSize: {
        type: FunctionConstructor;
        required: true;
    };
    setItemSize: {
        type: FunctionConstructor;
        required: true;
    };
}>, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}> | null, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    hasItemSize: {
        type: FunctionConstructor;
        required: true;
    };
    setItemSize: {
        type: FunctionConstructor;
        required: true;
    };
}>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
