import { PropType } from 'vue';
import { ThumbData } from './interface';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    data: {
        type: PropType<ThumbData>;
    };
    direction: {
        type: PropType<"horizontal" | "vertical">;
        default: string;
    };
    alwaysShow: {
        type: BooleanConstructor;
        default: boolean;
    };
    both: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, {
    visible: import("vue").Ref<boolean, boolean>;
    trackRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
    thumbRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
    prefixCls: string;
    thumbCls: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    thumbStyle: import("vue").ComputedRef<{
        [x: string]: string;
    }>;
    handleThumbMouseDown: (ev: MouseEvent) => void;
    handleTrackClick: (ev: MouseEvent) => void;
    setOffset: (_offset: number) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "scroll"[], "scroll", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    data: {
        type: PropType<ThumbData>;
    };
    direction: {
        type: PropType<"horizontal" | "vertical">;
        default: string;
    };
    alwaysShow: {
        type: BooleanConstructor;
        default: boolean;
    };
    both: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onScroll?: ((...args: any[]) => any) | undefined;
}>, {
    both: boolean;
    direction: "horizontal" | "vertical";
    alwaysShow: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
