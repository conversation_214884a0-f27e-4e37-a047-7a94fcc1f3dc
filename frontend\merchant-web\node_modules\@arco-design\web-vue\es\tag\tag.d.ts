import type { CSSProperties, PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    color: {
        type: PropType<string>;
    };
    size: {
        type: PropType<"medium" | "large" | "small">;
    };
    bordered: {
        type: BooleanConstructor;
        default: boolean;
    };
    visible: {
        type: BooleanConstructor;
        default: undefined;
    };
    defaultVisible: {
        type: BooleanConstructor;
        default: boolean;
    };
    loading: {
        type: BooleanConstructor;
        default: boolean;
    };
    closable: {
        type: BooleanConstructor;
        default: boolean;
    };
    checkable: {
        type: BooleanConstructor;
        default: boolean;
    };
    checked: {
        type: BooleanConstructor;
        default: undefined;
    };
    defaultChecked: {
        type: BooleanConstructor;
        default: boolean;
    };
    nowrap: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, {
    prefixCls: string;
    cls: import("vue").ComputedRef<(string | {
        [x: string]: boolean | "" | undefined;
    })[]>;
    style: import("vue").ComputedRef<CSSProperties | undefined>;
    computedVisible: import("vue").ComputedRef<boolean>;
    computedChecked: import("vue").ComputedRef<boolean>;
    handleClick: (ev: MouseEvent) => void;
    handleClose: (ev: MouseEvent) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    'update:visible': (visible: boolean) => true;
    'update:checked': (checked: boolean) => true;
    close: (ev: MouseEvent) => true;
    check: (checked: boolean, ev: MouseEvent) => true;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    color: {
        type: PropType<string>;
    };
    size: {
        type: PropType<"medium" | "large" | "small">;
    };
    bordered: {
        type: BooleanConstructor;
        default: boolean;
    };
    visible: {
        type: BooleanConstructor;
        default: undefined;
    };
    defaultVisible: {
        type: BooleanConstructor;
        default: boolean;
    };
    loading: {
        type: BooleanConstructor;
        default: boolean;
    };
    closable: {
        type: BooleanConstructor;
        default: boolean;
    };
    checkable: {
        type: BooleanConstructor;
        default: boolean;
    };
    checked: {
        type: BooleanConstructor;
        default: undefined;
    };
    defaultChecked: {
        type: BooleanConstructor;
        default: boolean;
    };
    nowrap: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onClose?: ((ev: MouseEvent) => any) | undefined;
    "onUpdate:visible"?: ((visible: boolean) => any) | undefined;
    "onUpdate:checked"?: ((checked: boolean) => any) | undefined;
    onCheck?: ((checked: boolean, ev: MouseEvent) => any) | undefined;
}>, {
    visible: boolean;
    nowrap: boolean;
    loading: boolean;
    bordered: boolean;
    closable: boolean;
    defaultVisible: boolean;
    checked: boolean;
    checkable: boolean;
    defaultChecked: boolean;
}, {}, {
    IconHover: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>, {
        prefixCls: string;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{}>, {
        disabled: boolean;
        size: "mini" | "medium" | "large" | "small";
    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    IconClose: any;
    IconLoading: any;
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
