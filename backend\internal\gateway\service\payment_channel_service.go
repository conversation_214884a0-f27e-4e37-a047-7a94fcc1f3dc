package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/alipay"
	"github.com/go-pay/gopay/wechat"
	"github.com/go-pay/util"

	"payment-gateway/internal/gateway/dto"
	"payment-gateway/internal/gateway/model"
	"payment-gateway/internal/gateway/repository"
	"payment-gateway/internal/shared/constants"
	"payment-gateway/internal/shared/errors"
	"payment-gateway/internal/shared/utils"
)

// PaymentChannelService 支付渠道服务接口
type PaymentChannelService interface {
	// CreatePayment 创建支付订单
	CreatePayment(ctx context.Context, req *dto.PaymentRequest) (*dto.PaymentResponse, error)

	// ProcessWechatPayment 处理微信支付
	ProcessWechatPayment(ctx context.Context, config *model.PaymentChannelConfig, order *model.PaymentOrder) (*dto.PaymentResponse, error)

	// ProcessAlipayPayment 处理支付宝支付
	ProcessAlipayPayment(ctx context.Context, config *model.PaymentChannelConfig, order *model.PaymentOrder) (*dto.PaymentResponse, error)

	// ProcessQQPayment 处理QQ支付
	ProcessQQPayment(ctx context.Context, config *model.PaymentChannelConfig, order *model.PaymentOrder) (*dto.PaymentResponse, error)

	// VerifyWechatNotify 验证微信支付回调
	VerifyWechatNotify(ctx context.Context, config *model.PaymentChannelConfig, notifyData map[string]interface{}) (bool, error)

	// VerifyAlipayNotify 验证支付宝支付回调
	VerifyAlipayNotify(ctx context.Context, config *model.PaymentChannelConfig, notifyData map[string]interface{}) (bool, error)
}

// paymentChannelService 支付渠道服务实现
type paymentChannelService struct {
	aggregateCodeRepo repository.AggregateCodeRepository
	channelConfigRepo repository.PaymentChannelConfigRepository
	paymentOrderRepo  repository.PaymentOrderRepository
	paymentRecordRepo repository.PaymentRecordRepository
}

// NewPaymentChannelService 创建支付渠道服务实例
func NewPaymentChannelService(
	aggregateCodeRepo repository.AggregateCodeRepository,
	channelConfigRepo repository.PaymentChannelConfigRepository,
	paymentOrderRepo repository.PaymentOrderRepository,
	paymentRecordRepo repository.PaymentRecordRepository,
) PaymentChannelService {
	return &paymentChannelService{
		aggregateCodeRepo: aggregateCodeRepo,
		channelConfigRepo: channelConfigRepo,
		paymentOrderRepo:  paymentOrderRepo,
		paymentRecordRepo: paymentRecordRepo,
	}
}

// CreatePayment 创建支付订单
func (s *paymentChannelService) CreatePayment(ctx context.Context, req *dto.PaymentRequest) (*dto.PaymentResponse, error) {
	// 获取聚合码信息
	aggregateCode, err := s.aggregateCodeRepo.GetByCodeID(ctx, req.CodeID)
	if err != nil {
		return nil, err
	}

	// 检查聚合码状态和过期时间
	if aggregateCode.Status != model.AggregateCodeStatusEnabled {
		return nil, errors.NewBusinessError(constants.ErrCodeValidation, "聚合码已禁用")
	}
	if time.Now().After(aggregateCode.ExpireTime) {
		return nil, errors.NewBusinessError(constants.ErrCodeValidation, "聚合码已过期")
	}

	// 验证支付渠道是否支持
	var supportedChannels []string
	if err := json.Unmarshal([]byte(aggregateCode.SupportedChannels), &supportedChannels); err != nil {
		return nil, errors.NewBusinessError(constants.ErrCodeInternalError, "支付渠道解析失败")
	}

	channelSupported := false
	for _, channel := range supportedChannels {
		if channel == req.Channel {
			channelSupported = true
			break
		}
	}
	if !channelSupported {
		return nil, errors.NewBusinessError(constants.ErrCodeValidation, "不支持的支付渠道")
	}

	// 获取支付渠道配置
	config, err := s.channelConfigRepo.GetByMerchantAndChannel(ctx, aggregateCode.MerchantID, req.Channel)
	if err != nil {
		return nil, err
	}
	if config.Status != model.ChannelConfigStatusEnabled {
		return nil, errors.NewBusinessError(constants.ErrCodeValidation, "支付渠道未启用")
	}

	// 生成支付订单
	orderNo := utils.GenerateID("PAY")
	expireTime := time.Now().Add(30 * time.Minute) // 订单30分钟过期

	paymentOrder := &model.PaymentOrder{
		OrderNo:         orderNo,
		MerchantID:      aggregateCode.MerchantID,
		MerchantOrderNo: req.CodeID, // 使用聚合码ID作为商户订单号
		AppID:           aggregateCode.AppID,
		Subject:         aggregateCode.Subject,
		Body:            aggregateCode.Body,
		Amount:          aggregateCode.Amount,
		Currency:        aggregateCode.Currency,
		Channel:         req.Channel,
		PayMethod:       req.PayMethod,
		Status:          model.OrderStatusPending,
		PayStatus:       0, // 未支付
		NotifyURL:       aggregateCode.NotifyURL,
		ReturnURL:       aggregateCode.ReturnURL,
		ClientIP:        req.ClientIP,
		UserAgent:       req.UserAgent,
		OpenID:          req.OpenID,
		ExpireTime:      expireTime,
		ExtraData:       req.ExtraData,
	}

	// 保存支付订单
	if err := s.paymentOrderRepo.Create(ctx, paymentOrder); err != nil {
		return nil, err
	}

	// 根据支付渠道处理支付
	var paymentResponse *dto.PaymentResponse
	switch req.Channel {
	case model.ChannelWechat:
		paymentResponse, err = s.ProcessWechatPayment(ctx, config, paymentOrder)
	case model.ChannelAlipay:
		paymentResponse, err = s.ProcessAlipayPayment(ctx, config, paymentOrder)
	case model.ChannelQQPay:
		paymentResponse, err = s.ProcessQQPayment(ctx, config, paymentOrder)
	default:
		return nil, errors.NewBusinessError(constants.ErrCodeValidation, "不支持的支付渠道")
	}

	if err != nil {
		// 更新订单状态为失败
		paymentOrder.Status = model.OrderStatusCancelled
		s.paymentOrderRepo.Update(ctx, paymentOrder)
		return nil, err
	}

	return paymentResponse, nil
}

// ProcessWechatPayment 处理微信支付
func (s *paymentChannelService) ProcessWechatPayment(ctx context.Context, config *model.PaymentChannelConfig, order *model.PaymentOrder) (*dto.PaymentResponse, error) {
	// 初始化微信客户端
	client := wechat.NewClient(config.AppID, config.MchID, config.APIKey, config.IsProd)
	client.DebugSwitch = gopay.DebugOff // 生产环境关闭调试

	// 如果有证书配置，添加证书
	if config.CertContent != "" {
		// 这里应该处理证书内容，实际项目中需要将证书保存到文件或直接使用内容
		// client.AddCertPemFileContent([]byte(config.CertContent))
	}

	// 构建支付参数
	bm := make(gopay.BodyMap)
	bm.Set("nonce_str", util.RandomString(32)).
		Set("body", order.Subject).
		Set("out_trade_no", order.OrderNo).
		Set("total_fee", order.Amount). // gopay中金额单位是分
		Set("spbill_create_ip", order.ClientIP).
		Set("notify_url", config.NotifyURL).
		Set("sign_type", wechat.SignType_MD5)

	// 根据支付方式设置不同参数
	switch order.PayMethod {
	case model.PayMethodNative:
		// 扫码支付
		bm.Set("trade_type", wechat.TradeType_Native)
	case model.PayMethodJSAPI:
		// 公众号支付
		bm.Set("trade_type", wechat.TradeType_JsApi)
		if order.OpenID != "" {
			bm.Set("openid", order.OpenID)
		}
	case model.PayMethodApp:
		// APP支付
		bm.Set("trade_type", wechat.TradeType_App)
	case model.PayMethodH5:
		// H5支付
		bm.Set("trade_type", wechat.TradeType_H5)
		// H5支付需要场景信息
		bm.SetBodyMap("scene_info", func(bm gopay.BodyMap) {
			bm.SetBodyMap("h5_info", func(bm gopay.BodyMap) {
				bm.Set("type", "Wap")
				bm.Set("wap_url", config.ReturnURL)
				bm.Set("wap_name", order.Subject)
			})
		})
	case model.PayMethodMiniProgram:
		// 小程序支付
		bm.Set("trade_type", wechat.TradeType_JsApi)
		if order.OpenID != "" {
			bm.Set("openid", order.OpenID)
		}
	default:
		return nil, errors.NewBusinessError(constants.ErrCodeValidation, "不支持的微信支付方式")
	}

	// 调用微信统一下单接口
	wxRsp, err := client.UnifiedOrder(ctx, bm)
	if err != nil {
		return nil, errors.NewBusinessError(constants.ErrCodeInternalError, fmt.Sprintf("微信支付下单失败: %v", err))
	}

	// 验证返回签名
	ok, err := wechat.VerifySign(config.APIKey, wechat.SignType_MD5, wxRsp)
	if err != nil || !ok {
		return nil, errors.NewBusinessError(constants.ErrCodeInternalError, "微信支付签名验证失败")
	}

	// 检查返回状态
	if wxRsp.ReturnCode != gopay.SUCCESS || wxRsp.ResultCode != gopay.SUCCESS {
		errMsg := fmt.Sprintf("微信支付失败: %s - %s", wxRsp.ReturnMsg, wxRsp.ErrCodeDes)
		return nil, errors.NewBusinessError(constants.ErrCodeInternalError, errMsg)
	}

	// 创建支付记录
	paymentRecord := &model.PaymentRecord{
		RecordNo:        utils.GenerateID("REC"),
		OrderID:         order.ID,
		OrderNo:         order.OrderNo,
		MerchantID:      order.MerchantID,
		Channel:         order.Channel,
		PayMethod:       order.PayMethod,
		Amount:          order.Amount,
		Currency:        order.Currency,
		Status:          0, // 处理中
		ChannelOrderNo:  wxRsp.PrepayId,
		ChannelResponse: fmt.Sprintf(`{"prepay_id":"%s","code_url":"%s"}`, wxRsp.PrepayId, wxRsp.CodeUrl),
	}

	if err := s.paymentRecordRepo.Create(ctx, paymentRecord); err != nil {
		// 记录日志但不影响主流程
		// logger.Error("创建支付记录失败", err)
	}

	// 构建支付响应数据
	payData := make(map[string]interface{})
	payData["prepay_id"] = wxRsp.PrepayId

	var qrCodeURL, payURL string

	switch order.PayMethod {
	case model.PayMethodNative:
		// 扫码支付返回二维码URL
		qrCodeURL = wxRsp.CodeUrl
		payData["code_url"] = wxRsp.CodeUrl
	case model.PayMethodJSAPI:
		// 公众号支付返回支付参数
		timeStamp := strconv.FormatInt(time.Now().Unix(), 10)
		packages := "prepay_id=" + wxRsp.PrepayId
		paySign := wechat.GetMiniPaySign(config.AppID, wxRsp.NonceStr, packages, wechat.SignType_MD5, timeStamp, config.APIKey)

		payData["appId"] = config.AppID
		payData["timeStamp"] = timeStamp
		payData["nonceStr"] = wxRsp.NonceStr
		payData["package"] = packages
		payData["signType"] = "MD5"
		payData["paySign"] = paySign
	case model.PayMethodApp:
		// APP支付返回支付参数
		timeStamp := strconv.FormatInt(time.Now().Unix(), 10)
		paySign := wechat.GetAppPaySign(config.AppID, config.MchID, wxRsp.NonceStr, wxRsp.PrepayId, wechat.SignType_MD5, timeStamp, config.APIKey)

		payData["appid"] = config.AppID
		payData["partnerid"] = config.MchID
		payData["prepayid"] = wxRsp.PrepayId
		payData["package"] = "Sign=WXPay"
		payData["noncestr"] = wxRsp.NonceStr
		payData["timestamp"] = timeStamp
		payData["sign"] = paySign
	case model.PayMethodH5:
		// H5支付返回支付链接
		payURL = wxRsp.MwebUrl
		payData["mweb_url"] = wxRsp.MwebUrl
	}

	return &dto.PaymentResponse{
		OrderNo:    order.OrderNo,
		Channel:    order.Channel,
		PayMethod:  order.PayMethod,
		Amount:     order.Amount,
		PayData:    payData,
		QRCodeURL:  qrCodeURL,
		PayURL:     payURL,
		ExpireTime: order.ExpireTime,
	}, nil
}

// ProcessAlipayPayment 处理支付宝支付
func (s *paymentChannelService) ProcessAlipayPayment(ctx context.Context, config *model.PaymentChannelConfig, order *model.PaymentOrder) (*dto.PaymentResponse, error) {
	// 初始化支付宝客户端
	client, err := alipay.NewClient(config.AppID, config.PrivateKey, config.IsProd)
	if err != nil {
		return nil, errors.NewBusinessError(constants.ErrCodeInternalError, fmt.Sprintf("初始化支付宝客户端失败: %v", err))
	}

	// 设置支付宝公钥（注意：新版本gopay不需要单独设置公钥）
	// 公钥已在NewClient时设置

	// 设置应用公钥证书和根证书（如果有）
	if config.CertContent != "" {
		// 实际项目中需要处理证书文件
		// client.SetCertSnFromPath("appCertPublicKey.crt", "alipayRootCert.crt", "alipayCertPublicKey_RSA2.crt")
	}

	// 构建支付参数
	bm := make(gopay.BodyMap)
	bm.Set("subject", order.Subject).
		Set("out_trade_no", order.OrderNo).
		Set("total_amount", fmt.Sprintf("%.2f", float64(order.Amount)/100)). // 支付宝金额单位是元
		Set("body", order.Body).
		Set("timeout_express", "30m") // 30分钟超时

	// 设置回调地址
	if config.NotifyURL != "" {
		bm.Set("notify_url", config.NotifyURL)
	}
	if config.ReturnURL != "" {
		bm.Set("return_url", config.ReturnURL)
	}

	var aliRsp interface{}
	var qrCodeURL, payURL string
	payData := make(map[string]interface{})

	// 根据支付方式调用不同接口
	switch order.PayMethod {
	case model.PayMethodNative:
		// 扫码支付
		aliPayRsp, err := client.TradePrecreate(ctx, bm)
		if err != nil {
			return nil, errors.NewBusinessError(constants.ErrCodeInternalError, fmt.Sprintf("支付宝扫码支付失败: %v", err))
		}
		aliRsp = aliPayRsp

		if aliPayRsp.Response.Code == "10000" {
			qrCodeURL = aliPayRsp.Response.QrCode
			payData["qr_code"] = aliPayRsp.Response.QrCode
		}

	case model.PayMethodH5:
		// H5支付
		aliPayRsp, err := client.TradeWapPay(ctx, bm)
		if err != nil {
			return nil, errors.NewBusinessError(constants.ErrCodeInternalError, fmt.Sprintf("支付宝H5支付失败: %v", err))
		}
		aliRsp = aliPayRsp
		payURL = aliPayRsp
		payData["pay_url"] = aliPayRsp

	case model.PayMethodApp:
		// APP支付
		aliPayRsp, err := client.TradeAppPay(ctx, bm)
		if err != nil {
			return nil, errors.NewBusinessError(constants.ErrCodeInternalError, fmt.Sprintf("支付宝APP支付失败: %v", err))
		}
		aliRsp = aliPayRsp
		payData["order_string"] = aliPayRsp

	case model.PayMethodJSAPI:
		// 网页支付
		aliPayRsp, err := client.TradePagePay(ctx, bm)
		if err != nil {
			return nil, errors.NewBusinessError(constants.ErrCodeInternalError, fmt.Sprintf("支付宝网页支付失败: %v", err))
		}
		aliRsp = aliPayRsp
		payURL = aliPayRsp
		payData["pay_url"] = aliPayRsp

	default:
		return nil, errors.NewBusinessError(constants.ErrCodeValidation, "不支持的支付宝支付方式")
	}

	// 创建支付记录
	responseJSON, _ := json.Marshal(aliRsp)
	paymentRecord := &model.PaymentRecord{
		RecordNo:        utils.GenerateID("REC"),
		OrderID:         order.ID,
		OrderNo:         order.OrderNo,
		MerchantID:      order.MerchantID,
		Channel:         order.Channel,
		PayMethod:       order.PayMethod,
		Amount:          order.Amount,
		Currency:        order.Currency,
		Status:          0,             // 处理中
		ChannelOrderNo:  order.OrderNo, // 支付宝使用商户订单号
		ChannelResponse: string(responseJSON),
	}

	if err := s.paymentRecordRepo.Create(ctx, paymentRecord); err != nil {
		// 记录日志但不影响主流程
		// logger.Error("创建支付记录失败", err)
	}

	return &dto.PaymentResponse{
		OrderNo:    order.OrderNo,
		Channel:    order.Channel,
		PayMethod:  order.PayMethod,
		Amount:     order.Amount,
		PayData:    payData,
		QRCodeURL:  qrCodeURL,
		PayURL:     payURL,
		ExpireTime: order.ExpireTime,
	}, nil
}

// ProcessQQPayment 处理QQ支付
func (s *paymentChannelService) ProcessQQPayment(ctx context.Context, config *model.PaymentChannelConfig, order *model.PaymentOrder) (*dto.PaymentResponse, error) {
	// QQ支付的实现类似微信支付，这里提供基础框架
	// 实际项目中需要根据QQ支付的具体API进行实现

	// 构建支付参数
	payData := make(map[string]interface{})
	payData["order_no"] = order.OrderNo
	payData["amount"] = order.Amount
	payData["subject"] = order.Subject

	// 模拟QQ支付响应
	var qrCodeURL, payURL string

	switch order.PayMethod {
	case model.PayMethodNative:
		// 扫码支付
		qrCodeURL = fmt.Sprintf("https://qpay.qq.com/cgi-bin/pay/qpay_unified_order.cgi?order_no=%s", order.OrderNo)
		payData["qr_code"] = qrCodeURL
	case model.PayMethodH5:
		// H5支付
		payURL = fmt.Sprintf("https://qpay.qq.com/cgi-bin/pay/qpay_h5_pay.cgi?order_no=%s", order.OrderNo)
		payData["pay_url"] = payURL
	default:
		return nil, errors.NewBusinessError(constants.ErrCodeValidation, "不支持的QQ支付方式")
	}

	// 创建支付记录
	paymentRecord := &model.PaymentRecord{
		RecordNo:        utils.GenerateID("REC"),
		OrderID:         order.ID,
		OrderNo:         order.OrderNo,
		MerchantID:      order.MerchantID,
		Channel:         order.Channel,
		PayMethod:       order.PayMethod,
		Amount:          order.Amount,
		Currency:        order.Currency,
		Status:          0, // 处理中
		ChannelOrderNo:  order.OrderNo,
		ChannelResponse: `{"status":"pending"}`,
	}

	if err := s.paymentRecordRepo.Create(ctx, paymentRecord); err != nil {
		// 记录日志但不影响主流程
		// logger.Error("创建支付记录失败", err)
	}

	return &dto.PaymentResponse{
		OrderNo:    order.OrderNo,
		Channel:    order.Channel,
		PayMethod:  order.PayMethod,
		Amount:     order.Amount,
		PayData:    payData,
		QRCodeURL:  qrCodeURL,
		PayURL:     payURL,
		ExpireTime: order.ExpireTime,
	}, nil
}

// VerifyWechatNotify 验证微信支付回调
func (s *paymentChannelService) VerifyWechatNotify(ctx context.Context, config *model.PaymentChannelConfig, notifyData map[string]interface{}) (bool, error) {
	// 验证微信支付回调签名
	// 实际项目中需要根据微信支付的回调验证规则实现

	// 检查必要字段
	if notifyData["return_code"] != "SUCCESS" {
		return false, errors.NewBusinessError(constants.ErrCodeValidation, "微信支付回调失败")
	}

	if notifyData["result_code"] != "SUCCESS" {
		return false, errors.NewBusinessError(constants.ErrCodeValidation, "微信支付结果失败")
	}

	// 验证签名
	if _, ok := notifyData["sign"].(string); !ok {
		return false, errors.NewBusinessError(constants.ErrCodeValidation, "微信支付回调签名缺失")
	}

	// 这里应该实现具体的签名验证逻辑
	// ok := wechat.VerifySign(config.APIKey, wechat.SignType_MD5, notifyData)

	return true, nil
}

// VerifyAlipayNotify 验证支付宝支付回调
func (s *paymentChannelService) VerifyAlipayNotify(ctx context.Context, config *model.PaymentChannelConfig, notifyData map[string]interface{}) (bool, error) {
	// 验证支付宝支付回调签名
	// 实际项目中需要根据支付宝的回调验证规则实现

	// 检查交易状态
	tradeStatus, ok := notifyData["trade_status"].(string)
	if !ok {
		return false, errors.NewBusinessError(constants.ErrCodeValidation, "支付宝回调交易状态缺失")
	}

	// 只有交易成功或交易结束才认为支付成功
	if tradeStatus != "TRADE_SUCCESS" && tradeStatus != "TRADE_FINISHED" {
		return false, errors.NewBusinessError(constants.ErrCodeValidation, "支付宝交易状态异常")
	}

	// 验证签名
	if _, ok := notifyData["sign"].(string); !ok {
		return false, errors.NewBusinessError(constants.ErrCodeValidation, "支付宝回调签名缺失")
	}

	// 这里应该实现具体的签名验证逻辑
	// ok := alipay.VerifySign(config.PublicKey, notifyData)

	return true, nil
}
