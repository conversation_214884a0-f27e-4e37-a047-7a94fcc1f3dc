import{d as S,r as f,a as g,o as P,c as h,b as B,e as t,w as o,f as s,g as l,t as N,j as E,_ as F}from"./index-LzkvhPr6.js";const O={class:"refund-orders"},U=S({__name:"RefundOrders",setup(j){const n=f({refundNo:"",orderNo:"",status:""}),x=[{title:"退款单号",dataIndex:"refundNo",width:200},{title:"原订单号",dataIndex:"orderNo",width:200},{title:"退款金额",dataIndex:"amount",slotName:"amount",width:120},{title:"退款原因",dataIndex:"reason",width:150},{title:"状态",dataIndex:"status",slotName:"status",width:100},{title:"申请时间",dataIndex:"createTime",width:180},{title:"处理时间",dataIndex:"processTime",width:180},{title:"操作",slotName:"actions",width:100}],w=g([{id:1,refundNo:"REF202401010001",orderNo:"PAY202401010001",amount:299,reason:"商品质量问题",status:"success",createTime:"2024-01-01 11:30:00",processTime:"2024-01-01 11:35:00"},{id:2,refundNo:"REF202401010002",orderNo:"PAY202401010002",amount:158.5,reason:"用户取消订单",status:"pending",createTime:"2024-01-01 11:25:00",processTime:""}]),m=f({current:1,pageSize:10,total:50,showTotal:!0}),v=g(!1),V=d=>({success:"green",pending:"orange",failed:"red"})[d]||"gray",b=d=>({success:"退款成功",pending:"处理中",failed:"退款失败"})[d]||"未知",_=()=>{console.log("搜索退款订单:",n)},C=()=>{Object.assign(n,{refundNo:"",orderNo:"",status:""}),_()},T=d=>{m.current=d},y=d=>{console.log("查看退款详情:",d)};return P(()=>{console.log("加载退款订单数据")}),(d,e)=>{const c=s("a-input"),r=s("a-form-item"),u=s("a-option"),I=s("a-select"),i=s("a-button"),k=s("a-form"),p=s("a-card"),M=s("a-tag"),R=s("a-table");return E(),h("div",O,[e[10]||(e[10]=B("h2",null,"退款订单",-1)),t(p,{class:"search-card"},{default:o(()=>[t(k,{model:n,layout:"inline"},{default:o(()=>[t(r,{label:"退款单号"},{default:o(()=>[t(c,{modelValue:n.refundNo,"onUpdate:modelValue":e[0]||(e[0]=a=>n.refundNo=a),placeholder:"请输入退款单号"},null,8,["modelValue"])]),_:1}),t(r,{label:"原订单号"},{default:o(()=>[t(c,{modelValue:n.orderNo,"onUpdate:modelValue":e[1]||(e[1]=a=>n.orderNo=a),placeholder:"请输入原订单号"},null,8,["modelValue"])]),_:1}),t(r,{label:"状态"},{default:o(()=>[t(I,{modelValue:n.status,"onUpdate:modelValue":e[2]||(e[2]=a=>n.status=a),placeholder:"请选择状态",style:{width:"120px"}},{default:o(()=>[t(u,{value:""},{default:o(()=>e[3]||(e[3]=[l("全部",-1)])),_:1,__:[3]}),t(u,{value:"pending"},{default:o(()=>e[4]||(e[4]=[l("处理中",-1)])),_:1,__:[4]}),t(u,{value:"success"},{default:o(()=>e[5]||(e[5]=[l("退款成功",-1)])),_:1,__:[5]}),t(u,{value:"failed"},{default:o(()=>e[6]||(e[6]=[l("退款失败",-1)])),_:1,__:[6]})]),_:1},8,["modelValue"])]),_:1}),t(r,null,{default:o(()=>[t(i,{type:"primary",onClick:_},{default:o(()=>e[7]||(e[7]=[l("搜索",-1)])),_:1,__:[7]}),t(i,{onClick:C,style:{"margin-left":"8px"}},{default:o(()=>e[8]||(e[8]=[l("重置",-1)])),_:1,__:[8]})]),_:1})]),_:1},8,["model"])]),_:1}),t(p,{class:"table-card"},{default:o(()=>[t(R,{columns:x,data:w.value,pagination:m,loading:v.value,onPageChange:T},{status:o(({record:a})=>[t(M,{color:V(a.status)},{default:o(()=>[l(N(b(a.status)),1)]),_:2},1032,["color"])]),amount:o(({record:a})=>[l(" ¥"+N(a.amount),1)]),actions:o(({record:a})=>[t(i,{type:"text",size:"small",onClick:z=>y(a)},{default:o(()=>e[9]||(e[9]=[l(" 查看详情 ",-1)])),_:2,__:[9]},1032,["onClick"])]),_:1},8,["data","pagination","loading"])]),_:1})])}}}),D=F(U,[["__scopeId","data-v-7058691a"]]);export{D as default};
