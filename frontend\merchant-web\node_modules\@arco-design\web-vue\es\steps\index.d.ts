import type { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _Steps from './steps';
import _StepsStep from './step';
declare const Steps: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
        type: {
            type: import("vue").PropType<"default" | "dot" | "arrow" | "navigation">;
            default: string;
        };
        direction: {
            type: import("vue").PropType<"horizontal" | "vertical">;
            default: string;
        };
        labelPlacement: {
            type: import("vue").PropType<"horizontal" | "vertical">;
            default: string;
        };
        current: {
            type: NumberConstructor;
            default: undefined;
        };
        defaultCurrent: {
            type: NumberConstructor;
            default: number;
        };
        status: {
            type: import("vue").PropType<"wait" | "error" | "finish" | "process">;
            default: string;
        };
        lineLess: {
            type: BooleanConstructor;
            default: boolean;
        };
        small: {
            type: BooleanConstructor;
            default: boolean;
        };
        changeable: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{
        onChange?: ((step: number, ev: Event) => any) | undefined;
        "onUpdate:current"?: ((step: number) => any) | undefined;
    }>, {
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        'update:current': (step: number) => true;
        change: (step: number, ev: Event) => true;
    }, import("vue").PublicProps, {
        small: boolean;
        current: number;
        type: "default" | "dot" | "arrow" | "navigation";
        direction: "horizontal" | "vertical";
        lineLess: boolean;
        status: "wait" | "error" | "finish" | "process";
        defaultCurrent: number;
        labelPlacement: "horizontal" | "vertical";
        changeable: boolean;
    }, true, {}, {}, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        type: {
            type: import("vue").PropType<"default" | "dot" | "arrow" | "navigation">;
            default: string;
        };
        direction: {
            type: import("vue").PropType<"horizontal" | "vertical">;
            default: string;
        };
        labelPlacement: {
            type: import("vue").PropType<"horizontal" | "vertical">;
            default: string;
        };
        current: {
            type: NumberConstructor;
            default: undefined;
        };
        defaultCurrent: {
            type: NumberConstructor;
            default: number;
        };
        status: {
            type: import("vue").PropType<"wait" | "error" | "finish" | "process">;
            default: string;
        };
        lineLess: {
            type: BooleanConstructor;
            default: boolean;
        };
        small: {
            type: BooleanConstructor;
            default: boolean;
        };
        changeable: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{
        onChange?: ((step: number, ev: Event) => any) | undefined;
        "onUpdate:current"?: ((step: number) => any) | undefined;
    }>, {
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
    }, {}, {}, {}, {
        small: boolean;
        current: number;
        type: "default" | "dot" | "arrow" | "navigation";
        direction: "horizontal" | "vertical";
        lineLess: boolean;
        status: "wait" | "error" | "finish" | "process";
        defaultCurrent: number;
        labelPlacement: "horizontal" | "vertical";
        changeable: boolean;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    type: {
        type: import("vue").PropType<"default" | "dot" | "arrow" | "navigation">;
        default: string;
    };
    direction: {
        type: import("vue").PropType<"horizontal" | "vertical">;
        default: string;
    };
    labelPlacement: {
        type: import("vue").PropType<"horizontal" | "vertical">;
        default: string;
    };
    current: {
        type: NumberConstructor;
        default: undefined;
    };
    defaultCurrent: {
        type: NumberConstructor;
        default: number;
    };
    status: {
        type: import("vue").PropType<"wait" | "error" | "finish" | "process">;
        default: string;
    };
    lineLess: {
        type: BooleanConstructor;
        default: boolean;
    };
    small: {
        type: BooleanConstructor;
        default: boolean;
    };
    changeable: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onChange?: ((step: number, ev: Event) => any) | undefined;
    "onUpdate:current"?: ((step: number) => any) | undefined;
}>, {
    cls: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    'update:current': (step: number) => true;
    change: (step: number, ev: Event) => true;
}, string, {
    small: boolean;
    current: number;
    type: "default" | "dot" | "arrow" | "navigation";
    direction: "horizontal" | "vertical";
    lineLess: boolean;
    status: "wait" | "error" | "finish" | "process";
    defaultCurrent: number;
    labelPlacement: "horizontal" | "vertical";
    changeable: boolean;
}, {}, string, {}, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    Step: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        title: StringConstructor;
        description: StringConstructor;
        status: {
            type: import("vue").PropType<"wait" | "error" | "finish" | "process">;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>, {
        prefixCls: string;
        iconCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        itemRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
        showTail: import("vue").ComputedRef<boolean>;
        stepNumber: import("vue").ComputedRef<number>;
        computedStatus: import("vue").ComputedRef<"wait" | "error" | "finish" | "process">;
        type: import("vue").ComputedRef<"default" | "dot" | "arrow" | "navigation">;
        handleClick: (ev: Event) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        title: StringConstructor;
        description: StringConstructor;
        status: {
            type: import("vue").PropType<"wait" | "error" | "finish" | "process">;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{}>, {
        disabled: boolean;
    }, {}, {
        IconCheck: any;
        IconClose: any;
    }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type StepsInstance = InstanceType<typeof _Steps>;
export declare type StepsStepInstance = InstanceType<typeof _StepsStep>;
export { _StepsStep as Step };
export default Steps;
