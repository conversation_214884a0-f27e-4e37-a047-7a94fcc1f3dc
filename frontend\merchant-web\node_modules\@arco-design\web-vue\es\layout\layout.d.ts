declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    hasSider: {
        type: BooleanConstructor;
    };
}>, {
    classNames: import("vue").ComputedRef<(string | {
        [x: string]: number | true;
    })[]>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    hasSider: {
        type: BooleanConstructor;
    };
}>> & Readonly<{}>, {
    hasSider: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
