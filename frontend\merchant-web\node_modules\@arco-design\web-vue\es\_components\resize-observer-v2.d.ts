declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    watchOnUpdated: BooleanConstructor;
}>, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>[] | undefined, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "resize"[], "resize", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    watchOnUpdated: BooleanConstructor;
}>> & Readonly<{
    onResize?: ((...args: any[]) => any) | undefined;
}>, {
    watchOnUpdated: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
