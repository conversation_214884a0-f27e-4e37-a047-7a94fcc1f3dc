import { CSSProperties, PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    title: StringConstructor;
    value: {
        type: PropType<number | Date>;
    };
    format: {
        type: StringConstructor;
        default: string;
    };
    extra: StringConstructor;
    start: {
        type: BooleanConstructor;
        default: boolean;
    };
    precision: {
        type: NumberConstructor;
        default: number;
    };
    separator: StringConstructor;
    showGroupSeparator: {
        type: BooleanConstructor;
        default: boolean;
    };
    animation: {
        type: BooleanConstructor;
        default: boolean;
    };
    animationDuration: {
        type: NumberConstructor;
        default: number;
    };
    valueFrom: {
        type: NumberConstructor;
        default: undefined;
    };
    placeholder: {
        type: StringConstructor;
    };
    valueStyle: {
        type: PropType<CSSProperties>;
    };
}>, {
    prefixCls: string;
    showPlaceholder: import("vue").ComputedRef<boolean>;
    formatValue: import("vue").ComputedRef<{
        isNumber: boolean;
        integer: string;
        decimal: string;
        value?: undefined;
    } | {
        isNumber: boolean;
        value: string | Date | undefined;
        integer?: undefined;
        decimal?: undefined;
    }>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    title: StringConstructor;
    value: {
        type: PropType<number | Date>;
    };
    format: {
        type: StringConstructor;
        default: string;
    };
    extra: StringConstructor;
    start: {
        type: BooleanConstructor;
        default: boolean;
    };
    precision: {
        type: NumberConstructor;
        default: number;
    };
    separator: StringConstructor;
    showGroupSeparator: {
        type: BooleanConstructor;
        default: boolean;
    };
    animation: {
        type: BooleanConstructor;
        default: boolean;
    };
    animationDuration: {
        type: NumberConstructor;
        default: number;
    };
    valueFrom: {
        type: NumberConstructor;
        default: undefined;
    };
    placeholder: {
        type: StringConstructor;
    };
    valueStyle: {
        type: PropType<CSSProperties>;
    };
}>> & Readonly<{}>, {
    start: boolean;
    format: string;
    animation: boolean;
    animationDuration: number;
    precision: number;
    showGroupSeparator: boolean;
    valueFrom: number;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
