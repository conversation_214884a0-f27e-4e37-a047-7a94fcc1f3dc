package model

import (
	"time"

	"gorm.io/gorm"
)

// BaseModel 基础模型，包含公共字段
type BaseModel struct {
	ID        uint           `gorm:"primarykey" json:"id"`                                 // 主键ID
	CreatedAt time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"` // 创建时间
	UpdatedAt time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"` // 更新时间
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`                    // 软删除时间
}

// Settlement 结算单模型
type Settlement struct {
	BaseModel
	SettlementNo    string    `gorm:"uniqueIndex;size:32;not null" json:"settlement_no"` // 结算单号
	MerchantID      uint      `gorm:"not null;index" json:"merchant_id"`                 // 商户ID
	SettleDate      string    `gorm:"size:10;not null;index" json:"settle_date"`         // 结算日期，格式：2023-01-01
	SettleType      int       `gorm:"not null" json:"settle_type"`                       // 结算类型：1-T+1，2-T+0，3-D+0，4-手动结算
	OrderCount      int       `gorm:"default:0" json:"order_count"`                      // 订单数量
	OrderAmount     int64     `gorm:"default:0" json:"order_amount"`                     // 订单金额（分）
	RefundCount     int       `gorm:"default:0" json:"refund_count"`                     // 退款数量
	RefundAmount    int64     `gorm:"default:0" json:"refund_amount"`                    // 退款金额（分）
	FeeAmount       int64     `gorm:"default:0" json:"fee_amount"`                       // 手续费金额（分）
	SettleAmount    int64     `gorm:"default:0" json:"settle_amount"`                    // 结算金额（分）
	ActualAmount    int64     `gorm:"default:0" json:"actual_amount"`                    // 实际到账金额（分）
	Status          int       `gorm:"default:0;index" json:"status"`                     // 状态：0-待结算，1-结算中，2-结算成功，3-结算失败
	BankCode        string    `gorm:"size:20" json:"bank_code"`                          // 银行代码
	BankName        string    `gorm:"size:100" json:"bank_name"`                         // 银行名称
	BankAccount     string    `gorm:"size:50" json:"bank_account"`                       // 银行账号
	AccountName     string    `gorm:"size:50" json:"account_name"`                       // 账户名
	BankAccountName string    `gorm:"size:50" json:"bank_account_name"`                  // 银行账户名
	TransferNo      string    `gorm:"size:64" json:"transfer_no"`                        // 转账单号
	TransferTime    time.Time `json:"transfer_time"`                                     // 转账时间
	FinishTime      time.Time `json:"finish_time"`                                       // 完成时间
	ErrorMsg        string    `gorm:"size:500" json:"error_msg"`                         // 错误信息
	Remark          string    `gorm:"size:500" json:"remark"`                            // 备注
	OperatorID      uint      `json:"operator_id"`                                       // 操作员ID
	OperatorName    string    `gorm:"size:50" json:"operator_name"`                      // 操作员姓名
}

// TableName 指定表名
func (Settlement) TableName() string {
	return "settlements"
}

// SettlementDetail 结算明细模型
type SettlementDetail struct {
	BaseModel
	SettlementID uint      `gorm:"not null;index" json:"settlement_id"`         // 结算单ID
	SettlementNo string    `gorm:"size:32;not null;index" json:"settlement_no"` // 结算单号
	MerchantID   uint      `gorm:"not null;index" json:"merchant_id"`           // 商户ID
	OrderID      uint      `gorm:"not null;index" json:"order_id"`              // 订单ID
	OrderNo      string    `gorm:"size:32;not null;index" json:"order_no"`      // 订单号
	OrderType    int       `gorm:"not null" json:"order_type"`                  // 订单类型：1-支付订单，2-退款订单
	OrderAmount  int64     `gorm:"not null" json:"order_amount"`                // 订单金额（分）
	FeeAmount    int64     `gorm:"default:0" json:"fee_amount"`                 // 手续费金额（分）
	SettleAmount int64     `gorm:"not null" json:"settle_amount"`               // 结算金额（分）
	Channel      string    `gorm:"size:20;not null" json:"channel"`             // 支付渠道
	PayMethod    string    `gorm:"size:20;not null" json:"pay_method"`          // 支付方式
	OrderTime    time.Time `gorm:"not null" json:"order_time"`                  // 订单时间
	SettleDate   string    `gorm:"size:10;not null" json:"settle_date"`         // 结算日期
	Status       int       `gorm:"default:1" json:"status"`                     // 状态：1-正常，2-异常
	Remark       string    `gorm:"size:200" json:"remark"`                      // 备注
}

// TableName 指定表名
func (SettlementDetail) TableName() string {
	return "settlement_details"
}

// WithdrawOrder 提现订单模型
type WithdrawOrder struct {
	BaseModel
	WithdrawNo      string    `gorm:"uniqueIndex;size:32;not null" json:"withdraw_no"` // 提现单号
	MerchantID      uint      `gorm:"not null;index" json:"merchant_id"`               // 商户ID
	Amount          int64     `gorm:"not null" json:"amount"`                          // 提现金额（分）
	FeeAmount       int64     `gorm:"default:0" json:"fee_amount"`                     // 手续费金额（分）
	ActualAmount    int64     `gorm:"not null" json:"actual_amount"`                   // 实际到账金额（分）
	WithdrawType    int       `gorm:"default:1" json:"withdraw_type"`                  // 提现类型：1-银行卡，2-支付宝，3-微信
	BankCode        string    `gorm:"size:20" json:"bank_code"`                        // 银行代码
	BankName        string    `gorm:"size:100;not null" json:"bank_name"`              // 银行名称
	BankAccount     string    `gorm:"size:50;not null" json:"bank_account"`            // 银行账号
	BankAccountName string    `gorm:"size:50;not null" json:"bank_account_name"`       // 银行账户名
	AccountName     string    `gorm:"size:50;not null" json:"account_name"`            // 账户名（别名）
	Status          int       `gorm:"default:0;index" json:"status"`                   // 状态：0-待审核，1-审核通过，2-处理中，3-提现成功，4-提现失败，5-审核拒绝
	AuditTime       time.Time `json:"audit_time"`                                      // 审核时间
	AuditorID       uint      `json:"auditor_id"`                                      // 审核员ID
	AuditorName     string    `gorm:"size:50" json:"auditor_name"`                     // 审核员姓名
	AuditRemark     string    `gorm:"size:200" json:"audit_remark"`                    // 审核备注
	TransferNo      string    `gorm:"size:64" json:"transfer_no"`                      // 转账单号
	TransferTime    time.Time `json:"transfer_time"`                                   // 转账时间
	FinishTime      time.Time `json:"finish_time"`                                     // 完成时间
	ErrorMsg        string    `gorm:"size:500" json:"error_msg"`                       // 错误信息
	ApplyReason     string    `gorm:"size:200" json:"apply_reason"`                    // 申请原因
	Remark          string    `gorm:"size:200" json:"remark"`                          // 备注
}

// TableName 指定表名
func (WithdrawOrder) TableName() string {
	return "withdraw_orders"
}

// AccountBalance 账户余额模型
type AccountBalance struct {
	BaseModel
	MerchantID       uint      `gorm:"uniqueIndex;not null" json:"merchant_id"` // 商户ID
	AvailableBalance int64     `gorm:"default:0" json:"available_balance"`      // 可用余额（分）
	FrozenBalance    int64     `gorm:"default:0" json:"frozen_balance"`         // 冻结余额（分）
	SettleBalance    int64     `gorm:"default:0" json:"settle_balance"`         // 待结算余额（分）
	TotalIncome      int64     `gorm:"default:0" json:"total_income"`           // 总收入（分）
	TotalExpense     int64     `gorm:"default:0" json:"total_expense"`          // 总支出（分）
	TotalWithdraw    int64     `gorm:"default:0" json:"total_withdraw"`         // 总提现（分）
	TodayIncome      int64     `gorm:"default:0" json:"today_income"`           // 今日收入（分）
	TodayExpense     int64     `gorm:"default:0" json:"today_expense"`          // 今日支出（分）
	LastSettleAt     time.Time `json:"last_settle_at"`                          // 最后结算时间
	Version          int64     `gorm:"default:0" json:"version"`                // 版本号，用于乐观锁
}

// TableName 指定表名
func (AccountBalance) TableName() string {
	return "account_balances"
}

// BalanceChange 余额变动记录模型
type BalanceChange struct {
	BaseModel
	MerchantID       uint   `gorm:"not null;index" json:"merchant_id"`             // 商户ID
	ChangeNo         string `gorm:"uniqueIndex;size:32;not null" json:"change_no"` // 变动单号
	ChangeType       int    `gorm:"not null" json:"change_type"`                   // 变动类型：1-收入，2-支出，3-冻结，4-解冻，5-结算，6-提现
	Amount           int64  `gorm:"not null" json:"amount"`                        // 变动金额（分）
	BeforeBalance    int64  `gorm:"not null" json:"before_balance"`                // 变动前余额（分）
	AfterBalance     int64  `gorm:"not null" json:"after_balance"`                 // 变动后余额（分）
	RelatedOrderNo   string `gorm:"size:32;index" json:"related_order_no"`         // 关联订单号
	RelatedOrderType int    `json:"related_order_type"`                            // 关联订单类型：1-支付订单，2-退款订单，3-结算单，4-提现单
	Description      string `gorm:"size:200;not null" json:"description"`          // 变动说明
	OperatorID       uint   `json:"operator_id"`                                   // 操作员ID
	OperatorName     string `gorm:"size:50" json:"operator_name"`                  // 操作员姓名
	OperatorType     string `gorm:"size:20" json:"operator_type"`                  // 操作员类型：system, admin, merchant
	Remark           string `gorm:"size:200" json:"remark"`                        // 备注
}

// TableName 指定表名
func (BalanceChange) TableName() string {
	return "balance_changes"
}

// ReconciliationRecord 对账记录模型
type ReconciliationRecord struct {
	BaseModel
	ReconciliationNo string    `gorm:"uniqueIndex;size:32;not null" json:"reconciliation_no"` // 对账单号
	MerchantID       uint      `gorm:"not null;index" json:"merchant_id"`                     // 商户ID
	RecordNo         string    `gorm:"uniqueIndex;size:32;not null" json:"record_no"`         // 对账记录号
	Channel          string    `gorm:"size:20;not null;index" json:"channel"`                 // 支付渠道
	ReconcileDate    string    `gorm:"size:10;not null;index" json:"reconcile_date"`          // 对账日期
	FileURL          string    `gorm:"size:255" json:"file_url"`                              // 对账文件URL
	TotalCount       int       `gorm:"default:0" json:"total_count"`                          // 总笔数
	TotalAmount      int64     `gorm:"default:0" json:"total_amount"`                         // 总金额（分）
	SuccessCount     int       `gorm:"default:0" json:"success_count"`                        // 成功笔数
	SuccessAmount    int64     `gorm:"default:0" json:"success_amount"`                       // 成功金额（分）
	FailCount        int       `gorm:"default:0" json:"fail_count"`                           // 失败笔数
	FailAmount       int64     `gorm:"default:0" json:"fail_amount"`                          // 失败金额（分）
	DiffCount        int       `gorm:"default:0" json:"diff_count"`                           // 差异笔数
	DiffAmount       int64     `gorm:"default:0" json:"diff_amount"`                          // 差异金额（分）
	Status           int       `gorm:"default:0;index" json:"status"`                         // 状态：0-待对账，1-对账中，2-对账完成，3-对账失败
	StartTime        time.Time `json:"start_time"`                                            // 开始时间
	FinishTime       time.Time `json:"finish_time"`                                           // 完成时间
	ErrorMsg         string    `gorm:"size:500" json:"error_msg"`                             // 错误信息
	OperatorID       uint      `json:"operator_id"`                                           // 操作员ID
	OperatorName     string    `gorm:"size:50" json:"operator_name"`                          // 操作员姓名
	Remark           string    `gorm:"size:200" json:"remark"`                                // 备注
}

// TableName 指定表名
func (ReconciliationRecord) TableName() string {
	return "reconciliation_records"
}

// ReconciliationDetail 对账明细模型
type ReconciliationDetail struct {
	BaseModel
	RecordID       uint      `gorm:"not null;index" json:"record_id"`         // 对账记录ID
	RecordNo       string    `gorm:"size:32;not null;index" json:"record_no"` // 对账记录号
	OrderNo        string    `gorm:"size:32;not null;index" json:"order_no"`  // 订单号
	ChannelOrderNo string    `gorm:"size:64" json:"channel_order_no"`         // 渠道订单号
	ChannelTradeNo string    `gorm:"size:64" json:"channel_trade_no"`         // 渠道交易号
	OrderAmount    int64     `gorm:"not null" json:"order_amount"`            // 订单金额（分）
	ChannelAmount  int64     `gorm:"not null" json:"channel_amount"`          // 渠道金额（分）
	OrderStatus    int       `gorm:"not null" json:"order_status"`            // 订单状态
	ChannelStatus  int       `gorm:"not null" json:"channel_status"`          // 渠道状态
	DiffType       int       `gorm:"default:0" json:"diff_type"`              // 差异类型：0-无差异，1-金额差异，2-状态差异，3-订单缺失，4-多余订单
	DiffAmount     int64     `gorm:"default:0" json:"diff_amount"`            // 差异金额（分）
	HandleStatus   int       `gorm:"default:0" json:"handle_status"`          // 处理状态：0-待处理，1-已处理，2-忽略
	HandleTime     time.Time `json:"handle_time"`                             // 处理时间
	HandleRemark   string    `gorm:"size:200" json:"handle_remark"`           // 处理备注
	OperatorID     uint      `json:"operator_id"`                             // 操作员ID
	OperatorName   string    `gorm:"size:50" json:"operator_name"`            // 操作员姓名
}

// TableName 指定表名
func (ReconciliationDetail) TableName() string {
	return "reconciliation_details"
}

// 结算状态常量
const (
	SettlementStatusPending    = 0 // 待结算
	SettlementStatusProcessing = 1 // 结算中
	SettlementStatusSuccess    = 2 // 结算成功
	SettlementStatusFailed     = 3 // 结算失败
)

// 结算类型常量
const (
	SettlementTypeT1     = 1 // T+1
	SettlementTypeT0     = 2 // T+0
	SettlementTypeD0     = 3 // D+0
	SettlementTypeManual = 4 // 手动结算
)

// 提现状态常量
const (
	WithdrawStatusPending    = 0 // 待审核
	WithdrawStatusApproved   = 1 // 审核通过
	WithdrawStatusProcessing = 2 // 处理中
	WithdrawStatusSuccess    = 3 // 提现成功
	WithdrawStatusFailed     = 4 // 提现失败
	WithdrawStatusRejected   = 5 // 审核拒绝
)

// 余额变动类型常量
const (
	BalanceChangeTypeIncome   = 1 // 收入
	BalanceChangeTypeExpense  = 2 // 支出
	BalanceChangeTypeFreeze   = 3 // 冻结
	BalanceChangeTypeUnfreeze = 4 // 解冻
	BalanceChangeTypeSettle   = 5 // 结算
	BalanceChangeTypeWithdraw = 6 // 提现
)

// 订单类型常量
const (
	OrderTypePayment = 1 // 支付订单
	OrderTypeRefund  = 2 // 退款订单
)

// 对账状态常量
const (
	ReconcileStatusPending    = 0 // 待对账
	ReconcileStatusProcessing = 1 // 对账中
	ReconcileStatusCompleted  = 2 // 对账完成
	ReconcileStatusFailed     = 3 // 对账失败
)

// 对账状态常量（别名，用于repository）
const (
	ReconciliationStatusPending    = ReconcileStatusPending    // 待对账
	ReconciliationStatusProcessing = ReconcileStatusProcessing // 对账中
	ReconciliationStatusSuccess    = ReconcileStatusCompleted  // 对账完成
	ReconciliationStatusFailed     = ReconcileStatusFailed     // 对账失败
)

// 差异类型常量
const (
	DiffTypeNone         = 0 // 无差异
	DiffTypeAmount       = 1 // 金额差异
	DiffTypeStatus       = 2 // 状态差异
	DiffTypeOrderMissing = 3 // 订单缺失
	DiffTypeOrderExtra   = 4 // 多余订单
)

// 处理状态常量
const (
	HandleStatusPending   = 0 // 待处理
	HandleStatusProcessed = 1 // 已处理
	HandleStatusIgnored   = 2 // 忽略
)

// SettlementOrder 结算订单模型（用于repository接口）
type SettlementOrder = Settlement

// WithdrawRecord 提现记录模型（用于repository接口）
type WithdrawRecord = WithdrawOrder
