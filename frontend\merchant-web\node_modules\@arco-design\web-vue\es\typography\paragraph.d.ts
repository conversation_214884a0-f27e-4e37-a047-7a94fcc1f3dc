import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    blockquote: {
        type: BooleanConstructor;
    };
    spacing: {
        type: PropType<"default" | "close">;
        default: string;
    };
}>, {
    component: import("vue").ComputedRef<"div" | "blockquote">;
    classNames: import("vue").ComputedRef<{
        [x: string]: boolean;
    }[]>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    blockquote: {
        type: BooleanConstructor;
    };
    spacing: {
        type: PropType<"default" | "close">;
        default: string;
    };
}>> & Readonly<{}>, {
    blockquote: boolean;
    spacing: "default" | "close";
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
