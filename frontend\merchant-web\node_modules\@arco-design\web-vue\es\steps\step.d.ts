import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    title: StringConstructor;
    description: StringConstructor;
    status: {
        type: PropType<"wait" | "error" | "finish" | "process">;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, {
    prefixCls: string;
    iconCls: string;
    cls: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    itemRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
    showTail: import("vue").ComputedRef<boolean>;
    stepNumber: import("vue").ComputedRef<number>;
    computedStatus: import("vue").ComputedRef<"wait" | "error" | "finish" | "process">;
    type: import("vue").ComputedRef<"default" | "dot" | "arrow" | "navigation">;
    handleClick: (ev: Event) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    title: StringConstructor;
    description: StringConstructor;
    status: {
        type: PropType<"wait" | "error" | "finish" | "process">;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{}>, {
    disabled: boolean;
}, {}, {
    IconCheck: any;
    IconClose: any;
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
