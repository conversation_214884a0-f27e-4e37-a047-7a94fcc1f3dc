<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title | default "扫码支付"}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        
        .container {
            max-width: 400px;
            margin: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            text-align: center;
            padding: 40px 20px;
        }
        
        .qr-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }
        
        .qr-container {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            display: inline-block;
        }
        
        .qr-code {
            width: 256px;
            height: 256px;
            border: 2px solid #e8e8e8;
            border-radius: 8px;
            background: white;
        }
        
        .qr-tips {
            font-size: 16px;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .qr-steps {
            text-align: left;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .step-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        .step-list {
            list-style: none;
            padding: 0;
        }
        
        .step-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 8px;
            font-size: 14px;
            color: #666;
        }
        
        .step-number {
            background: #1890ff;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
            flex-shrink: 0;
        }
        
        .qr-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
        }
        
        .btn-secondary {
            background: #f5f5f5;
            color: #666;
        }
        
        .btn-secondary:hover {
            background: #e8e8e8;
        }
        
        .loading {
            display: none;
            color: #1890ff;
            font-size: 14px;
            margin-top: 10px;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid #1890ff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
            margin-left: 8px;
        }
        
        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
        
        @media (max-width: 480px) {
            .container {
                margin: 10px;
                padding: 30px 15px;
            }
            
            .qr-code {
                width: 200px;
                height: 200px;
            }
            
            .qr-title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="qr-title">{{.Title | default "扫码支付"}}</div>
        
        <div class="qr-container">
            <img src="{{.QRCodeURL}}" alt="支付二维码" class="qr-code" id="qrCode">
        </div>
        
        <div class="qr-tips">
            请使用手机扫描上方二维码完成支付
        </div>
        
        <div class="qr-steps">
            <div class="step-title">支付步骤：</div>
            <ul class="step-list">
                <li class="step-item">
                    <span class="step-number">1</span>
                    <span>打开手机上的支付应用</span>
                </li>
                <li class="step-item">
                    <span class="step-number">2</span>
                    <span>扫描上方二维码</span>
                </li>
                <li class="step-item">
                    <span class="step-number">3</span>
                    <span>确认支付金额并完成支付</span>
                </li>
            </ul>
        </div>
        
        <div class="qr-actions">
            <button class="btn btn-primary" onclick="refreshQRCode()">刷新二维码</button>
            <button class="btn btn-secondary" onclick="checkPaymentStatus()">检查支付状态</button>
        </div>
        
        <div class="loading" id="loading">正在检查支付状态...</div>
    </div>
    
    <script>
        let paymentCheckInterval;
        
        // 刷新二维码
        function refreshQRCode() {
            const qrCode = document.getElementById('qrCode');
            const currentSrc = qrCode.src;
            qrCode.src = currentSrc + '&t=' + new Date().getTime();
        }
        
        // 检查支付状态
        function checkPaymentStatus() {
            const loading = document.getElementById('loading');
            loading.style.display = 'block';
            
            // 这里应该调用API检查支付状态
            // 目前使用模拟检查
            setTimeout(() => {
                loading.style.display = 'none';
                // 如果支付成功，可以跳转到成功页面
                // window.location.href = '/payment/success';
                alert('支付状态检查完成，请继续扫码支付');
            }, 2000);
        }
        
        // 自动检查支付状态
        function startPaymentCheck() {
            paymentCheckInterval = setInterval(() => {
                // 这里应该调用API检查支付状态
                // 如果支付成功，清除定时器并跳转
                console.log('自动检查支付状态...');
            }, 3000);
        }
        
        // 停止支付状态检查
        function stopPaymentCheck() {
            if (paymentCheckInterval) {
                clearInterval(paymentCheckInterval);
            }
        }
        
        // 页面加载时开始自动检查
        window.addEventListener('load', () => {
            startPaymentCheck();
        });
        
        // 页面卸载时停止检查
        window.addEventListener('beforeunload', () => {
            stopPaymentCheck();
        });
        
        // 页面可见性变化时处理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                stopPaymentCheck();
            } else {
                startPaymentCheck();
            }
        });
    </script>
</body>
</html>
