package repository

import (
	"context"

	"gorm.io/gorm"
	"payment-gateway/internal/gateway/model"
	"payment-gateway/internal/shared/errors"
)

// PaymentChannelConfigRepository 支付渠道配置仓储接口
type PaymentChannelConfigRepository interface {
	// Create 创建支付渠道配置
	Create(ctx context.Context, config *model.PaymentChannelConfig) error
	
	// GetByMerchantAndChannel 根据商户ID和渠道代码获取配置
	GetByMerchantAndChannel(ctx context.Context, merchantID uint, channelCode string) (*model.PaymentChannelConfig, error)
	
	// GetByMerchant 获取商户的所有支付渠道配置
	GetByMerchant(ctx context.Context, merchantID uint) ([]*model.PaymentChannelConfig, error)
	
	// GetEnabledByMerchant 获取商户启用的支付渠道配置
	GetEnabledByMerchant(ctx context.Context, merchantID uint) ([]*model.PaymentChannelConfig, error)
	
	// Update 更新支付渠道配置
	Update(ctx context.Context, config *model.PaymentChannelConfig) error
	
	// UpdateStatus 更新支付渠道配置状态
	UpdateStatus(ctx context.Context, merchantID uint, channelCode string, status int) error
	
	// Delete 删除支付渠道配置
	Delete(ctx context.Context, merchantID uint, channelCode string) error
	
	// List 获取支付渠道配置列表
	List(ctx context.Context, merchantID uint, channelCode string, status *int, page, pageSize int) ([]*model.PaymentChannelConfig, int64, error)
}

// paymentChannelConfigRepository 支付渠道配置仓储实现
type paymentChannelConfigRepository struct {
	db *gorm.DB
}

// NewPaymentChannelConfigRepository 创建支付渠道配置仓储实例
func NewPaymentChannelConfigRepository(db *gorm.DB) PaymentChannelConfigRepository {
	return &paymentChannelConfigRepository{
		db: db,
	}
}

// Create 创建支付渠道配置
func (r *paymentChannelConfigRepository) Create(ctx context.Context, config *model.PaymentChannelConfig) error {
	if err := r.db.WithContext(ctx).Create(config).Error; err != nil {
		return errors.NewDatabaseError("创建支付渠道配置失败", err.Error())
	}
	return nil
}

// GetByMerchantAndChannel 根据商户ID和渠道代码获取配置
func (r *paymentChannelConfigRepository) GetByMerchantAndChannel(ctx context.Context, merchantID uint, channelCode string) (*model.PaymentChannelConfig, error) {
	var config model.PaymentChannelConfig
	err := r.db.WithContext(ctx).
		Where("merchant_id = ? AND channel_code = ?", merchantID, channelCode).
		First(&config).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.NewNotFoundError("支付渠道配置不存在")
		}
		return nil, errors.NewDatabaseError("获取支付渠道配置失败", err.Error())
	}

	return &config, nil
}

// GetByMerchant 获取商户的所有支付渠道配置
func (r *paymentChannelConfigRepository) GetByMerchant(ctx context.Context, merchantID uint) ([]*model.PaymentChannelConfig, error) {
	var configs []*model.PaymentChannelConfig
	err := r.db.WithContext(ctx).
		Where("merchant_id = ?", merchantID).
		Order("channel_code").
		Find(&configs).Error

	if err != nil {
		return nil, errors.NewDatabaseError("获取商户支付渠道配置失败", err.Error())
	}

	return configs, nil
}

// GetEnabledByMerchant 获取商户启用的支付渠道配置
func (r *paymentChannelConfigRepository) GetEnabledByMerchant(ctx context.Context, merchantID uint) ([]*model.PaymentChannelConfig, error) {
	var configs []*model.PaymentChannelConfig
	err := r.db.WithContext(ctx).
		Where("merchant_id = ? AND status = ?", merchantID, model.ChannelConfigStatusEnabled).
		Order("channel_code").
		Find(&configs).Error

	if err != nil {
		return nil, errors.NewDatabaseError("获取商户启用的支付渠道配置失败", err.Error())
	}

	return configs, nil
}

// Update 更新支付渠道配置
func (r *paymentChannelConfigRepository) Update(ctx context.Context, config *model.PaymentChannelConfig) error {
	if err := r.db.WithContext(ctx).Save(config).Error; err != nil {
		return errors.NewDatabaseError("更新支付渠道配置失败", err.Error())
	}
	return nil
}

// UpdateStatus 更新支付渠道配置状态
func (r *paymentChannelConfigRepository) UpdateStatus(ctx context.Context, merchantID uint, channelCode string, status int) error {
	result := r.db.WithContext(ctx).Model(&model.PaymentChannelConfig{}).
		Where("merchant_id = ? AND channel_code = ?", merchantID, channelCode).
		Update("status", status)
	
	if result.Error != nil {
		return errors.NewDatabaseError("更新支付渠道配置状态失败", result.Error.Error())
	}
	
	if result.RowsAffected == 0 {
		return errors.NewNotFoundError("支付渠道配置不存在")
	}
	
	return nil
}

// Delete 删除支付渠道配置
func (r *paymentChannelConfigRepository) Delete(ctx context.Context, merchantID uint, channelCode string) error {
	result := r.db.WithContext(ctx).
		Where("merchant_id = ? AND channel_code = ?", merchantID, channelCode).
		Delete(&model.PaymentChannelConfig{})
	
	if result.Error != nil {
		return errors.NewDatabaseError("删除支付渠道配置失败", result.Error.Error())
	}
	
	if result.RowsAffected == 0 {
		return errors.NewNotFoundError("支付渠道配置不存在")
	}
	
	return nil
}

// List 获取支付渠道配置列表
func (r *paymentChannelConfigRepository) List(ctx context.Context, merchantID uint, channelCode string, status *int, page, pageSize int) ([]*model.PaymentChannelConfig, int64, error) {
	var configs []*model.PaymentChannelConfig
	var total int64
	
	query := r.db.WithContext(ctx).Model(&model.PaymentChannelConfig{})
	
	// 添加查询条件
	if merchantID > 0 {
		query = query.Where("merchant_id = ?", merchantID)
	}
	if channelCode != "" {
		query = query.Where("channel_code = ?", channelCode)
	}
	if status != nil {
		query = query.Where("status = ?", *status)
	}
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, errors.NewDatabaseError("获取支付渠道配置总数失败", err.Error())
	}
	
	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("channel_code").Offset(offset).Limit(pageSize).Find(&configs).Error; err != nil {
		return nil, 0, errors.NewDatabaseError("获取支付渠道配置列表失败", err.Error())
	}
	
	return configs, total, nil
}
