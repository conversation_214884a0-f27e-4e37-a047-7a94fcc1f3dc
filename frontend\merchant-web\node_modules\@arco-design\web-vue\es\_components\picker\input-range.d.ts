import { Dayjs } from 'dayjs';
import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    size: {
        type: PropType<"mini" | "medium" | "large" | "small">;
    };
    focused: {
        type: BooleanConstructor;
    };
    focusedIndex: {
        type: NumberConstructor;
    };
    error: {
        type: BooleanConstructor;
    };
    disabled: {
        type: PropType<boolean | boolean[]>;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
    };
    allowClear: {
        type: BooleanConstructor;
    };
    placeholder: {
        type: PropType<string[]>;
        default: () => never[];
    };
    inputValue: {
        type: PropType<string[]>;
    };
    value: {
        type: PropType<(Dayjs | undefined)[]>;
        default: () => never[];
    };
    format: {
        type: PropType<string | ((value: Dayjs) => string)>;
        required: true;
    };
}>, {
    prefixCls: string;
    classNames: import("vue").ComputedRef<(string | {
        [x: string]: boolean | import("vue").Slot<any> | undefined;
    })[]>;
    refInput0: import("vue").Ref<HTMLInputElement | undefined, HTMLInputElement | undefined>;
    refInput1: import("vue").Ref<HTMLInputElement | undefined, HTMLInputElement | undefined>;
    disabled0: import("vue").ComputedRef<boolean>;
    disabled1: import("vue").ComputedRef<boolean>;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    getDisabled: (index: number) => boolean;
    getInputWrapClassName: (index: number) => (string | {
        [x: string]: boolean;
    })[];
    displayValue0: import("vue").ComputedRef<string | undefined>;
    displayValue1: import("vue").ComputedRef<string | undefined>;
    changeFocusedInput: (index: number) => void;
    onChange: (e: Event) => void;
    onPressEnter: () => void;
    onPressTab: (e: Event) => void;
    onClear: (e: Event) => void;
    feedback: import("vue").Ref<string | undefined, string | undefined>;
}, {}, {}, {
    focus(index?: number | undefined): void;
    blur(): void;
}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("clear" | "press-enter" | "change" | "focused-index-change" | "update:focusedIndex")[], "clear" | "press-enter" | "change" | "focused-index-change" | "update:focusedIndex", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    size: {
        type: PropType<"mini" | "medium" | "large" | "small">;
    };
    focused: {
        type: BooleanConstructor;
    };
    focusedIndex: {
        type: NumberConstructor;
    };
    error: {
        type: BooleanConstructor;
    };
    disabled: {
        type: PropType<boolean | boolean[]>;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
    };
    allowClear: {
        type: BooleanConstructor;
    };
    placeholder: {
        type: PropType<string[]>;
        default: () => never[];
    };
    inputValue: {
        type: PropType<string[]>;
    };
    value: {
        type: PropType<(Dayjs | undefined)[]>;
        default: () => never[];
    };
    format: {
        type: PropType<string | ((value: Dayjs) => string)>;
        required: true;
    };
}>> & Readonly<{
    onClear?: ((...args: any[]) => any) | undefined;
    "onPress-enter"?: ((...args: any[]) => any) | undefined;
    onChange?: ((...args: any[]) => any) | undefined;
    "onFocused-index-change"?: ((...args: any[]) => any) | undefined;
    "onUpdate:focusedIndex"?: ((...args: any[]) => any) | undefined;
}>, {
    disabled: boolean | boolean[];
    value: (Dayjs | undefined)[];
    focused: boolean;
    readonly: boolean;
    error: boolean;
    allowClear: boolean;
    placeholder: string[];
}, {}, {
    IconHover: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>, {
        prefixCls: string;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{}>, {
        disabled: boolean;
        size: "mini" | "medium" | "large" | "small";
    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    IconClose: any;
    FeedbackIcon: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        type: {
            type: StringConstructor;
        };
    }>, {
        cls: import("vue").ComputedRef<string[]>;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        type: {
            type: StringConstructor;
        };
    }>> & Readonly<{}>, {}, {}, {
        IconLoading: any;
        IconCheckCircleFill: any;
        IconExclamationCircleFill: any;
        IconCloseCircleFill: any;
    }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
