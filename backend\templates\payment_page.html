<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title | default "聚合支付"}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: {{.BrandConfig.BackgroundColor | default "#f5f5f5"}};
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 400px;
            margin: 20px auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: {{.BrandConfig.ThemeColor | default "#1890ff"}};
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 10px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: {{.BrandConfig.ThemeColor | default "#1890ff"}};
        }
        
        .title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .description {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .payment-info {
            padding: 20px;
        }
        
        .amount {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .amount-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .amount-value {
            font-size: 32px;
            font-weight: bold;
            color: {{.BrandConfig.ThemeColor | default "#1890ff"}};
        }
        
        .amount-currency {
            font-size: 16px;
            margin-left: 5px;
        }
        
        .order-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .order-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .order-item:last-child {
            margin-bottom: 0;
        }
        
        .order-label {
            color: #666;
        }
        
        .order-value {
            color: #333;
            font-weight: 500;
        }
        
        .payment-methods {
            margin-bottom: 20px;
        }
        
        .method-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        .method-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 10px;
        }
        
        .method-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 10px;
            border: 2px solid #e8e8e8;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #333;
        }
        
        .method-item:hover {
            border-color: {{.BrandConfig.ThemeColor | default "#1890ff"}};
            background: rgba(24, 144, 255, 0.05);
        }
        
        .method-item.recommended {
            border-color: {{.BrandConfig.ThemeColor | default "#1890ff"}};
            background: rgba(24, 144, 255, 0.1);
        }
        
        .method-icon {
            width: 40px;
            height: 40px;
            margin-bottom: 8px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        
        .method-name {
            font-size: 12px;
            text-align: center;
            font-weight: 500;
        }
        
        .recommended-badge {
            background: {{.BrandConfig.ThemeColor | default "#1890ff"}};
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            margin-top: 4px;
        }
        
        .expire-info {
            text-align: center;
            color: #666;
            font-size: 12px;
            margin-bottom: 20px;
        }
        
        .footer {
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e8e8e8;
            background: #f8f9fa;
        }
        
        .contact-info {
            font-size: 12px;
            color: #666;
        }
        
        .expired {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }
        
        .expired-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .expired-text {
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        .expired-desc {
            font-size: 14px;
        }
        
        /* 支付渠道图标样式 */
        .wechat { background: #07c160; color: white; }
        .alipay { background: #1677ff; color: white; }
        .qqpay { background: #12b7f5; color: white; }
        .unionpay { background: #e21e2f; color: white; }
        .paypal { background: #003087; color: white; }
        .applepay { background: #000; color: white; }
        
        @media (max-width: 480px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }
            
            .method-list {
                grid-template-columns: repeat(3, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        {{if .IsExpired}}
        <div class="expired">
            <div class="expired-icon">⏰</div>
            <div class="expired-text">支付码已过期</div>
            <div class="expired-desc">请重新生成支付码</div>
        </div>
        {{else}}
        <div class="header">
            <div class="logo">
                {{if .BrandConfig.Logo}}
                <img src="{{.BrandConfig.Logo}}" alt="Logo" style="width: 100%; height: 100%; border-radius: 50%;">
                {{else}}
                💳
                {{end}}
            </div>
            <div class="title">{{.Subject}}</div>
            {{if .Body}}
            <div class="description">{{.Body}}</div>
            {{end}}
        </div>
        
        <div class="payment-info">
            <div class="amount">
                <div class="amount-label">支付金额</div>
                <div class="amount-value">
                    ¥{{.AmountYuan}}
                    <span class="amount-currency">{{.Currency}}</span>
                </div>
            </div>
            
            <div class="order-info">
                <div class="order-item">
                    <span class="order-label">订单编号</span>
                    <span class="order-value">{{.CodeID}}</span>
                </div>
                <div class="order-item">
                    <span class="order-label">创建时间</span>
                    <span class="order-value">{{.CreatedAt}}</span>
                </div>
                <div class="order-item">
                    <span class="order-label">有效期至</span>
                    <span class="order-value">{{.ExpireTime}}</span>
                </div>
            </div>
            
            <div class="payment-methods">
                <div class="method-title">选择支付方式</div>
                <div class="method-list">
                    {{range .AvailableChannels}}
                    <a href="javascript:void(0)" class="method-item {{if eq .Code $.RecommendedChannel}}recommended{{end}}" 
                       onclick="selectPaymentMethod('{{.Code}}')">
                        <div class="method-icon {{.Code}}">
                            {{if eq .Code "wechat"}}💬
                            {{else if eq .Code "alipay"}}🅰️
                            {{else if eq .Code "qqpay"}}🐧
                            {{else if eq .Code "unionpay"}}🏦
                            {{else if eq .Code "paypal"}}💰
                            {{else if eq .Code "applepay"}}🍎
                            {{else}}💳{{end}}
                        </div>
                        <div class="method-name">{{.Name}}</div>
                        {{if eq .Code $.RecommendedChannel}}
                        <div class="recommended-badge">推荐</div>
                        {{end}}
                    </a>
                    {{end}}
                </div>
            </div>
            
            <div class="expire-info">
                支付码将在 <span id="countdown"></span> 后过期
            </div>
        </div>
        
        {{if .BrandConfig.ContactInfo}}
        <div class="footer">
            <div class="contact-info">{{.BrandConfig.ContactInfo}}</div>
        </div>
        {{end}}
        {{end}}
    </div>
    
    <script>
        // 倒计时功能
        function updateCountdown() {
            const expireTime = new Date('{{.ExpireTime}}').getTime();
            const now = new Date().getTime();
            const distance = expireTime - now;
            
            if (distance < 0) {
                document.getElementById('countdown').innerHTML = '已过期';
                return;
            }
            
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);
            
            document.getElementById('countdown').innerHTML = 
                (hours > 0 ? hours + '小时' : '') + 
                (minutes > 0 ? minutes + '分钟' : '') + 
                seconds + '秒';
        }
        
        // 每秒更新倒计时
        updateCountdown();
        setInterval(updateCountdown, 1000);
        
        // 选择支付方式
        function selectPaymentMethod(channel) {
            // 这里可以调用API创建支付订单
            const paymentData = {
                code_id: '{{.CodeID}}',
                channel: channel,
                pay_method: 'native', // 默认扫码支付
                client_ip: '',
                user_agent: navigator.userAgent
            };
            
            // 发送支付请求
            fetch('/api/v1/aggregate-codes/payment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(paymentData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    // 处理支付响应
                    handlePaymentResponse(data.data, channel);
                } else {
                    alert('支付失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('支付请求失败:', error);
                alert('支付请求失败，请重试');
            });
        }
        
        // 处理支付响应
        function handlePaymentResponse(paymentData, channel) {
            if (channel === 'wechat' || channel === 'alipay') {
                // 扫码支付，显示二维码
                if (paymentData.qr_code_url) {
                    window.open(paymentData.qr_code_url, '_blank');
                }
            } else if (channel === 'h5') {
                // H5支付，跳转到支付页面
                if (paymentData.pay_url) {
                    window.location.href = paymentData.pay_url;
                }
            }
        }
    </script>
    
    {{if .BrandConfig.CustomCSS}}
    <style>
        {{.BrandConfig.CustomCSS}}
    </style>
    {{end}}
</body>
</html>
