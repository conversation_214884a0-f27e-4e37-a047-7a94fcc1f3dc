import { CSSProperties, PropType, StyleValue } from 'vue';
import { LabelValue } from './interface';
import { TriggerProps } from '../trigger';
import { TreeFieldNames, TreeNodeData, TreeProps, TreeNodeKey, Node } from '../tree/interface';
import pickSubCompSlots from '../_utils/pick-sub-comp-slots';
import { Data } from '../_utils/types';
import { ScrollbarProps } from '../scrollbar';
import { SelectViewValue } from '../_components/select-view/interface';
declare const _default;
export default _default;
