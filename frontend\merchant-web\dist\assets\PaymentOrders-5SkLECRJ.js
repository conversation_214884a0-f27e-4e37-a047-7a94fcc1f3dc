import{d as O,r as f,a as g,o as A,c as U,b as Y,e as a,w as e,f as d,g as n,h as j,i as D,t as y,j as x,_ as $}from"./index-LzkvhPr6.js";const E={class:"payment-orders"},F=O({__name:"PaymentOrders",setup(R){const s=f({orderNo:"",payMethod:"",status:""}),v=[{title:"订单号",dataIndex:"orderNo",width:200},{title:"金额",dataIndex:"amount",slotName:"amount",width:120},{title:"支付方式",dataIndex:"payMethod",width:120},{title:"状态",dataIndex:"status",slotName:"status",width:100},{title:"创建时间",dataIndex:"createTime",width:180},{title:"支付时间",dataIndex:"payTime",width:180},{title:"操作",slotName:"actions",width:150}],w=g([{id:1,orderNo:"PAY202401010001",amount:299,payMethod:"微信支付",status:"success",createTime:"2024-01-01 10:30:00",payTime:"2024-01-01 10:31:25"},{id:2,orderNo:"PAY202401010002",amount:158.5,payMethod:"支付宝",status:"success",createTime:"2024-01-01 10:25:00",payTime:"2024-01-01 10:26:10"},{id:3,orderNo:"PAY202401010003",amount:88,payMethod:"微信支付",status:"pending",createTime:"2024-01-01 10:20:00",payTime:""}]),r=f({current:1,pageSize:10,total:100,showTotal:!0,showPageSize:!0}),C=g(!1),N=l=>({success:"green",pending:"orange",failed:"red"})[l]||"gray",M=l=>({success:"已支付",pending:"待支付",failed:"支付失败"})[l]||"未知",m=()=>{console.log("搜索订单:",s)},V=()=>{Object.assign(s,{orderNo:"",payMethod:"",status:""}),m()},T=l=>{r.current=l},b=l=>{r.pageSize=l,r.current=1},k=l=>{console.log("查看订单详情:",l)},P=l=>{console.log("取消订单:",l)};return A(()=>{console.log("加载支付订单数据")}),(l,t)=>{const S=d("a-input"),i=d("a-form-item"),u=d("a-option"),_=d("a-select"),p=d("a-button"),h=d("a-form"),c=d("a-card"),z=d("a-tag"),I=d("a-table");return x(),U("div",E,[t[15]||(t[15]=Y("h2",null,"支付订单",-1)),a(c,{class:"search-card"},{default:e(()=>[a(h,{model:s,layout:"inline"},{default:e(()=>[a(i,{label:"订单号"},{default:e(()=>[a(S,{modelValue:s.orderNo,"onUpdate:modelValue":t[0]||(t[0]=o=>s.orderNo=o),placeholder:"请输入订单号"},null,8,["modelValue"])]),_:1}),a(i,{label:"支付方式"},{default:e(()=>[a(_,{modelValue:s.payMethod,"onUpdate:modelValue":t[1]||(t[1]=o=>s.payMethod=o),placeholder:"请选择支付方式",style:{width:"120px"}},{default:e(()=>[a(u,{value:""},{default:e(()=>t[3]||(t[3]=[n("全部",-1)])),_:1,__:[3]}),a(u,{value:"wechat"},{default:e(()=>t[4]||(t[4]=[n("微信支付",-1)])),_:1,__:[4]}),a(u,{value:"alipay"},{default:e(()=>t[5]||(t[5]=[n("支付宝",-1)])),_:1,__:[5]}),a(u,{value:"unionpay"},{default:e(()=>t[6]||(t[6]=[n("银联支付",-1)])),_:1,__:[6]})]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"状态"},{default:e(()=>[a(_,{modelValue:s.status,"onUpdate:modelValue":t[2]||(t[2]=o=>s.status=o),placeholder:"请选择状态",style:{width:"100px"}},{default:e(()=>[a(u,{value:""},{default:e(()=>t[7]||(t[7]=[n("全部",-1)])),_:1,__:[7]}),a(u,{value:"pending"},{default:e(()=>t[8]||(t[8]=[n("待支付",-1)])),_:1,__:[8]}),a(u,{value:"success"},{default:e(()=>t[9]||(t[9]=[n("已支付",-1)])),_:1,__:[9]}),a(u,{value:"failed"},{default:e(()=>t[10]||(t[10]=[n("支付失败",-1)])),_:1,__:[10]})]),_:1},8,["modelValue"])]),_:1}),a(i,null,{default:e(()=>[a(p,{type:"primary",onClick:m},{default:e(()=>t[11]||(t[11]=[n("搜索",-1)])),_:1,__:[11]}),a(p,{onClick:V,style:{"margin-left":"8px"}},{default:e(()=>t[12]||(t[12]=[n("重置",-1)])),_:1,__:[12]})]),_:1})]),_:1},8,["model"])]),_:1}),a(c,{class:"table-card"},{default:e(()=>[a(I,{columns:v,data:w.value,pagination:r,loading:C.value,onPageChange:T,onPageSizeChange:b},{status:e(({record:o})=>[a(z,{color:N(o.status)},{default:e(()=>[n(y(M(o.status)),1)]),_:2},1032,["color"])]),amount:e(({record:o})=>[n(" ¥"+y(o.amount),1)]),actions:e(({record:o})=>[a(p,{type:"text",size:"small",onClick:B=>k(o)},{default:e(()=>t[13]||(t[13]=[n(" 查看详情 ",-1)])),_:2,__:[13]},1032,["onClick"]),o.status==="pending"?(x(),j(p,{key:0,type:"text",size:"small",onClick:B=>P(o)},{default:e(()=>t[14]||(t[14]=[n(" 取消订单 ",-1)])),_:2,__:[14]},1032,["onClick"])):D("",!0)]),_:1},8,["data","pagination","loading"])]),_:1})])}}}),G=$(F,[["__scopeId","data-v-60e4fa54"]]);export{G as default};
