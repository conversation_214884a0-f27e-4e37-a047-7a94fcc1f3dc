import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    ellipsis: {
        type: PropType<boolean | import("./interface").EllipsisConfig>;
        default: boolean;
    };
}>, {
    component: import("vue").ComputedRef<"div" | "span">;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    ellipsis: {
        type: PropType<boolean | import("./interface").EllipsisConfig>;
        default: boolean;
    };
}>> & Readonly<{}>, {
    ellipsis: boolean | import("./interface").EllipsisConfig;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
