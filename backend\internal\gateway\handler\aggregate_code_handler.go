package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"payment-gateway/internal/gateway/dto"
	"payment-gateway/internal/gateway/service"
	"payment-gateway/internal/shared/response"
	"payment-gateway/internal/shared/validator"
)

// AggregateCodeHandler 聚合码处理器
type AggregateCodeHandler struct {
	aggregateCodeService  service.AggregateCodeService
	paymentChannelService service.PaymentChannelService
	validator            *validator.Validator
}

// NewAggregateCodeHandler 创建聚合码处理器实例
func NewAggregateCodeHandler(
	aggregateCodeService service.AggregateCodeService,
	paymentChannelService service.PaymentChannelService,
	validator *validator.Validator,
) *AggregateCodeHandler {
	return &AggregateCodeHandler{
		aggregateCodeService:  aggregateCodeService,
		paymentChannelService: paymentChannelService,
		validator:            validator,
	}
}

// CreateAggregateCode 创建聚合码
// @Summary 创建聚合码
// @Description 创建支持多种支付方式的聚合支付码
// @Tags 聚合码管理
// @Accept json
// @Produce json
// @Param request body dto.CreateAggregateCodeRequest true "创建聚合码请求"
// @Success 200 {object} response.Response{data=dto.CreateAggregateCodeResponse} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/aggregate-codes [post]
func (h *AggregateCodeHandler) CreateAggregateCode(c *gin.Context) {
	var req dto.CreateAggregateCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}
	
	// 验证请求参数
	if err := h.validator.ValidateStruct(&req); err != nil {
		response.ValidationError(c, err)
		return
	}
	
	// 创建聚合码
	resp, err := h.aggregateCodeService.CreateAggregateCode(c.Request.Context(), &req)
	if err != nil {
		response.HandleError(c, err)
		return
	}
	
	response.Success(c, "创建聚合码成功", resp)
}

// GetAggregateCode 获取聚合码信息
// @Summary 获取聚合码信息
// @Description 根据聚合码ID获取详细信息
// @Tags 聚合码管理
// @Accept json
// @Produce json
// @Param code_id path string true "聚合码ID"
// @Success 200 {object} response.Response{data=dto.GetAggregateCodeResponse} "获取成功"
// @Failure 404 {object} response.Response "聚合码不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/aggregate-codes/{code_id} [get]
func (h *AggregateCodeHandler) GetAggregateCode(c *gin.Context) {
	codeID := c.Param("code_id")
	if codeID == "" {
		response.Error(c, http.StatusBadRequest, "聚合码ID不能为空", nil)
		return
	}
	
	// 获取聚合码信息
	resp, err := h.aggregateCodeService.GetAggregateCode(c.Request.Context(), codeID)
	if err != nil {
		response.HandleError(c, err)
		return
	}
	
	response.Success(c, "获取聚合码信息成功", resp)
}

// UpdateAggregateCode 更新聚合码
// @Summary 更新聚合码
// @Description 更新聚合码的配置信息
// @Tags 聚合码管理
// @Accept json
// @Produce json
// @Param code_id path string true "聚合码ID"
// @Param request body dto.UpdateAggregateCodeRequest true "更新聚合码请求"
// @Success 200 {object} response.Response "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "聚合码不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/aggregate-codes/{code_id} [put]
func (h *AggregateCodeHandler) UpdateAggregateCode(c *gin.Context) {
	codeID := c.Param("code_id")
	if codeID == "" {
		response.Error(c, http.StatusBadRequest, "聚合码ID不能为空", nil)
		return
	}
	
	var req dto.UpdateAggregateCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}
	
	// 设置聚合码ID
	req.CodeID = codeID
	
	// 验证请求参数
	if err := h.validator.ValidateStruct(&req); err != nil {
		response.ValidationError(c, err)
		return
	}
	
	// 更新聚合码
	if err := h.aggregateCodeService.UpdateAggregateCode(c.Request.Context(), &req); err != nil {
		response.HandleError(c, err)
		return
	}
	
	response.Success(c, "更新聚合码成功", nil)
}

// ListAggregateCodes 获取聚合码列表
// @Summary 获取聚合码列表
// @Description 分页获取聚合码列表
// @Tags 聚合码管理
// @Accept json
// @Produce json
// @Param merchant_id query int false "商户ID"
// @Param app_id query string false "应用ID"
// @Param status query int false "状态"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} response.Response{data=dto.ListAggregateCodesResponse} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/aggregate-codes [get]
func (h *AggregateCodeHandler) ListAggregateCodes(c *gin.Context) {
	var req dto.ListAggregateCodesRequest
	
	// 解析查询参数
	if merchantIDStr := c.Query("merchant_id"); merchantIDStr != "" {
		if merchantID, err := strconv.ParseUint(merchantIDStr, 10, 32); err == nil {
			req.MerchantID = uint(merchantID)
		}
	}
	
	req.AppID = c.Query("app_id")
	
	if statusStr := c.Query("status"); statusStr != "" {
		if status, err := strconv.Atoi(statusStr); err == nil {
			req.Status = &status
		}
	}
	
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			req.Page = page
		} else {
			req.Page = 1
		}
	} else {
		req.Page = 1
	}
	
	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 {
			req.PageSize = pageSize
		} else {
			req.PageSize = 20
		}
	} else {
		req.PageSize = 20
	}
	
	// 验证请求参数
	if err := h.validator.ValidateStruct(&req); err != nil {
		response.ValidationError(c, err)
		return
	}
	
	// 获取聚合码列表
	resp, err := h.aggregateCodeService.ListAggregateCodes(c.Request.Context(), &req)
	if err != nil {
		response.HandleError(c, err)
		return
	}
	
	response.Success(c, "获取聚合码列表成功", resp)
}

// DeleteAggregateCode 删除聚合码
// @Summary 删除聚合码
// @Description 软删除指定的聚合码
// @Tags 聚合码管理
// @Accept json
// @Produce json
// @Param code_id path string true "聚合码ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 404 {object} response.Response "聚合码不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/aggregate-codes/{code_id} [delete]
func (h *AggregateCodeHandler) DeleteAggregateCode(c *gin.Context) {
	codeID := c.Param("code_id")
	if codeID == "" {
		response.Error(c, http.StatusBadRequest, "聚合码ID不能为空", nil)
		return
	}
	
	// 删除聚合码
	if err := h.aggregateCodeService.DeleteAggregateCode(c.Request.Context(), codeID); err != nil {
		response.HandleError(c, err)
		return
	}
	
	response.Success(c, "删除聚合码成功", nil)
}

// SmartRoute 智能路由
// @Summary 智能路由
// @Description 根据用户代理推荐最佳支付渠道
// @Tags 聚合码管理
// @Accept json
// @Produce json
// @Param request body dto.SmartRouteRequest true "智能路由请求"
// @Success 200 {object} response.Response{data=dto.SmartRouteResponse} "路由成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/aggregate-codes/smart-route [post]
func (h *AggregateCodeHandler) SmartRoute(c *gin.Context) {
	var req dto.SmartRouteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}
	
	// 如果没有传递User-Agent，从请求头获取
	if req.UserAgent == "" {
		req.UserAgent = c.GetHeader("User-Agent")
	}
	
	// 如果没有传递客户端IP，从请求获取
	if req.ClientIP == "" {
		req.ClientIP = c.ClientIP()
	}
	
	// 验证请求参数
	if err := h.validator.ValidateStruct(&req); err != nil {
		response.ValidationError(c, err)
		return
	}
	
	// 执行智能路由
	resp, err := h.aggregateCodeService.SmartRoute(c.Request.Context(), &req)
	if err != nil {
		response.HandleError(c, err)
		return
	}
	
	response.Success(c, "智能路由成功", resp)
}

// GetPaymentPage 获取支付页面数据
// @Summary 获取支付页面数据
// @Description 获取聚合码支付页面所需的数据
// @Tags 聚合码管理
// @Accept json
// @Produce json
// @Param code_id path string true "聚合码ID"
// @Success 200 {object} response.Response{data=dto.PaymentPageData} "获取成功"
// @Failure 404 {object} response.Response "聚合码不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/aggregate-codes/{code_id}/payment-page [get]
func (h *AggregateCodeHandler) GetPaymentPage(c *gin.Context) {
	codeID := c.Param("code_id")
	if codeID == "" {
		response.Error(c, http.StatusBadRequest, "聚合码ID不能为空", nil)
		return
	}
	
	// 获取User-Agent用于智能路由
	userAgent := c.GetHeader("User-Agent")
	
	// 更新扫码次数
	h.aggregateCodeService.UpdateScanCount(c.Request.Context(), codeID)
	
	// 获取支付页面数据
	resp, err := h.aggregateCodeService.GetPaymentPageData(c.Request.Context(), codeID, userAgent)
	if err != nil {
		response.HandleError(c, err)
		return
	}
	
	response.Success(c, "获取支付页面数据成功", resp)
}

// CreatePayment 创建支付订单
// @Summary 创建支付订单
// @Description 根据聚合码和选择的支付渠道创建支付订单
// @Tags 聚合码管理
// @Accept json
// @Produce json
// @Param request body dto.PaymentRequest true "支付请求"
// @Success 200 {object} response.Response{data=dto.PaymentResponse} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/aggregate-codes/payment [post]
func (h *AggregateCodeHandler) CreatePayment(c *gin.Context) {
	var req dto.PaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}
	
	// 如果没有传递客户端IP，从请求获取
	if req.ClientIP == "" {
		req.ClientIP = c.ClientIP()
	}
	
	// 如果没有传递User-Agent，从请求头获取
	if req.UserAgent == "" {
		req.UserAgent = c.GetHeader("User-Agent")
	}
	
	// 验证请求参数
	if err := h.validator.ValidateStruct(&req); err != nil {
		response.ValidationError(c, err)
		return
	}
	
	// 创建支付订单
	resp, err := h.paymentChannelService.CreatePayment(c.Request.Context(), &req)
	if err != nil {
		response.HandleError(c, err)
		return
	}
	
	response.Success(c, "创建支付订单成功", resp)
}
