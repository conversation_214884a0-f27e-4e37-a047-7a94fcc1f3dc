package repository

import (
	"context"
	"time"
	"payment-gateway/internal/admin/model"

	"gorm.io/gorm"
)

// SystemConfigRepository 系统配置仓储接口
type SystemConfigRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, config *model.SystemConfig) error
	GetByID(ctx context.Context, id uint) (*model.SystemConfig, error)
	GetByKey(ctx context.Context, configKey string) (*model.SystemConfig, error)
	Update(ctx context.Context, config *model.SystemConfig) error
	Delete(ctx context.Context, id uint) error
	
	// 查询操作
	List(ctx context.Context, condition *SystemConfigQueryCondition) ([]*model.SystemConfig, int64, error)
	GetByCategory(ctx context.Context, category string) ([]*model.SystemConfig, error)
	GetPublicConfigs(ctx context.Context) ([]*model.SystemConfig, error)
	GetConfigsByKeys(ctx context.Context, keys []string) ([]*model.SystemConfig, error)
	
	// 批量操作
	BatchUpdate(ctx context.Context, configs []*model.SystemConfig) error
	BatchCreate(ctx context.Context, configs []*model.SystemConfig) error
	
	// 配置值操作
	GetConfigValue(ctx context.Context, configKey string) (string, error)
	SetConfigValue(ctx context.Context, configKey, configValue string) error
	
	// 缓存操作
	GetAllConfigs(ctx context.Context) (map[string]*model.SystemConfig, error)
	RefreshCache(ctx context.Context) error
}

// SystemConfigQueryCondition 系统配置查询条件
type SystemConfigQueryCondition struct {
	ConfigKey   *string `json:"config_key"`
	Category    *string `json:"category"`
	Name        *string `json:"name"`
	ConfigType  *string `json:"config_type"`
	IsPublic    *bool   `json:"is_public"`
	Page        int     `json:"page"`
	PageSize    int     `json:"page_size"`
	OrderBy     string  `json:"order_by"`
}

// systemConfigRepository 系统配置仓储实现
type systemConfigRepository struct {
	db *gorm.DB
}

// NewSystemConfigRepository 创建系统配置仓储
func NewSystemConfigRepository(db *gorm.DB) SystemConfigRepository {
	return &systemConfigRepository{
		db: db,
	}
}

// Create 创建系统配置
func (r *systemConfigRepository) Create(ctx context.Context, config *model.SystemConfig) error {
	return r.db.WithContext(ctx).Create(config).Error
}

// GetByID 根据ID获取系统配置
func (r *systemConfigRepository) GetByID(ctx context.Context, id uint) (*model.SystemConfig, error) {
	var config model.SystemConfig
	err := r.db.WithContext(ctx).First(&config, id).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// GetByKey 根据配置键获取系统配置
func (r *systemConfigRepository) GetByKey(ctx context.Context, configKey string) (*model.SystemConfig, error) {
	var config model.SystemConfig
	err := r.db.WithContext(ctx).Where("config_key = ?", configKey).First(&config).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// Update 更新系统配置
func (r *systemConfigRepository) Update(ctx context.Context, config *model.SystemConfig) error {
	return r.db.WithContext(ctx).Save(config).Error
}

// Delete 删除系统配置
func (r *systemConfigRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.SystemConfig{}, id).Error
}

// List 分页查询系统配置
func (r *systemConfigRepository) List(ctx context.Context, condition *SystemConfigQueryCondition) ([]*model.SystemConfig, int64, error) {
	var configs []*model.SystemConfig
	var total int64
	
	query := r.db.WithContext(ctx).Model(&model.SystemConfig{})
	
	// 构建查询条件
	query = r.buildQueryCondition(query, condition)
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := (condition.Page - 1) * condition.PageSize
	orderBy := "sort_order ASC, created_at DESC"
	if condition.OrderBy != "" {
		orderBy = condition.OrderBy
	}
	
	err := query.Order(orderBy).Offset(offset).Limit(condition.PageSize).Find(&configs).Error
	if err != nil {
		return nil, 0, err
	}
	
	return configs, total, nil
}

// GetByCategory 根据分类获取系统配置
func (r *systemConfigRepository) GetByCategory(ctx context.Context, category string) ([]*model.SystemConfig, error) {
	var configs []*model.SystemConfig
	err := r.db.WithContext(ctx).Where("category = ?", category).Order("sort_order ASC, created_at DESC").Find(&configs).Error
	return configs, err
}

// GetPublicConfigs 获取公开配置
func (r *systemConfigRepository) GetPublicConfigs(ctx context.Context) ([]*model.SystemConfig, error) {
	var configs []*model.SystemConfig
	err := r.db.WithContext(ctx).Where("is_public = ?", true).Order("sort_order ASC, created_at DESC").Find(&configs).Error
	return configs, err
}

// GetConfigsByKeys 根据配置键列表获取配置
func (r *systemConfigRepository) GetConfigsByKeys(ctx context.Context, keys []string) ([]*model.SystemConfig, error) {
	var configs []*model.SystemConfig
	err := r.db.WithContext(ctx).Where("config_key IN ?", keys).Find(&configs).Error
	return configs, err
}

// BatchUpdate 批量更新系统配置
func (r *systemConfigRepository) BatchUpdate(ctx context.Context, configs []*model.SystemConfig) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, config := range configs {
			if err := tx.Save(config).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// BatchCreate 批量创建系统配置
func (r *systemConfigRepository) BatchCreate(ctx context.Context, configs []*model.SystemConfig) error {
	return r.db.WithContext(ctx).CreateInBatches(configs, 100).Error
}

// GetConfigValue 获取配置值
func (r *systemConfigRepository) GetConfigValue(ctx context.Context, configKey string) (string, error) {
	var configValue string
	err := r.db.WithContext(ctx).Model(&model.SystemConfig{}).Where("config_key = ?", configKey).Select("config_value").Scan(&configValue).Error
	return configValue, err
}

// SetConfigValue 设置配置值
func (r *systemConfigRepository) SetConfigValue(ctx context.Context, configKey, configValue string) error {
	return r.db.WithContext(ctx).Model(&model.SystemConfig{}).Where("config_key = ?", configKey).Update("config_value", configValue).Error
}

// GetAllConfigs 获取所有配置
func (r *systemConfigRepository) GetAllConfigs(ctx context.Context) (map[string]*model.SystemConfig, error) {
	var configs []*model.SystemConfig
	err := r.db.WithContext(ctx).Find(&configs).Error
	if err != nil {
		return nil, err
	}
	
	configMap := make(map[string]*model.SystemConfig)
	for _, config := range configs {
		configMap[config.ConfigKey] = config
	}
	
	return configMap, nil
}

// RefreshCache 刷新缓存（这里是占位实现，实际应该与缓存系统集成）
func (r *systemConfigRepository) RefreshCache(ctx context.Context) error {
	// TODO: 实现缓存刷新逻辑
	return nil
}

// buildQueryCondition 构建查询条件
func (r *systemConfigRepository) buildQueryCondition(query *gorm.DB, condition *SystemConfigQueryCondition) *gorm.DB {
	if condition.ConfigKey != nil {
		query = query.Where("config_key LIKE ?", "%"+*condition.ConfigKey+"%")
	}
	
	if condition.Category != nil {
		query = query.Where("category = ?", *condition.Category)
	}
	
	if condition.Name != nil {
		query = query.Where("name LIKE ?", "%"+*condition.Name+"%")
	}
	
	if condition.ConfigType != nil {
		query = query.Where("config_type = ?", *condition.ConfigType)
	}
	
	if condition.IsPublic != nil {
		query = query.Where("is_public = ?", *condition.IsPublic)
	}
	
	return query
}

// SystemLogRepository 系统日志仓储接口
type SystemLogRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, log *model.SystemLog) error
	GetByID(ctx context.Context, id uint) (*model.SystemLog, error)
	
	// 查询操作
	List(ctx context.Context, condition *SystemLogQueryCondition) ([]*model.SystemLog, int64, error)
	GetByModule(ctx context.Context, module string, limit int) ([]*model.SystemLog, error)
	GetByUser(ctx context.Context, userID uint, userType string, limit int) ([]*model.SystemLog, error)
	GetByIP(ctx context.Context, ip string, limit int) ([]*model.SystemLog, error)
	
	// 统计操作
	GetLogStats(ctx context.Context, module string, startDate, endDate string) (*LogStats, error)
	GetErrorLogs(ctx context.Context, limit int) ([]*model.SystemLog, error)
	
	// 清理操作
	CleanOldLogs(ctx context.Context, days int) error
}

// SystemLogQueryCondition 系统日志查询条件
type SystemLogQueryCondition struct {
	Module     *string `json:"module"`
	Action     *string `json:"action"`
	Method     *string `json:"method"`
	UserID     *uint   `json:"user_id"`
	Username   *string `json:"username"`
	UserType   *string `json:"user_type"`
	IP         *string `json:"ip"`
	Status     *int    `json:"status"`
	StartDate  *string `json:"start_date"`
	EndDate    *string `json:"end_date"`
	Page       int     `json:"page"`
	PageSize   int     `json:"page_size"`
	OrderBy    string  `json:"order_by"`
}

// LogStats 日志统计信息
type LogStats struct {
	TotalCount   int64 `json:"total_count"`
	SuccessCount int64 `json:"success_count"`
	ErrorCount   int64 `json:"error_count"`
	SuccessRate  int   `json:"success_rate"`
	AvgDuration  int   `json:"avg_duration"`
}

// systemLogRepository 系统日志仓储实现
type systemLogRepository struct {
	db *gorm.DB
}

// NewSystemLogRepository 创建系统日志仓储
func NewSystemLogRepository(db *gorm.DB) SystemLogRepository {
	return &systemLogRepository{
		db: db,
	}
}

// Create 创建系统日志
func (r *systemLogRepository) Create(ctx context.Context, log *model.SystemLog) error {
	return r.db.WithContext(ctx).Create(log).Error
}

// GetByID 根据ID获取系统日志
func (r *systemLogRepository) GetByID(ctx context.Context, id uint) (*model.SystemLog, error) {
	var log model.SystemLog
	err := r.db.WithContext(ctx).First(&log, id).Error
	if err != nil {
		return nil, err
	}
	return &log, nil
}

// List 分页查询系统日志
func (r *systemLogRepository) List(ctx context.Context, condition *SystemLogQueryCondition) ([]*model.SystemLog, int64, error) {
	var logs []*model.SystemLog
	var total int64
	
	query := r.db.WithContext(ctx).Model(&model.SystemLog{})
	
	// 构建查询条件
	query = r.buildLogQueryCondition(query, condition)
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := (condition.Page - 1) * condition.PageSize
	orderBy := "created_at DESC"
	if condition.OrderBy != "" {
		orderBy = condition.OrderBy
	}
	
	err := query.Order(orderBy).Offset(offset).Limit(condition.PageSize).Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}
	
	return logs, total, nil
}

// GetByModule 根据模块获取日志
func (r *systemLogRepository) GetByModule(ctx context.Context, module string, limit int) ([]*model.SystemLog, error) {
	var logs []*model.SystemLog
	query := r.db.WithContext(ctx).Where("module = ?", module).Order("created_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	err := query.Find(&logs).Error
	return logs, err
}

// GetByUser 根据用户获取日志
func (r *systemLogRepository) GetByUser(ctx context.Context, userID uint, userType string, limit int) ([]*model.SystemLog, error) {
	var logs []*model.SystemLog
	query := r.db.WithContext(ctx).Where("user_id = ? AND user_type = ?", userID, userType).Order("created_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	err := query.Find(&logs).Error
	return logs, err
}

// GetByIP 根据IP获取日志
func (r *systemLogRepository) GetByIP(ctx context.Context, ip string, limit int) ([]*model.SystemLog, error) {
	var logs []*model.SystemLog
	query := r.db.WithContext(ctx).Where("ip = ?", ip).Order("created_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	err := query.Find(&logs).Error
	return logs, err
}

// GetLogStats 获取日志统计
func (r *systemLogRepository) GetLogStats(ctx context.Context, module string, startDate, endDate string) (*LogStats, error) {
	var stats LogStats
	
	query := r.db.WithContext(ctx).Model(&model.SystemLog{})
	if module != "" {
		query = query.Where("module = ?", module)
	}
	if startDate != "" && endDate != "" {
		query = query.Where("DATE(created_at) BETWEEN ? AND ?", startDate, endDate)
	}
	
	// 总数
	query.Count(&stats.TotalCount)
	
	// 成功数
	query.Where("status >= 200 AND status < 400").Count(&stats.SuccessCount)
	
	// 错误数
	stats.ErrorCount = stats.TotalCount - stats.SuccessCount
	
	// 成功率
	if stats.TotalCount > 0 {
		stats.SuccessRate = int((stats.SuccessCount * 10000) / stats.TotalCount)
	}
	
	// 平均耗时
	query.Select("AVG(duration)").Scan(&stats.AvgDuration)
	
	return &stats, nil
}

// GetErrorLogs 获取错误日志
func (r *systemLogRepository) GetErrorLogs(ctx context.Context, limit int) ([]*model.SystemLog, error) {
	var logs []*model.SystemLog
	query := r.db.WithContext(ctx).Where("status >= 400").Order("created_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	err := query.Find(&logs).Error
	return logs, err
}

// CleanOldLogs 清理旧日志
func (r *systemLogRepository) CleanOldLogs(ctx context.Context, days int) error {
	cutoffDate := time.Now().AddDate(0, 0, -days)
	return r.db.WithContext(ctx).Where("created_at < ?", cutoffDate).Delete(&model.SystemLog{}).Error
}

// buildLogQueryCondition 构建日志查询条件
func (r *systemLogRepository) buildLogQueryCondition(query *gorm.DB, condition *SystemLogQueryCondition) *gorm.DB {
	if condition.Module != nil {
		query = query.Where("module = ?", *condition.Module)
	}
	
	if condition.Action != nil {
		query = query.Where("action LIKE ?", "%"+*condition.Action+"%")
	}
	
	if condition.Method != nil {
		query = query.Where("method = ?", *condition.Method)
	}
	
	if condition.UserID != nil {
		query = query.Where("user_id = ?", *condition.UserID)
	}
	
	if condition.Username != nil {
		query = query.Where("username LIKE ?", "%"+*condition.Username+"%")
	}
	
	if condition.UserType != nil {
		query = query.Where("user_type = ?", *condition.UserType)
	}
	
	if condition.IP != nil {
		query = query.Where("ip = ?", *condition.IP)
	}
	
	if condition.Status != nil {
		query = query.Where("status = ?", *condition.Status)
	}
	
	if condition.StartDate != nil {
		query = query.Where("DATE(created_at) >= ?", *condition.StartDate)
	}
	
	if condition.EndDate != nil {
		query = query.Where("DATE(created_at) <= ?", *condition.EndDate)
	}
	
	return query
}
