import type { PropType } from 'vue';
import type { TableBorder, TableChangeExtra, TableColumnData, TableComponents, TableData, TableDraggable, TableExpandable, TableOperationColumn, TableRowSelection } from './interface';
import { PaginationProps } from '../pagination';
import { VirtualListProps } from '../_components/virtual-list-v2/interface';
import { ScrollbarProps } from '../scrollbar';
import type { BaseType } from '../_utils/types';
declare const _default;
export default _default;
