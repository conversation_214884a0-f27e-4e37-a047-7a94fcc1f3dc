import type { App } from 'vue';
import type { ArcoIconOptions } from '../../_utils/types';
declare const IconPauseCircleFill: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
        size: {
            type: (NumberConstructor | StringConstructor)[];
        };
        strokeWidth: {
            type: NumberConstructor;
            default: number;
        };
        strokeLinecap: {
            type: StringConstructor;
            default: string;
            validator: (value: any) => boolean;
        };
        strokeLinejoin: {
            type: StringConstructor;
            default: string;
            validator: (value: any) => boolean;
        };
        rotate: NumberConstructor;
        spin: BooleanConstructor;
    }>> & Readonly<{
        onClick?: ((ev: MouseEvent) => any) | undefined;
    }>, {
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        innerStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
        onClick: (ev: MouseEvent) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        click: (ev: MouseEvent) => true;
    }, import("vue").PublicProps, {
        strokeWidth: number;
        strokeLinecap: string;
        strokeLinejoin: string;
        spin: boolean;
    }, true, {}, {}, import("@vue/runtime-core").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        size: {
            type: (NumberConstructor | StringConstructor)[];
        };
        strokeWidth: {
            type: NumberConstructor;
            default: number;
        };
        strokeLinecap: {
            type: StringConstructor;
            default: string;
            validator: (value: any) => boolean;
        };
        strokeLinejoin: {
            type: StringConstructor;
            default: string;
            validator: (value: any) => boolean;
        };
        rotate: NumberConstructor;
        spin: BooleanConstructor;
    }>> & Readonly<{
        onClick?: ((ev: MouseEvent) => any) | undefined;
    }>, {
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        innerStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
        onClick: (ev: MouseEvent) => void;
    }, {}, {}, {}, {
        strokeWidth: number;
        strokeLinecap: string;
        strokeLinejoin: string;
        spin: boolean;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    size: {
        type: (NumberConstructor | StringConstructor)[];
    };
    strokeWidth: {
        type: NumberConstructor;
        default: number;
    };
    strokeLinecap: {
        type: StringConstructor;
        default: string;
        validator: (value: any) => boolean;
    };
    strokeLinejoin: {
        type: StringConstructor;
        default: string;
        validator: (value: any) => boolean;
    };
    rotate: NumberConstructor;
    spin: BooleanConstructor;
}>> & Readonly<{
    onClick?: ((ev: MouseEvent) => any) | undefined;
}>, {
    cls: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    innerStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
    onClick: (ev: MouseEvent) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    click: (ev: MouseEvent) => true;
}, string, {
    strokeWidth: number;
    strokeLinecap: string;
    strokeLinejoin: string;
    spin: boolean;
}, {}, string, {}, import("@vue/runtime-core").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    install: (app: App, options?: ArcoIconOptions | undefined) => void;
};
export default IconPauseCircleFill;
