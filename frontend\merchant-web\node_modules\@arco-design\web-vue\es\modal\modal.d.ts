import type { CSSProperties, PropType, StyleValue } from 'vue';
import { ButtonProps } from '../button';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    visible: {
        type: BooleanConstructor;
        default: undefined;
    };
    defaultVisible: {
        type: BooleanConstructor;
        default: boolean;
    };
    width: {
        type: (StringConstructor | NumberConstructor)[];
    };
    top: {
        type: (StringConstructor | NumberConstructor)[];
    };
    mask: {
        type: BooleanConstructor;
        default: boolean;
    };
    title: {
        type: StringConstructor;
    };
    titleAlign: {
        type: PropType<"center" | "start">;
        default: string;
    };
    alignCenter: {
        type: BooleanConstructor;
        default: boolean;
    };
    unmountOnClose: BooleanConstructor;
    maskClosable: {
        type: BooleanConstructor;
        default: boolean;
    };
    hideCancel: {
        type: BooleanConstructor;
        default: boolean;
    };
    simple: {
        type: BooleanConstructor;
        default: (props: any) => any;
    };
    closable: {
        type: BooleanConstructor;
        default: boolean;
    };
    okText: StringConstructor;
    cancelText: StringConstructor;
    okLoading: {
        type: BooleanConstructor;
        default: boolean;
    };
    okButtonProps: {
        type: PropType<ButtonProps>;
    };
    cancelButtonProps: {
        type: PropType<ButtonProps>;
    };
    footer: {
        type: BooleanConstructor;
        default: boolean;
    };
    renderToBody: {
        type: BooleanConstructor;
        default: boolean;
    };
    popupContainer: {
        type: PropType<string | HTMLElement>;
        default: string;
    };
    maskStyle: {
        type: PropType<CSSProperties>;
    };
    modalClass: {
        type: PropType<string | any[]>;
    };
    modalStyle: {
        type: PropType<CSSProperties>;
    };
    onBeforeOk: {
        type: PropType<(done: (closed: boolean) => void) => void | boolean | Promise<void | boolean>>;
    };
    onBeforeCancel: {
        type: PropType<() => boolean>;
    };
    escToClose: {
        type: BooleanConstructor;
        default: boolean;
    };
    draggable: {
        type: BooleanConstructor;
        default: boolean;
    };
    fullscreen: {
        type: BooleanConstructor;
        default: boolean;
    };
    maskAnimationName: {
        type: StringConstructor;
        default: (props: Record<string, any>) => "fade-in-standard" | "fade-modal";
    };
    modalAnimationName: {
        type: StringConstructor;
        default: (props: Record<string, any>) => "zoom-in" | "zoom-modal";
    };
    bodyClass: {
        type: PropType<string | any[]>;
    };
    bodyStyle: {
        type: PropType<StyleValue>;
    };
    messageType: {
        type: PropType<"error" | "success" | "warning" | "info">;
    };
    hideTitle: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, {
    prefixCls: string;
    mounted: import("vue").Ref<boolean, boolean>;
    computedVisible: import("vue").ComputedRef<boolean>;
    containerRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
    wrapperRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
    mergedModalStyle: import("vue").ComputedRef<CSSProperties>;
    okDisplayText: import("vue").ComputedRef<string>;
    cancelDisplayText: import("vue").ComputedRef<string>;
    zIndex: Readonly<import("vue").Ref<number, number>>;
    handleOk: (e: Event) => Promise<void>;
    handleCancel: (e: Event) => void;
    handleMaskClick: (e: Event) => void;
    handleMaskMouseDown: (ev: Event) => void;
    handleOpen: () => void;
    handleClose: () => void;
    mergedOkLoading: import("vue").ComputedRef<boolean>;
    modalRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
    wrapperCls: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    modalCls: import("vue").ComputedRef<(string | any[] | {
        [x: string]: boolean;
    } | undefined)[]>;
    teleportContainer: import("vue").Ref<string | HTMLElement | undefined, string | HTMLElement | undefined>;
    handleMoveDown: (ev: MouseEvent) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    'update:visible': (visible: boolean) => true;
    ok: (e: Event) => true;
    cancel: (e: Event) => true;
    open: () => true;
    close: () => true;
    beforeOpen: () => true;
    beforeClose: () => true;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    visible: {
        type: BooleanConstructor;
        default: undefined;
    };
    defaultVisible: {
        type: BooleanConstructor;
        default: boolean;
    };
    width: {
        type: (StringConstructor | NumberConstructor)[];
    };
    top: {
        type: (StringConstructor | NumberConstructor)[];
    };
    mask: {
        type: BooleanConstructor;
        default: boolean;
    };
    title: {
        type: StringConstructor;
    };
    titleAlign: {
        type: PropType<"center" | "start">;
        default: string;
    };
    alignCenter: {
        type: BooleanConstructor;
        default: boolean;
    };
    unmountOnClose: BooleanConstructor;
    maskClosable: {
        type: BooleanConstructor;
        default: boolean;
    };
    hideCancel: {
        type: BooleanConstructor;
        default: boolean;
    };
    simple: {
        type: BooleanConstructor;
        default: (props: any) => any;
    };
    closable: {
        type: BooleanConstructor;
        default: boolean;
    };
    okText: StringConstructor;
    cancelText: StringConstructor;
    okLoading: {
        type: BooleanConstructor;
        default: boolean;
    };
    okButtonProps: {
        type: PropType<ButtonProps>;
    };
    cancelButtonProps: {
        type: PropType<ButtonProps>;
    };
    footer: {
        type: BooleanConstructor;
        default: boolean;
    };
    renderToBody: {
        type: BooleanConstructor;
        default: boolean;
    };
    popupContainer: {
        type: PropType<string | HTMLElement>;
        default: string;
    };
    maskStyle: {
        type: PropType<CSSProperties>;
    };
    modalClass: {
        type: PropType<string | any[]>;
    };
    modalStyle: {
        type: PropType<CSSProperties>;
    };
    onBeforeOk: {
        type: PropType<(done: (closed: boolean) => void) => void | boolean | Promise<void | boolean>>;
    };
    onBeforeCancel: {
        type: PropType<() => boolean>;
    };
    escToClose: {
        type: BooleanConstructor;
        default: boolean;
    };
    draggable: {
        type: BooleanConstructor;
        default: boolean;
    };
    fullscreen: {
        type: BooleanConstructor;
        default: boolean;
    };
    maskAnimationName: {
        type: StringConstructor;
        default: (props: Record<string, any>) => "fade-in-standard" | "fade-modal";
    };
    modalAnimationName: {
        type: StringConstructor;
        default: (props: Record<string, any>) => "zoom-in" | "zoom-modal";
    };
    bodyClass: {
        type: PropType<string | any[]>;
    };
    bodyStyle: {
        type: PropType<StyleValue>;
    };
    messageType: {
        type: PropType<"error" | "success" | "warning" | "info">;
    };
    hideTitle: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onOpen?: (() => any) | undefined;
    onCancel?: ((e: Event) => any) | undefined;
    onClose?: (() => any) | undefined;
    "onUpdate:visible"?: ((visible: boolean) => any) | undefined;
    onOk?: ((e: Event) => any) | undefined;
    onBeforeOpen?: (() => any) | undefined;
    onBeforeClose?: (() => any) | undefined;
}>, {
    unmountOnClose: boolean;
    popupContainer: string | HTMLElement;
    renderToBody: boolean;
    visible: boolean;
    closable: boolean;
    footer: boolean;
    mask: boolean;
    defaultVisible: boolean;
    maskClosable: boolean;
    escToClose: boolean;
    draggable: boolean;
    simple: boolean;
    okLoading: boolean;
    hideCancel: boolean;
    titleAlign: "center" | "start";
    alignCenter: boolean;
    fullscreen: boolean;
    maskAnimationName: string;
    modalAnimationName: string;
    hideTitle: boolean;
}, {}, {
    ClientOnly: import("vue").DefineComponent<{}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>[] | null | undefined, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    ArcoButton: {
        new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            shape: {
                type: PropType<"round" | "circle" | "square">;
            };
            status: {
                type: PropType<"normal" | "success" | "warning" | "danger">;
            };
            size: {
                type: PropType<"mini" | "medium" | "large" | "small">;
            };
            long: {
                type: BooleanConstructor;
                default: boolean;
            };
            loading: {
                type: BooleanConstructor;
                default: boolean;
            };
            disabled: {
                type: BooleanConstructor;
            };
            htmlType: {
                type: StringConstructor;
                default: string;
            };
            autofocus: {
                type: BooleanConstructor;
                default: boolean;
            };
            href: StringConstructor;
        }>> & Readonly<{
            onClick?: ((ev: MouseEvent) => any) | undefined;
        }>, {
            prefixCls: string;
            cls: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            mergedDisabled: import("vue").ComputedRef<boolean>;
            handleClick: (ev: MouseEvent) => void;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            click: (ev: MouseEvent) => true;
        }, import("vue").PublicProps, {
            disabled: boolean;
            autofocus: boolean;
            loading: boolean;
            long: boolean;
            htmlType: string;
        }, true, {}, {}, {
            IconLoading: any;
        } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
            P: {};
            B: {};
            D: {};
            C: {};
            M: {};
            Defaults: {};
        }, Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            shape: {
                type: PropType<"round" | "circle" | "square">;
            };
            status: {
                type: PropType<"normal" | "success" | "warning" | "danger">;
            };
            size: {
                type: PropType<"mini" | "medium" | "large" | "small">;
            };
            long: {
                type: BooleanConstructor;
                default: boolean;
            };
            loading: {
                type: BooleanConstructor;
                default: boolean;
            };
            disabled: {
                type: BooleanConstructor;
            };
            htmlType: {
                type: StringConstructor;
                default: string;
            };
            autofocus: {
                type: BooleanConstructor;
                default: boolean;
            };
            href: StringConstructor;
        }>> & Readonly<{
            onClick?: ((ev: MouseEvent) => any) | undefined;
        }>, {
            prefixCls: string;
            cls: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            mergedDisabled: import("vue").ComputedRef<boolean>;
            handleClick: (ev: MouseEvent) => void;
        }, {}, {}, {}, {
            disabled: boolean;
            autofocus: boolean;
            loading: boolean;
            long: boolean;
            htmlType: string;
        }>;
        __isFragment?: undefined;
        __isTeleport?: undefined;
        __isSuspense?: undefined;
    } & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
        type: {
            type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
        };
        shape: {
            type: PropType<"round" | "circle" | "square">;
        };
        status: {
            type: PropType<"normal" | "success" | "warning" | "danger">;
        };
        size: {
            type: PropType<"mini" | "medium" | "large" | "small">;
        };
        long: {
            type: BooleanConstructor;
            default: boolean;
        };
        loading: {
            type: BooleanConstructor;
            default: boolean;
        };
        disabled: {
            type: BooleanConstructor;
        };
        htmlType: {
            type: StringConstructor;
            default: string;
        };
        autofocus: {
            type: BooleanConstructor;
            default: boolean;
        };
        href: StringConstructor;
    }>> & Readonly<{
        onClick?: ((ev: MouseEvent) => any) | undefined;
    }>, {
        prefixCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        mergedDisabled: import("vue").ComputedRef<boolean>;
        handleClick: (ev: MouseEvent) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        click: (ev: MouseEvent) => true;
    }, string, {
        disabled: boolean;
        autofocus: boolean;
        loading: boolean;
        long: boolean;
        htmlType: string;
    }, {}, string, {}, {
        IconLoading: any;
    } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
        Group: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            type: {
                type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            status: {
                type: PropType<"normal" | "success" | "warning" | "danger">;
            };
            shape: {
                type: PropType<"round" | "circle" | "square">;
            };
            size: {
                type: PropType<"mini" | "medium" | "large" | "small">;
            };
            disabled: {
                type: BooleanConstructor;
            };
        }>, {
            prefixCls: string;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            status: {
                type: PropType<"normal" | "success" | "warning" | "danger">;
            };
            shape: {
                type: PropType<"round" | "circle" | "square">;
            };
            size: {
                type: PropType<"mini" | "medium" | "large" | "small">;
            };
            disabled: {
                type: BooleanConstructor;
            };
        }>> & Readonly<{}>, {
            disabled: boolean;
        }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        install: (app: import("vue").App<any>, options?: import("../_utils/types").ArcoOptions | undefined) => void;
    };
    IconHover: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>, {
        prefixCls: string;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{}>, {
        disabled: boolean;
        size: "mini" | "medium" | "large" | "small";
    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    IconClose: any;
    IconInfoCircleFill: any;
    IconCheckCircleFill: any;
    IconExclamationCircleFill: any;
    IconCloseCircleFill: any;
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
