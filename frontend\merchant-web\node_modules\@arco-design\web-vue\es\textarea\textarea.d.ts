import { CSSProperties, PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    modelValue: StringConstructor;
    defaultValue: {
        type: StringConstructor;
        default: string;
    };
    placeholder: StringConstructor;
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    error: {
        type: BooleanConstructor;
        default: boolean;
    };
    maxLength: {
        type: PropType<number | {
            length: number;
            errorOnly?: boolean | undefined;
        }>;
        default: number;
    };
    showWordLimit: {
        type: BooleanConstructor;
        default: boolean;
    };
    allowClear: {
        type: BooleanConstructor;
        default: boolean;
    };
    autoSize: {
        type: PropType<boolean | {
            minRows?: number | undefined;
            maxRows?: number | undefined;
        }>;
        default: boolean;
    };
    wordLength: {
        type: PropType<(value: string) => number>;
    };
    wordSlice: {
        type: PropType<(value: string, maxLength: number) => string>;
    };
    textareaAttrs: {
        type: PropType<Record<string, any>>;
    };
}>, {
    prefixCls: string;
    wrapperCls: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    textareaRef: import("vue").Ref<HTMLInputElement | undefined, HTMLInputElement | undefined>;
    textareaStyle: import("vue").Ref<CSSProperties | undefined, CSSProperties | undefined>;
    mirrorRef: import("vue").Ref<HTMLInputElement | undefined, HTMLInputElement | undefined>;
    mirrorStyle: import("vue").Ref<CSSProperties | undefined, CSSProperties | undefined>;
    computedValue: import("vue").ComputedRef<string>;
    showClearBtn: import("vue").ComputedRef<string | false>;
    valueLength: import("vue").ComputedRef<number>;
    computedMaxLength: import("vue").ComputedRef<number>;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    mergeTextareaAttrs: import("vue").ComputedRef<{
        [x: string]: any;
    }>;
    getWrapperAttrs: (attr: Record<string, any>) => Omit<{
        [x: string]: unknown;
    }, string>;
    getTextareaAttrs: (attr: Record<string, any>) => Pick<{
        [x: string]: unknown;
    }, string>;
    handleInput: (e: InputEvent) => void;
    handleFocus: (ev: FocusEvent) => void;
    handleBlur: (ev: FocusEvent) => void;
    handleComposition: (e: CompositionEvent) => void;
    handleClear: (ev: MouseEvent) => void;
    handleResize: () => void;
    handleMousedown: (e: MouseEvent) => void;
}, {}, {}, {
    focus(): void;
    blur(): void;
}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    'update:modelValue': (value: string) => true;
    input: (value: string, ev: Event) => true;
    change: (value: string, ev: Event) => true;
    clear: (ev: MouseEvent) => true;
    focus: (ev: FocusEvent) => true;
    blur: (ev: FocusEvent) => true;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    modelValue: StringConstructor;
    defaultValue: {
        type: StringConstructor;
        default: string;
    };
    placeholder: StringConstructor;
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    error: {
        type: BooleanConstructor;
        default: boolean;
    };
    maxLength: {
        type: PropType<number | {
            length: number;
            errorOnly?: boolean | undefined;
        }>;
        default: number;
    };
    showWordLimit: {
        type: BooleanConstructor;
        default: boolean;
    };
    allowClear: {
        type: BooleanConstructor;
        default: boolean;
    };
    autoSize: {
        type: PropType<boolean | {
            minRows?: number | undefined;
            maxRows?: number | undefined;
        }>;
        default: boolean;
    };
    wordLength: {
        type: PropType<(value: string) => number>;
    };
    wordSlice: {
        type: PropType<(value: string, maxLength: number) => string>;
    };
    textareaAttrs: {
        type: PropType<Record<string, any>>;
    };
}>> & Readonly<{
    onFocus?: ((ev: FocusEvent) => any) | undefined;
    onClear?: ((ev: MouseEvent) => any) | undefined;
    onChange?: ((value: string, ev: Event) => any) | undefined;
    onBlur?: ((ev: FocusEvent) => any) | undefined;
    onInput?: ((value: string, ev: Event) => any) | undefined;
    "onUpdate:modelValue"?: ((value: string) => any) | undefined;
}>, {
    disabled: boolean;
    error: boolean;
    allowClear: boolean;
    defaultValue: string;
    maxLength: number | {
        length: number;
        errorOnly?: boolean | undefined;
    };
    showWordLimit: boolean;
    autoSize: boolean | {
        minRows?: number | undefined;
        maxRows?: number | undefined;
    };
}, {}, {
    ResizeObserver: import("vue").DefineComponent<{}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }> | null, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "resize"[], "resize", import("vue").PublicProps, Readonly<{}> & Readonly<{
        onResize?: ((...args: any[]) => any) | undefined;
    }>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    IconHover: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>, {
        prefixCls: string;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{}>, {
        disabled: boolean;
        size: "mini" | "medium" | "large" | "small";
    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    IconClose: any;
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
