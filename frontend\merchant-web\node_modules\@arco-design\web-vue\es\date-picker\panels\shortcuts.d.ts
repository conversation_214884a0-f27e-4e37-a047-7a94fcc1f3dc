import { PropType } from 'vue';
import { ShortcutType } from '../interface';
import { isFunction } from '../../_utils/is';
export interface ShortcutsProps {
    prefixCls: string;
    shortcuts: ShortcutType[];
}
declare const _default: import("vue").DefineComponent<{
    prefixCls: string;
    shortcuts: ShortcutType[];
}, {
    datePickerT: (key: string, ...args: any[]) => any;
    onItemClick: (item: ShortcutType) => void;
    onItemMouseEnter: (item: ShortcutType) => void;
    onItemMouseLeave: (item: ShortcutType) => void;
    onNowClick: () => void;
    isFunction: typeof isFunction;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("item-click" | "item-mouse-enter" | "item-mouse-leave" | "now-click")[], "item-click" | "item-mouse-enter" | "item-mouse-leave" | "now-click", import("vue").PublicProps, Readonly<{
    prefixCls: string;
    shortcuts: ShortcutType[];
}> & Readonly<{
    "onItem-click"?: ((...args: any[]) => any) | undefined;
    "onItem-mouse-enter"?: ((...args: any[]) => any) | undefined;
    "onItem-mouse-leave"?: ((...args: any[]) => any) | undefined;
    "onNow-click"?: ((...args: any[]) => any) | undefined;
}>, {
    showNowBtn: boolean;
    shortcuts: ShortcutType[];
}, {}, {
    Button: {
        new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            shape: {
                type: PropType<"round" | "circle" | "square">;
            };
            status: {
                type: PropType<"normal" | "success" | "warning" | "danger">;
            };
            size: {
                type: PropType<"mini" | "medium" | "large" | "small">;
            };
            long: {
                type: BooleanConstructor;
                default: boolean;
            };
            loading: {
                type: BooleanConstructor;
                default: boolean;
            };
            disabled: {
                type: BooleanConstructor;
            };
            htmlType: {
                type: StringConstructor;
                default: string;
            };
            autofocus: {
                type: BooleanConstructor;
                default: boolean;
            };
            href: StringConstructor;
        }>> & Readonly<{
            onClick?: ((ev: MouseEvent) => any) | undefined;
        }>, {
            prefixCls: string;
            cls: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            mergedDisabled: import("vue").ComputedRef<boolean>;
            handleClick: (ev: MouseEvent) => void;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            click: (ev: MouseEvent) => true;
        }, import("vue").PublicProps, {
            disabled: boolean;
            autofocus: boolean;
            loading: boolean;
            long: boolean;
            htmlType: string;
        }, true, {}, {}, {
            IconLoading: any;
        } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
            P: {};
            B: {};
            D: {};
            C: {};
            M: {};
            Defaults: {};
        }, Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            shape: {
                type: PropType<"round" | "circle" | "square">;
            };
            status: {
                type: PropType<"normal" | "success" | "warning" | "danger">;
            };
            size: {
                type: PropType<"mini" | "medium" | "large" | "small">;
            };
            long: {
                type: BooleanConstructor;
                default: boolean;
            };
            loading: {
                type: BooleanConstructor;
                default: boolean;
            };
            disabled: {
                type: BooleanConstructor;
            };
            htmlType: {
                type: StringConstructor;
                default: string;
            };
            autofocus: {
                type: BooleanConstructor;
                default: boolean;
            };
            href: StringConstructor;
        }>> & Readonly<{
            onClick?: ((ev: MouseEvent) => any) | undefined;
        }>, {
            prefixCls: string;
            cls: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            mergedDisabled: import("vue").ComputedRef<boolean>;
            handleClick: (ev: MouseEvent) => void;
        }, {}, {}, {}, {
            disabled: boolean;
            autofocus: boolean;
            loading: boolean;
            long: boolean;
            htmlType: string;
        }>;
        __isFragment?: undefined;
        __isTeleport?: undefined;
        __isSuspense?: undefined;
    } & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
        type: {
            type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
        };
        shape: {
            type: PropType<"round" | "circle" | "square">;
        };
        status: {
            type: PropType<"normal" | "success" | "warning" | "danger">;
        };
        size: {
            type: PropType<"mini" | "medium" | "large" | "small">;
        };
        long: {
            type: BooleanConstructor;
            default: boolean;
        };
        loading: {
            type: BooleanConstructor;
            default: boolean;
        };
        disabled: {
            type: BooleanConstructor;
        };
        htmlType: {
            type: StringConstructor;
            default: string;
        };
        autofocus: {
            type: BooleanConstructor;
            default: boolean;
        };
        href: StringConstructor;
    }>> & Readonly<{
        onClick?: ((ev: MouseEvent) => any) | undefined;
    }>, {
        prefixCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        mergedDisabled: import("vue").ComputedRef<boolean>;
        handleClick: (ev: MouseEvent) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        click: (ev: MouseEvent) => true;
    }, string, {
        disabled: boolean;
        autofocus: boolean;
        loading: boolean;
        long: boolean;
        htmlType: string;
    }, {}, string, {}, {
        IconLoading: any;
    } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
        Group: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            type: {
                type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            status: {
                type: PropType<"normal" | "success" | "warning" | "danger">;
            };
            shape: {
                type: PropType<"round" | "circle" | "square">;
            };
            size: {
                type: PropType<"mini" | "medium" | "large" | "small">;
            };
            disabled: {
                type: BooleanConstructor;
            };
        }>, {
            prefixCls: string;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            status: {
                type: PropType<"normal" | "success" | "warning" | "danger">;
            };
            shape: {
                type: PropType<"round" | "circle" | "square">;
            };
            size: {
                type: PropType<"mini" | "medium" | "large" | "small">;
            };
            disabled: {
                type: BooleanConstructor;
            };
        }>> & Readonly<{}>, {
            disabled: boolean;
        }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        install: (app: import("vue").App<any>, options?: import("../../_utils/types").ArcoOptions | undefined) => void;
    };
    RenderFunction: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        renderFunc: {
            type: PropType<import("../../_components/render-function").RenderFunc>;
            required: true;
        };
    }>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        renderFunc: {
            type: PropType<import("../../_components/render-function").RenderFunc>;
            required: true;
        };
    }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
