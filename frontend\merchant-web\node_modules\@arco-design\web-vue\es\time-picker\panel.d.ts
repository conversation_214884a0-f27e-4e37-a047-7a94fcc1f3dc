import { PropType } from 'vue';
import { Dayjs } from 'dayjs';
declare const _default: import("vue").DefineComponent<{
    value?: Dayjs | undefined;
    defaultValue?: Dayjs | undefined;
    format: string;
    visible: boolean;
    hideFooter: boolean;
    isRange: boolean;
    disabled: boolean;
    use12Hours: boolean;
    step?: {
        hour?: number | undefined;
        minute?: number | undefined;
        second?: number | undefined;
    } | undefined;
    disabledHours?: (() => number[]) | undefined;
    disabledMinutes?: ((selectedHour?: number | undefined) => number[]) | undefined;
    disabledSeconds?: ((selectedHour?: number | undefined, selectedMinute?: number | undefined) => number[]) | undefined;
    hideDisabledOptions: boolean;
}, {
    prefixCls: string;
    t: (key: string, ...args: any[]) => string;
    hours: import("vue").ComputedRef<import("./interface").TimeList>;
    minutes: import("vue").ComputedRef<import("./interface").TimeList>;
    seconds: import("vue").ComputedRef<import("./interface").TimeList>;
    ampmList: import("vue").ComputedRef<import("./interface").TimeList>;
    selectedValue: import("vue").Ref<{
        clone: () => Dayjs;
        isValid: () => boolean;
        year: {
            (): number;
            (value: number): Dayjs;
        };
        month: {
            (): number;
            (value: number): Dayjs;
        };
        date: {
            (): number;
            (value: number): Dayjs;
        };
        day: {
            (): 0 | 1 | 2 | 4 | 3 | 6 | 5;
            (value: number): Dayjs;
        };
        hour: {
            (): number;
            (value: number): Dayjs;
        };
        minute: {
            (): number;
            (value: number): Dayjs;
        };
        second: {
            (): number;
            (value: number): Dayjs;
        };
        millisecond: {
            (): number;
            (value: number): Dayjs;
        };
        set: (unit: import("dayjs").UnitType, value: number) => Dayjs;
        get: (unit: import("dayjs").UnitType) => number;
        add: {
            (value: number, unit?: import("dayjs").ManipulateType | undefined): Dayjs;
            (value: number, unit: import("dayjs").QUnitType): Dayjs;
        };
        subtract: {
            (value: number, unit?: import("dayjs").ManipulateType | undefined): Dayjs;
            (value: number, unit: import("dayjs").QUnitType): Dayjs;
        };
        startOf: {
            (unit: import("dayjs").OpUnitType): Dayjs;
            (unit: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q"): Dayjs;
        };
        endOf: {
            (unit: import("dayjs").OpUnitType): Dayjs;
            (unit: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q"): Dayjs;
        };
        format: (template?: string | undefined) => string;
        diff: (date?: string | number | Date | Dayjs | null | undefined, unit?: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q" | undefined, float?: boolean | undefined) => number;
        valueOf: () => number;
        unix: () => number;
        daysInMonth: () => number;
        toDate: () => Date;
        toJSON: () => string;
        toISOString: () => string;
        toString: () => string;
        utcOffset: () => number;
        isBefore: {
            (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
            (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
        };
        isSame: {
            (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
            (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
        };
        isAfter: {
            (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
            (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
        };
        locale: {
            (): string;
            (preset: string | ILocale, object?: Partial<ILocale> | undefined): Dayjs;
        };
        isBetween: (a: string | number | Date | Dayjs | null | undefined, b: string | number | Date | Dayjs | null | undefined, c?: import("dayjs").OpUnitType | null | undefined, d?: "()" | "[]" | "[)" | "(]" | undefined) => boolean;
        week: {
            (): number;
            (value: number): Dayjs;
        };
        weekYear: () => number;
        quarter: {
            (): number;
            (quarter: number): Dayjs;
        };
    } | undefined, Dayjs | {
        clone: () => Dayjs;
        isValid: () => boolean;
        year: {
            (): number;
            (value: number): Dayjs;
        };
        month: {
            (): number;
            (value: number): Dayjs;
        };
        date: {
            (): number;
            (value: number): Dayjs;
        };
        day: {
            (): 0 | 1 | 2 | 4 | 3 | 6 | 5;
            (value: number): Dayjs;
        };
        hour: {
            (): number;
            (value: number): Dayjs;
        };
        minute: {
            (): number;
            (value: number): Dayjs;
        };
        second: {
            (): number;
            (value: number): Dayjs;
        };
        millisecond: {
            (): number;
            (value: number): Dayjs;
        };
        set: (unit: import("dayjs").UnitType, value: number) => Dayjs;
        get: (unit: import("dayjs").UnitType) => number;
        add: {
            (value: number, unit?: import("dayjs").ManipulateType | undefined): Dayjs;
            (value: number, unit: import("dayjs").QUnitType): Dayjs;
        };
        subtract: {
            (value: number, unit?: import("dayjs").ManipulateType | undefined): Dayjs;
            (value: number, unit: import("dayjs").QUnitType): Dayjs;
        };
        startOf: {
            (unit: import("dayjs").OpUnitType): Dayjs;
            (unit: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q"): Dayjs;
        };
        endOf: {
            (unit: import("dayjs").OpUnitType): Dayjs;
            (unit: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q"): Dayjs;
        };
        format: (template?: string | undefined) => string;
        diff: (date?: string | number | Date | Dayjs | null | undefined, unit?: "D" | "M" | "y" | "s" | "millisecond" | "second" | "minute" | "hour" | "day" | "month" | "year" | "date" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "week" | "weeks" | "w" | "quarter" | "quarters" | "Q" | undefined, float?: boolean | undefined) => number;
        valueOf: () => number;
        unix: () => number;
        daysInMonth: () => number;
        toDate: () => Date;
        toJSON: () => string;
        toISOString: () => string;
        toString: () => string;
        utcOffset: () => number;
        isBefore: {
            (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
            (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
        };
        isSame: {
            (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
            (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
        };
        isAfter: {
            (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
            (date?: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
        };
        locale: {
            (): string;
            (preset: string | ILocale, object?: Partial<ILocale> | undefined): Dayjs;
        };
        isBetween: (a: string | number | Date | Dayjs | null | undefined, b: string | number | Date | Dayjs | null | undefined, c?: import("dayjs").OpUnitType | null | undefined, d?: "()" | "[]" | "[)" | "(]" | undefined) => boolean;
        week: {
            (): number;
            (value: number): Dayjs;
        };
        weekYear: () => number;
        quarter: {
            (): number;
            (quarter: number): Dayjs;
        };
    } | undefined>;
    selectedHour: import("vue").ComputedRef<number | undefined>;
    selectedMinute: import("vue").ComputedRef<number | undefined>;
    selectedSecond: import("vue").ComputedRef<number | undefined>;
    selectedAmpm: import("vue").ComputedRef<"pm" | "am">;
    computedUse12Hours: import("vue").ComputedRef<boolean>;
    confirmBtnDisabled: import("vue").ComputedRef<boolean>;
    columns: import("vue").ComputedRef<string[]>;
    onSelect: (value: number | string, type?: 'hour' | 'minute' | 'second' | 'ampm') => void;
    onSelectNow(): void;
    onConfirm(): void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    select: (value: Dayjs) => boolean;
    confirm: (value: Dayjs) => boolean;
}, string, import("vue").PublicProps, Readonly<{
    value?: Dayjs | undefined;
    defaultValue?: Dayjs | undefined;
    format: string;
    visible: boolean;
    hideFooter: boolean;
    isRange: boolean;
    disabled: boolean;
    use12Hours: boolean;
    step?: {
        hour?: number | undefined;
        minute?: number | undefined;
        second?: number | undefined;
    } | undefined;
    disabledHours?: (() => number[]) | undefined;
    disabledMinutes?: ((selectedHour?: number | undefined) => number[]) | undefined;
    disabledSeconds?: ((selectedHour?: number | undefined, selectedMinute?: number | undefined) => number[]) | undefined;
    hideDisabledOptions: boolean;
}> & Readonly<{
    onSelect?: ((value: Dayjs) => any) | undefined;
    onConfirm?: ((value: Dayjs) => any) | undefined;
}>, {
    disabled: boolean;
    visible: boolean;
    format: string;
    hideFooter: boolean;
    use12Hours: boolean;
    hideDisabledOptions: boolean;
    isRange: boolean;
}, {}, {
    TimeColumn: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        prefixCls: {
            type: StringConstructor;
            required: true;
        };
        list: {
            type: PropType<import("./interface").TimeList>;
            required: true;
        };
        value: {
            type: (StringConstructor | NumberConstructor)[];
        };
        visible: {
            type: BooleanConstructor;
        };
    }>, {
        refWrapper: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
        refMap: import("vue").Ref<Map<string | number, HTMLElement> & Omit<Map<string | number, HTMLElement>, keyof Map<any, any>>, Map<string | number, HTMLElement> | (Map<string | number, HTMLElement> & Omit<Map<string | number, HTMLElement>, keyof Map<any, any>>)>;
        onItemRef(el: HTMLElement, item: import("./interface").TimeListItem): void;
        onItemClick(item: import("./interface").TimeListItem): void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "select"[], "select", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        prefixCls: {
            type: StringConstructor;
            required: true;
        };
        list: {
            type: PropType<import("./interface").TimeList>;
            required: true;
        };
        value: {
            type: (StringConstructor | NumberConstructor)[];
        };
        visible: {
            type: BooleanConstructor;
        };
    }>> & Readonly<{
        onSelect?: ((...args: any[]) => any) | undefined;
    }>, {
        visible: boolean;
    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    Button: {
        new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            shape: {
                type: PropType<"round" | "circle" | "square">;
            };
            status: {
                type: PropType<"normal" | "success" | "warning" | "danger">;
            };
            size: {
                type: PropType<"mini" | "medium" | "large" | "small">;
            };
            long: {
                type: BooleanConstructor;
                default: boolean;
            };
            loading: {
                type: BooleanConstructor;
                default: boolean;
            };
            disabled: {
                type: BooleanConstructor;
            };
            htmlType: {
                type: StringConstructor;
                default: string;
            };
            autofocus: {
                type: BooleanConstructor;
                default: boolean;
            };
            href: StringConstructor;
        }>> & Readonly<{
            onClick?: ((ev: MouseEvent) => any) | undefined;
        }>, {
            prefixCls: string;
            cls: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            mergedDisabled: import("vue").ComputedRef<boolean>;
            handleClick: (ev: MouseEvent) => void;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            click: (ev: MouseEvent) => true;
        }, import("vue").PublicProps, {
            disabled: boolean;
            autofocus: boolean;
            loading: boolean;
            long: boolean;
            htmlType: string;
        }, true, {}, {}, {
            IconLoading: any;
        } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
            P: {};
            B: {};
            D: {};
            C: {};
            M: {};
            Defaults: {};
        }, Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            shape: {
                type: PropType<"round" | "circle" | "square">;
            };
            status: {
                type: PropType<"normal" | "success" | "warning" | "danger">;
            };
            size: {
                type: PropType<"mini" | "medium" | "large" | "small">;
            };
            long: {
                type: BooleanConstructor;
                default: boolean;
            };
            loading: {
                type: BooleanConstructor;
                default: boolean;
            };
            disabled: {
                type: BooleanConstructor;
            };
            htmlType: {
                type: StringConstructor;
                default: string;
            };
            autofocus: {
                type: BooleanConstructor;
                default: boolean;
            };
            href: StringConstructor;
        }>> & Readonly<{
            onClick?: ((ev: MouseEvent) => any) | undefined;
        }>, {
            prefixCls: string;
            cls: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            mergedDisabled: import("vue").ComputedRef<boolean>;
            handleClick: (ev: MouseEvent) => void;
        }, {}, {}, {}, {
            disabled: boolean;
            autofocus: boolean;
            loading: boolean;
            long: boolean;
            htmlType: string;
        }>;
        __isFragment?: undefined;
        __isTeleport?: undefined;
        __isSuspense?: undefined;
    } & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
        type: {
            type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
        };
        shape: {
            type: PropType<"round" | "circle" | "square">;
        };
        status: {
            type: PropType<"normal" | "success" | "warning" | "danger">;
        };
        size: {
            type: PropType<"mini" | "medium" | "large" | "small">;
        };
        long: {
            type: BooleanConstructor;
            default: boolean;
        };
        loading: {
            type: BooleanConstructor;
            default: boolean;
        };
        disabled: {
            type: BooleanConstructor;
        };
        htmlType: {
            type: StringConstructor;
            default: string;
        };
        autofocus: {
            type: BooleanConstructor;
            default: boolean;
        };
        href: StringConstructor;
    }>> & Readonly<{
        onClick?: ((ev: MouseEvent) => any) | undefined;
    }>, {
        prefixCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        mergedDisabled: import("vue").ComputedRef<boolean>;
        handleClick: (ev: MouseEvent) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        click: (ev: MouseEvent) => true;
    }, string, {
        disabled: boolean;
        autofocus: boolean;
        loading: boolean;
        long: boolean;
        htmlType: string;
    }, {}, string, {}, {
        IconLoading: any;
    } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
        Group: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            type: {
                type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            status: {
                type: PropType<"normal" | "success" | "warning" | "danger">;
            };
            shape: {
                type: PropType<"round" | "circle" | "square">;
            };
            size: {
                type: PropType<"mini" | "medium" | "large" | "small">;
            };
            disabled: {
                type: BooleanConstructor;
            };
        }>, {
            prefixCls: string;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            status: {
                type: PropType<"normal" | "success" | "warning" | "danger">;
            };
            shape: {
                type: PropType<"round" | "circle" | "square">;
            };
            size: {
                type: PropType<"mini" | "medium" | "large" | "small">;
            };
            disabled: {
                type: BooleanConstructor;
            };
        }>> & Readonly<{}>, {
            disabled: boolean;
        }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        install: (app: import("vue").App<any>, options?: import("../_utils/types").ArcoOptions | undefined) => void;
    };
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
