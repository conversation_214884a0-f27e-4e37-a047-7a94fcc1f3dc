import type { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _Tag from './tag';
declare const Tag: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
        color: {
            type: import("vue").PropType<string>;
        };
        size: {
            type: import("vue").PropType<"medium" | "large" | "small">;
        };
        bordered: {
            type: BooleanConstructor;
            default: boolean;
        };
        visible: {
            type: BooleanConstructor;
            default: undefined;
        };
        defaultVisible: {
            type: BooleanConstructor;
            default: boolean;
        };
        loading: {
            type: BooleanConstructor;
            default: boolean;
        };
        closable: {
            type: BooleanConstructor;
            default: boolean;
        };
        checkable: {
            type: BooleanConstructor;
            default: boolean;
        };
        checked: {
            type: BooleanConstructor;
            default: undefined;
        };
        defaultChecked: {
            type: BooleanConstructor;
            default: boolean;
        };
        nowrap: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{
        onClose?: ((ev: MouseEvent) => any) | undefined;
        "onUpdate:visible"?: ((visible: boolean) => any) | undefined;
        "onUpdate:checked"?: ((checked: boolean) => any) | undefined;
        onCheck?: ((checked: boolean, ev: MouseEvent) => any) | undefined;
    }>, {
        prefixCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean | "" | undefined;
        })[]>;
        style: import("vue").ComputedRef<import("vue").CSSProperties | undefined>;
        computedVisible: import("vue").ComputedRef<boolean>;
        computedChecked: import("vue").ComputedRef<boolean>;
        handleClick: (ev: MouseEvent) => void;
        handleClose: (ev: MouseEvent) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        'update:visible': (visible: boolean) => true;
        'update:checked': (checked: boolean) => true;
        close: (ev: MouseEvent) => true;
        check: (checked: boolean, ev: MouseEvent) => true;
    }, import("vue").PublicProps, {
        visible: boolean;
        nowrap: boolean;
        loading: boolean;
        bordered: boolean;
        closable: boolean;
        defaultVisible: boolean;
        checked: boolean;
        checkable: boolean;
        defaultChecked: boolean;
    }, true, {}, {}, {
        IconHover: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            prefix: {
                type: StringConstructor;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
                default: string;
            };
            disabled: {
                type: BooleanConstructor;
                default: boolean;
            };
        }>, {
            prefixCls: string;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            prefix: {
                type: StringConstructor;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
                default: string;
            };
            disabled: {
                type: BooleanConstructor;
                default: boolean;
            };
        }>> & Readonly<{}>, {
            disabled: boolean;
            size: "mini" | "medium" | "large" | "small";
        }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        IconClose: any;
        IconLoading: any;
    } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        color: {
            type: import("vue").PropType<string>;
        };
        size: {
            type: import("vue").PropType<"medium" | "large" | "small">;
        };
        bordered: {
            type: BooleanConstructor;
            default: boolean;
        };
        visible: {
            type: BooleanConstructor;
            default: undefined;
        };
        defaultVisible: {
            type: BooleanConstructor;
            default: boolean;
        };
        loading: {
            type: BooleanConstructor;
            default: boolean;
        };
        closable: {
            type: BooleanConstructor;
            default: boolean;
        };
        checkable: {
            type: BooleanConstructor;
            default: boolean;
        };
        checked: {
            type: BooleanConstructor;
            default: undefined;
        };
        defaultChecked: {
            type: BooleanConstructor;
            default: boolean;
        };
        nowrap: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{
        onClose?: ((ev: MouseEvent) => any) | undefined;
        "onUpdate:visible"?: ((visible: boolean) => any) | undefined;
        "onUpdate:checked"?: ((checked: boolean) => any) | undefined;
        onCheck?: ((checked: boolean, ev: MouseEvent) => any) | undefined;
    }>, {
        prefixCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean | "" | undefined;
        })[]>;
        style: import("vue").ComputedRef<import("vue").CSSProperties | undefined>;
        computedVisible: import("vue").ComputedRef<boolean>;
        computedChecked: import("vue").ComputedRef<boolean>;
        handleClick: (ev: MouseEvent) => void;
        handleClose: (ev: MouseEvent) => void;
    }, {}, {}, {}, {
        visible: boolean;
        nowrap: boolean;
        loading: boolean;
        bordered: boolean;
        closable: boolean;
        defaultVisible: boolean;
        checked: boolean;
        checkable: boolean;
        defaultChecked: boolean;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    color: {
        type: import("vue").PropType<string>;
    };
    size: {
        type: import("vue").PropType<"medium" | "large" | "small">;
    };
    bordered: {
        type: BooleanConstructor;
        default: boolean;
    };
    visible: {
        type: BooleanConstructor;
        default: undefined;
    };
    defaultVisible: {
        type: BooleanConstructor;
        default: boolean;
    };
    loading: {
        type: BooleanConstructor;
        default: boolean;
    };
    closable: {
        type: BooleanConstructor;
        default: boolean;
    };
    checkable: {
        type: BooleanConstructor;
        default: boolean;
    };
    checked: {
        type: BooleanConstructor;
        default: undefined;
    };
    defaultChecked: {
        type: BooleanConstructor;
        default: boolean;
    };
    nowrap: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onClose?: ((ev: MouseEvent) => any) | undefined;
    "onUpdate:visible"?: ((visible: boolean) => any) | undefined;
    "onUpdate:checked"?: ((checked: boolean) => any) | undefined;
    onCheck?: ((checked: boolean, ev: MouseEvent) => any) | undefined;
}>, {
    prefixCls: string;
    cls: import("vue").ComputedRef<(string | {
        [x: string]: boolean | "" | undefined;
    })[]>;
    style: import("vue").ComputedRef<import("vue").CSSProperties | undefined>;
    computedVisible: import("vue").ComputedRef<boolean>;
    computedChecked: import("vue").ComputedRef<boolean>;
    handleClick: (ev: MouseEvent) => void;
    handleClose: (ev: MouseEvent) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    'update:visible': (visible: boolean) => true;
    'update:checked': (checked: boolean) => true;
    close: (ev: MouseEvent) => true;
    check: (checked: boolean, ev: MouseEvent) => true;
}, string, {
    visible: boolean;
    nowrap: boolean;
    loading: boolean;
    bordered: boolean;
    closable: boolean;
    defaultVisible: boolean;
    checked: boolean;
    checkable: boolean;
    defaultChecked: boolean;
}, {}, string, {}, {
    IconHover: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>, {
        prefixCls: string;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{}>, {
        disabled: boolean;
        size: "mini" | "medium" | "large" | "small";
    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    IconClose: any;
    IconLoading: any;
} & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type TagInstance = InstanceType<typeof _Tag>;
export type { TagProps, TagColor } from './interface';
export default Tag;
