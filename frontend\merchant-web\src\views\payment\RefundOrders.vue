<template>
  <div class="refund-orders">
    <h2>退款订单</h2>
    
    <!-- 搜索表单 -->
    <a-card class="search-card">
      <a-form :model="searchForm" layout="inline">
        <a-form-item label="退款单号">
          <a-input v-model="searchForm.refundNo" placeholder="请输入退款单号" />
        </a-form-item>
        <a-form-item label="原订单号">
          <a-input v-model="searchForm.orderNo" placeholder="请输入原订单号" />
        </a-form-item>
        <a-form-item label="状态">
          <a-select v-model="searchForm.status" placeholder="请选择状态" style="width: 120px">
            <a-option value="">全部</a-option>
            <a-option value="pending">处理中</a-option>
            <a-option value="success">退款成功</a-option>
            <a-option value="failed">退款失败</a-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">搜索</a-button>
          <a-button @click="handleReset" style="margin-left: 8px">重置</a-button>
        </a-form-item>
      </a-form>
    </a-card>
    
    <!-- 退款表格 -->
    <a-card class="table-card">
      <a-table 
        :columns="columns" 
        :data="refunds" 
        :pagination="pagination"
        :loading="loading"
        @page-change="handlePageChange"
      >
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template #amount="{ record }">
          ¥{{ record.amount }}
        </template>
        <template #actions="{ record }">
          <a-button type="text" size="small" @click="viewDetail(record)">
            查看详情
          </a-button>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'

// 搜索表单
const searchForm = reactive({
  refundNo: '',
  orderNo: '',
  status: ''
})

// 表格列定义
const columns = [
  {
    title: '退款单号',
    dataIndex: 'refundNo',
    width: 200
  },
  {
    title: '原订单号',
    dataIndex: 'orderNo',
    width: 200
  },
  {
    title: '退款金额',
    dataIndex: 'amount',
    slotName: 'amount',
    width: 120
  },
  {
    title: '退款原因',
    dataIndex: 'reason',
    width: 150
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status',
    width: 100
  },
  {
    title: '申请时间',
    dataIndex: 'createTime',
    width: 180
  },
  {
    title: '处理时间',
    dataIndex: 'processTime',
    width: 180
  },
  {
    title: '操作',
    slotName: 'actions',
    width: 100
  }
]

// 退款数据
const refunds = ref([
  {
    id: 1,
    refundNo: 'REF202401010001',
    orderNo: 'PAY202401010001',
    amount: 299.00,
    reason: '商品质量问题',
    status: 'success',
    createTime: '2024-01-01 11:30:00',
    processTime: '2024-01-01 11:35:00'
  },
  {
    id: 2,
    refundNo: 'REF202401010002',
    orderNo: 'PAY202401010002',
    amount: 158.50,
    reason: '用户取消订单',
    status: 'pending',
    createTime: '2024-01-01 11:25:00',
    processTime: ''
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 50,
  showTotal: true
})

// 加载状态
const loading = ref(false)

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    success: 'green',
    pending: 'orange',
    failed: 'red'
  }
  return colorMap[status] || 'gray'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    success: '退款成功',
    pending: '处理中',
    failed: '退款失败'
  }
  return textMap[status] || '未知'
}

// 搜索
const handleSearch = () => {
  console.log('搜索退款订单:', searchForm)
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    refundNo: '',
    orderNo: '',
    status: ''
  })
  handleSearch()
}

// 分页变化
const handlePageChange = (page: number) => {
  pagination.current = page
}

// 查看详情
const viewDetail = (record: any) => {
  console.log('查看退款详情:', record)
}

// 页面加载时获取数据
onMounted(() => {
  console.log('加载退款订单数据')
})
</script>

<style scoped>
.refund-orders {
  padding: 0;
}

.search-card {
  margin-bottom: 16px;
}

.table-card {
  margin-bottom: 16px;
}
</style>
