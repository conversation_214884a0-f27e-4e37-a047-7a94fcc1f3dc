import type { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _Link from './link';
declare const Link: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
        href: StringConstructor;
        status: {
            type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
            default: string;
        };
        hoverable: {
            type: BooleanConstructor;
            default: boolean;
        };
        icon: BooleanConstructor;
        loading: BooleanConstructor;
        disabled: BooleanConstructor;
    }>> & Readonly<{
        onClick?: ((ev: MouseEvent) => any) | undefined;
    }>, {
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        prefixCls: string;
        showIcon: import("vue").ComputedRef<boolean>;
        handleClick: (ev: MouseEvent) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        click: (ev: MouseEvent) => true;
    }, import("vue").PublicProps, {
        disabled: boolean;
        icon: boolean;
        loading: boolean;
        status: "normal" | "success" | "warning" | "danger";
        hoverable: boolean;
    }, true, {}, {}, {
        IconLink: any;
        IconLoading: any;
    } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        href: StringConstructor;
        status: {
            type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
            default: string;
        };
        hoverable: {
            type: BooleanConstructor;
            default: boolean;
        };
        icon: BooleanConstructor;
        loading: BooleanConstructor;
        disabled: BooleanConstructor;
    }>> & Readonly<{
        onClick?: ((ev: MouseEvent) => any) | undefined;
    }>, {
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        prefixCls: string;
        showIcon: import("vue").ComputedRef<boolean>;
        handleClick: (ev: MouseEvent) => void;
    }, {}, {}, {}, {
        disabled: boolean;
        icon: boolean;
        loading: boolean;
        status: "normal" | "success" | "warning" | "danger";
        hoverable: boolean;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    href: StringConstructor;
    status: {
        type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
        default: string;
    };
    hoverable: {
        type: BooleanConstructor;
        default: boolean;
    };
    icon: BooleanConstructor;
    loading: BooleanConstructor;
    disabled: BooleanConstructor;
}>> & Readonly<{
    onClick?: ((ev: MouseEvent) => any) | undefined;
}>, {
    cls: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    prefixCls: string;
    showIcon: import("vue").ComputedRef<boolean>;
    handleClick: (ev: MouseEvent) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    click: (ev: MouseEvent) => true;
}, string, {
    disabled: boolean;
    icon: boolean;
    loading: boolean;
    status: "normal" | "success" | "warning" | "danger";
    hoverable: boolean;
}, {}, string, {}, {
    IconLink: any;
    IconLoading: any;
} & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type LinkInstance = InstanceType<typeof _Link>;
export default Link;
