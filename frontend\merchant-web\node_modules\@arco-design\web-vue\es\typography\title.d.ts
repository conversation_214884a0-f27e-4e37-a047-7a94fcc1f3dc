import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    heading: {
        type: PropType<1 | 2 | 4 | 3 | 6 | 5>;
        default: number;
    };
}>, {
    component: import("vue").ComputedRef<keyof HTMLElementTagNameMap>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    heading: {
        type: PropType<1 | 2 | 4 | 3 | 6 | 5>;
        default: number;
    };
}>> & Readonly<{}>, {
    heading: 1 | 2 | 4 | 3 | 6 | 5;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
