import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    percent: {
        type: NumberConstructor;
        default: number;
    };
    animation: {
        type: BooleanConstructor;
        default: boolean;
    };
    size: {
        type: PropType<"medium" | "large" | "small">;
        default: string;
    };
    strokeWidth: {
        type: NumberConstructor;
        default: number;
    };
    width: {
        type: (StringConstructor | NumberConstructor)[];
        default: string;
    };
    color: {
        type: (ObjectConstructor | StringConstructor)[];
        default: undefined;
    };
    trackColor: StringConstructor;
    formatText: {
        type: FunctionConstructor;
        default: undefined;
    };
    status: {
        type: PropType<"normal" | "success" | "warning" | "danger">;
    };
    showText: BooleanConstructor;
}>, {
    prefixCls: string;
    style: import("vue").ComputedRef<{
        width: string | number;
        height: string;
        backgroundColor: string | undefined;
    }>;
    barStyle: import("vue").ComputedRef<{
        width: string;
    } | {
        backgroundImage: string;
        backgroundColor?: undefined;
        width: string;
    } | {
        backgroundColor: string;
        backgroundImage?: undefined;
        width: string;
    }>;
    text: import("vue").ComputedRef<string>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    percent: {
        type: NumberConstructor;
        default: number;
    };
    animation: {
        type: BooleanConstructor;
        default: boolean;
    };
    size: {
        type: PropType<"medium" | "large" | "small">;
        default: string;
    };
    strokeWidth: {
        type: NumberConstructor;
        default: number;
    };
    width: {
        type: (StringConstructor | NumberConstructor)[];
        default: string;
    };
    color: {
        type: (ObjectConstructor | StringConstructor)[];
        default: undefined;
    };
    trackColor: StringConstructor;
    formatText: {
        type: FunctionConstructor;
        default: undefined;
    };
    status: {
        type: PropType<"normal" | "success" | "warning" | "danger">;
    };
    showText: BooleanConstructor;
}>> & Readonly<{}>, {
    size: "medium" | "large" | "small";
    color: string | Record<string, any>;
    animation: boolean;
    strokeWidth: number;
    width: string | number;
    showText: boolean;
    percent: number;
    formatText: Function;
}, {}, {
    IconExclamationCircleFill: any;
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
