import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    shape: {
        type: PropType<"circle" | "square">;
        default: string;
    };
    size: {
        type: StringConstructor;
        default: string;
    };
}>, {
    prefixCls: string;
    cls: import("vue").ComputedRef<string[]>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    shape: {
        type: PropType<"circle" | "square">;
        default: string;
    };
    size: {
        type: StringConstructor;
        default: string;
    };
}>> & Readonly<{}>, {
    size: string;
    shape: "circle" | "square";
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
