package repository

import (
	"context"
	"payment-gateway/internal/notification/model"
	"time"

	"gorm.io/gorm"
)

// EmailTemplateRepository 邮件模板仓储接口
type EmailTemplateRepository interface {
	// 基础操作
	Create(ctx context.Context, template *model.EmailTemplate) error
	GetByID(ctx context.Context, id uint) (*model.EmailTemplate, error)
	GetByCode(ctx context.Context, templateCode string) (*model.EmailTemplate, error)
	Update(ctx context.Context, template *model.EmailTemplate) error
	Delete(ctx context.Context, id uint) error
	
	// 查询操作
	List(ctx context.Context, condition *EmailTemplateQueryCondition) ([]*model.EmailTemplate, int64, error)
	GetByCategory(ctx context.Context, category string) ([]*model.EmailTemplate, error)
	GetActiveTemplates(ctx context.Context) ([]*model.EmailTemplate, error)
	
	// 状态操作
	UpdateStatus(ctx context.Context, id uint, status int) error
	
	// 验证操作
	CheckCodeExists(ctx context.Context, templateCode string, excludeID uint) bool
}

// EmailLogRepository 邮件日志仓储接口
type EmailLogRepository interface {
	// 基础操作
	Create(ctx context.Context, log *model.EmailLog) error
	GetByID(ctx context.Context, id uint) (*model.EmailLog, error)
	Update(ctx context.Context, log *model.EmailLog) error
	List(ctx context.Context, condition *EmailLogQueryCondition) ([]*model.EmailLog, int64, error)
	Delete(ctx context.Context, id uint) error
	
	// 查询操作
	GetByEmail(ctx context.Context, email string, limit int) ([]*model.EmailLog, error)
	GetByUserID(ctx context.Context, userID uint, limit int) ([]*model.EmailLog, error)
	GetByTemplateCode(ctx context.Context, templateCode string, limit int) ([]*model.EmailLog, error)
	
	// 统计操作
	GetEmailStats(ctx context.Context, startDate, endDate time.Time) (*EmailStats, error)
	GetFailedEmails(ctx context.Context, limit int) ([]*model.EmailLog, error)
	
	// 清理操作
	CleanOldLogs(ctx context.Context, beforeDate time.Time) (int64, error)
}

// EmailTemplateQueryCondition 邮件模板查询条件
type EmailTemplateQueryCondition struct {
	TemplateCode string
	TemplateName string
	Category     string
	Status       int
	Page         int
	PageSize     int
	OrderBy      string
}

// EmailLogQueryCondition 邮件日志查询条件
type EmailLogQueryCondition struct {
	TemplateCode string
	ToEmail      string
	UserID       uint
	UserType     string
	Status       int
	Provider     string
	StartDate    time.Time
	EndDate      time.Time
	Page         int
	PageSize     int
	OrderBy      string
}

// EmailStats 邮件统计
type EmailStats struct {
	TotalCount   int64   `json:"total_count"`
	SuccessCount int64   `json:"success_count"`
	FailedCount  int64   `json:"failed_count"`
	PendingCount int64   `json:"pending_count"`
	SuccessRate  float64 `json:"success_rate"`
}

// emailTemplateRepository 邮件模板仓储实现
type emailTemplateRepository struct {
	db *gorm.DB
}

// NewEmailTemplateRepository 创建邮件模板仓储
func NewEmailTemplateRepository(db *gorm.DB) EmailTemplateRepository {
	return &emailTemplateRepository{db: db}
}

// Create 创建邮件模板
func (r *emailTemplateRepository) Create(ctx context.Context, template *model.EmailTemplate) error {
	return r.db.WithContext(ctx).Create(template).Error
}

// GetByID 根据ID获取邮件模板
func (r *emailTemplateRepository) GetByID(ctx context.Context, id uint) (*model.EmailTemplate, error) {
	var template model.EmailTemplate
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

// GetByCode 根据模板代码获取邮件模板
func (r *emailTemplateRepository) GetByCode(ctx context.Context, templateCode string) (*model.EmailTemplate, error) {
	var template model.EmailTemplate
	err := r.db.WithContext(ctx).Where("template_code = ? AND status = ?", templateCode, 1).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

// Update 更新邮件模板
func (r *emailTemplateRepository) Update(ctx context.Context, template *model.EmailTemplate) error {
	return r.db.WithContext(ctx).Save(template).Error
}

// Delete 删除邮件模板
func (r *emailTemplateRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.EmailTemplate{}, id).Error
}

// List 分页查询邮件模板
func (r *emailTemplateRepository) List(ctx context.Context, condition *EmailTemplateQueryCondition) ([]*model.EmailTemplate, int64, error) {
	var templates []*model.EmailTemplate
	var total int64
	
	query := r.db.WithContext(ctx).Model(&model.EmailTemplate{})
	
	// 构建查询条件
	if condition.TemplateCode != "" {
		query = query.Where("template_code LIKE ?", "%"+condition.TemplateCode+"%")
	}
	if condition.TemplateName != "" {
		query = query.Where("template_name LIKE ?", "%"+condition.TemplateName+"%")
	}
	if condition.Category != "" {
		query = query.Where("category = ?", condition.Category)
	}
	if condition.Status >= 0 {
		query = query.Where("status = ?", condition.Status)
	}
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := (condition.Page - 1) * condition.PageSize
	orderBy := "created_at DESC"
	if condition.OrderBy != "" {
		orderBy = condition.OrderBy
	}
	
	err := query.Order(orderBy).Offset(offset).Limit(condition.PageSize).Find(&templates).Error
	return templates, total, err
}

// GetByCategory 根据分类获取邮件模板
func (r *emailTemplateRepository) GetByCategory(ctx context.Context, category string) ([]*model.EmailTemplate, error) {
	var templates []*model.EmailTemplate
	err := r.db.WithContext(ctx).Where("category = ? AND status = ?", category, 1).
		Order("sort_order ASC, created_at DESC").Find(&templates).Error
	return templates, err
}

// GetActiveTemplates 获取启用的邮件模板
func (r *emailTemplateRepository) GetActiveTemplates(ctx context.Context) ([]*model.EmailTemplate, error) {
	var templates []*model.EmailTemplate
	err := r.db.WithContext(ctx).Where("status = ?", 1).
		Order("category ASC, sort_order ASC").Find(&templates).Error
	return templates, err
}

// UpdateStatus 更新邮件模板状态
func (r *emailTemplateRepository) UpdateStatus(ctx context.Context, id uint, status int) error {
	return r.db.WithContext(ctx).Model(&model.EmailTemplate{}).
		Where("id = ?", id).Update("status", status).Error
}

// CheckCodeExists 检查模板代码是否存在
func (r *emailTemplateRepository) CheckCodeExists(ctx context.Context, templateCode string, excludeID uint) bool {
	var count int64
	query := r.db.WithContext(ctx).Model(&model.EmailTemplate{}).Where("template_code = ?", templateCode)
	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}
	query.Count(&count)
	return count > 0
}

// emailLogRepository 邮件日志仓储实现
type emailLogRepository struct {
	db *gorm.DB
}

// NewEmailLogRepository 创建邮件日志仓储
func NewEmailLogRepository(db *gorm.DB) EmailLogRepository {
	return &emailLogRepository{db: db}
}

// Create 创建邮件日志
func (r *emailLogRepository) Create(ctx context.Context, log *model.EmailLog) error {
	return r.db.WithContext(ctx).Create(log).Error
}

// GetByID 根据ID获取邮件日志
func (r *emailLogRepository) GetByID(ctx context.Context, id uint) (*model.EmailLog, error) {
	var log model.EmailLog
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&log).Error
	if err != nil {
		return nil, err
	}
	return &log, nil
}

// Update 更新邮件日志
func (r *emailLogRepository) Update(ctx context.Context, log *model.EmailLog) error {
	return r.db.WithContext(ctx).Save(log).Error
}

// List 分页查询邮件日志
func (r *emailLogRepository) List(ctx context.Context, condition *EmailLogQueryCondition) ([]*model.EmailLog, int64, error) {
	var logs []*model.EmailLog
	var total int64
	
	query := r.db.WithContext(ctx).Model(&model.EmailLog{})
	
	// 构建查询条件
	if condition.TemplateCode != "" {
		query = query.Where("template_code = ?", condition.TemplateCode)
	}
	if condition.ToEmail != "" {
		query = query.Where("to_email LIKE ?", "%"+condition.ToEmail+"%")
	}
	if condition.UserID > 0 {
		query = query.Where("user_id = ?", condition.UserID)
	}
	if condition.UserType != "" {
		query = query.Where("user_type = ?", condition.UserType)
	}
	if condition.Status >= 0 {
		query = query.Where("status = ?", condition.Status)
	}
	if condition.Provider != "" {
		query = query.Where("provider = ?", condition.Provider)
	}
	if !condition.StartDate.IsZero() {
		query = query.Where("created_at >= ?", condition.StartDate)
	}
	if !condition.EndDate.IsZero() {
		query = query.Where("created_at <= ?", condition.EndDate)
	}
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := (condition.Page - 1) * condition.PageSize
	orderBy := "created_at DESC"
	if condition.OrderBy != "" {
		orderBy = condition.OrderBy
	}
	
	err := query.Order(orderBy).Offset(offset).Limit(condition.PageSize).Find(&logs).Error
	return logs, total, err
}

// Delete 删除邮件日志
func (r *emailLogRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.EmailLog{}, id).Error
}

// GetByEmail 根据邮箱获取邮件日志
func (r *emailLogRepository) GetByEmail(ctx context.Context, email string, limit int) ([]*model.EmailLog, error) {
	var logs []*model.EmailLog
	err := r.db.WithContext(ctx).Where("to_email = ?", email).
		Order("created_at DESC").Limit(limit).Find(&logs).Error
	return logs, err
}

// GetByUserID 根据用户ID获取邮件日志
func (r *emailLogRepository) GetByUserID(ctx context.Context, userID uint, limit int) ([]*model.EmailLog, error) {
	var logs []*model.EmailLog
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).
		Order("created_at DESC").Limit(limit).Find(&logs).Error
	return logs, err
}

// GetByTemplateCode 根据模板代码获取邮件日志
func (r *emailLogRepository) GetByTemplateCode(ctx context.Context, templateCode string, limit int) ([]*model.EmailLog, error) {
	var logs []*model.EmailLog
	err := r.db.WithContext(ctx).Where("template_code = ?", templateCode).
		Order("created_at DESC").Limit(limit).Find(&logs).Error
	return logs, err
}

// GetEmailStats 获取邮件统计
func (r *emailLogRepository) GetEmailStats(ctx context.Context, startDate, endDate time.Time) (*EmailStats, error) {
	var stats EmailStats
	
	query := r.db.WithContext(ctx).Model(&model.EmailLog{})
	
	if !startDate.IsZero() {
		query = query.Where("created_at >= ?", startDate)
	}
	if !endDate.IsZero() {
		query = query.Where("created_at <= ?", endDate)
	}
	
	// 获取总数
	if err := query.Count(&stats.TotalCount).Error; err != nil {
		return nil, err
	}
	
	// 获取成功数
	if err := query.Where("status = ?", model.EmailStatusSuccess).Count(&stats.SuccessCount).Error; err != nil {
		return nil, err
	}
	
	// 获取失败数
	if err := query.Where("status = ?", model.EmailStatusFailed).Count(&stats.FailedCount).Error; err != nil {
		return nil, err
	}
	
	// 获取待发送数
	if err := query.Where("status = ?", model.EmailStatusPending).Count(&stats.PendingCount).Error; err != nil {
		return nil, err
	}
	
	// 计算成功率
	if stats.TotalCount > 0 {
		stats.SuccessRate = float64(stats.SuccessCount) / float64(stats.TotalCount) * 100
	}
	
	return &stats, nil
}

// GetFailedEmails 获取失败的邮件
func (r *emailLogRepository) GetFailedEmails(ctx context.Context, limit int) ([]*model.EmailLog, error) {
	var logs []*model.EmailLog
	err := r.db.WithContext(ctx).Where("status = ?", model.EmailStatusFailed).
		Order("created_at DESC").Limit(limit).Find(&logs).Error
	return logs, err
}

// CleanOldLogs 清理旧邮件日志
func (r *emailLogRepository) CleanOldLogs(ctx context.Context, beforeDate time.Time) (int64, error) {
	result := r.db.WithContext(ctx).Where("created_at < ?", beforeDate).Delete(&model.EmailLog{})
	return result.RowsAffected, result.Error
}
