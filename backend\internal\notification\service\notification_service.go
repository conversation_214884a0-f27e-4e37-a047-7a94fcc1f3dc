package service

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"net/http"
	"payment-gateway/internal/notification/model"
	"payment-gateway/internal/notification/repository"
	"payment-gateway/internal/shared/errors"
	"payment-gateway/internal/shared/interfaces"
	"payment-gateway/internal/shared/utils"
	"payment-gateway/internal/shared/utils/logger"
	"payment-gateway/internal/shared/utils/validator"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// NotificationService 通知服务实现
type notificationService struct {
	db                    *gorm.DB
	notificationQueueRepo repository.NotificationQueueRepository
	notificationLogRepo   repository.NotificationLogRepository
	emailTemplateRepo     repository.EmailTemplateRepository
	emailLogRepo          repository.EmailLogRepository
	smsTemplateRepo       repository.SMSTemplateRepository
	smsLogRepo            repository.SMSLogRepository
	messageQueueRepo      repository.MessageQueueRepository
	httpClient            *http.Client
}

// NewNotificationService 创建通知服务
func NewNotificationService(
	db *gorm.DB,
	notificationQueueRepo repository.NotificationQueueRepository,
	notificationLogRepo repository.NotificationLogRepository,
	emailTemplateRepo repository.EmailTemplateRepository,
	emailLogRepo repository.EmailLogRepository,
	smsTemplateRepo repository.SMSTemplateRepository,
	smsLogRepo repository.SMSLogRepository,
	messageQueueRepo repository.MessageQueueRepository,
) interfaces.NotificationService {
	return &notificationService{
		db:                    db,
		notificationQueueRepo: notificationQueueRepo,
		notificationLogRepo:   notificationLogRepo,
		emailTemplateRepo:     emailTemplateRepo,
		emailLogRepo:          emailLogRepo,
		smsTemplateRepo:       smsTemplateRepo,
		smsLogRepo:            smsLogRepo,
		messageQueueRepo:      messageQueueRepo,
		httpClient:            &http.Client{Timeout: 30 * time.Second},
	}
}

// SendPaymentNotification 发送支付通知
func (s *notificationService) SendPaymentNotification(ctx context.Context, orderNo string, notifyType int) error {
	// TODO: 根据订单号获取订单信息和商户信息
	// 这里需要调用Gateway域的服务获取订单详情

	// 构建通知数据
	notifyData := map[string]interface{}{
		"order_no":    orderNo,
		"notify_type": notifyType,
		"timestamp":   time.Now().Unix(),
	}

	notifyDataJSON, _ := json.Marshal(notifyData)

	// 生成通知ID
	notifyID := s.generateNotifyID(orderNo, notifyType)

	// 创建通知队列记录
	queue := &model.NotificationQueue{
		NotifyID:    notifyID,
		MerchantID:  1,          // TODO: 从订单信息中获取
		AppID:       "test_app", // TODO: 从订单信息中获取
		OrderNo:     orderNo,
		NotifyType:  notifyType,
		NotifyURL:   "https://example.com/notify", // TODO: 从商户配置中获取
		NotifyData:  string(notifyDataJSON),
		SignType:    model.SignTypeMD5,
		Status:      model.NotifyStatusPending,
		RetryCount:  0,
		MaxRetry:    5,
		NextRetryAt: time.Now(),
	}

	// 生成签名
	queue.Sign = s.generateSign(queue.NotifyData, "test_key") // TODO: 使用真实的商户密钥

	if err := s.notificationQueueRepo.Create(ctx, queue); err != nil {
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "创建通知队列失败")
	}

	// 立即尝试发送通知
	return s.sendNotification(ctx, queue)
}

// SendRefundNotification 发送退款通知
func (s *notificationService) SendRefundNotification(ctx context.Context, refundNo string) error {
	return s.SendPaymentNotification(ctx, refundNo, model.NotifyTypeRefundSuccess)
}

// RetryNotification 重试通知
func (s *notificationService) RetryNotification(ctx context.Context, notifyID string) error {
	// 获取通知队列记录
	queue, err := s.notificationQueueRepo.GetByNotifyID(ctx, notifyID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return interfaces.NewBusinessError(interfaces.ErrCodeNotFound, "通知记录不存在")
		}
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取通知记录失败")
	}

	// 检查是否可以重试
	if queue.RetryCount >= queue.MaxRetry {
		return interfaces.NewBusinessError(interfaces.ErrCodeValidation, "已达到最大重试次数")
	}

	if queue.Status == model.NotifyStatusSuccess {
		return interfaces.NewBusinessError(interfaces.ErrCodeValidation, "通知已成功，无需重试")
	}

	// 发送通知
	return s.sendNotification(ctx, queue)
}

// SendEmail 发送邮件
func (s *notificationService) SendEmail(ctx context.Context, req *interfaces.SendEmailRequest) error {
	// 验证请求参数
	if err := s.validateSendEmailRequest(req); err != nil {
		return err
	}

	// 创建邮件日志
	emailLog := &model.EmailLog{
		ToEmail:  req.ToEmail,
		ToName:   req.ToName,
		Subject:  req.Subject,
		Content:  req.Content,
		Status:   model.EmailStatusPending,
		UserID:   req.UserID,
		UserType: strconv.Itoa(req.UserType),
		IP:       req.IP,
	}

	if err := s.emailLogRepo.Create(ctx, emailLog); err != nil {
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "创建邮件日志失败")
	}

	// TODO: 实际发送邮件的逻辑
	// 这里应该调用邮件服务提供商的API

	// 模拟发送成功
	emailLog.Status = model.EmailStatusSuccess
	emailLog.SentAt = time.Now()
	emailLog.Provider = "smtp"
	emailLog.MessageID = s.generateMessageID()

	if err := s.emailLogRepo.Update(ctx, emailLog); err != nil {
		logger.Error(ctx, "更新邮件日志失败", zap.Error(err))
	}

	return nil
}

// SendEmailByTemplate 根据模板发送邮件
func (s *notificationService) SendEmailByTemplate(ctx context.Context, templateCode string, toEmail string, variables map[string]interface{}) error {
	// 获取邮件模板
	template, err := s.emailTemplateRepo.GetByCode(ctx, templateCode)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return interfaces.NewBusinessError(interfaces.ErrCodeNotFound, "邮件模板不存在")
		}
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取邮件模板失败")
	}

	// 替换模板变量
	subject := s.replaceTemplateVariables(template.Subject, variables)
	content := s.replaceTemplateVariables(template.Content, variables)

	// 构建发送请求
	req := &interfaces.SendEmailRequest{
		ToEmail:      toEmail,
		Subject:      subject,
		Content:      content,
		TemplateCode: templateCode,
		Data:         variables,
	}

	return s.SendEmail(ctx, req)
}

// SendSMS 发送短信
func (s *notificationService) SendSMS(ctx context.Context, req *interfaces.SendSMSRequest) error {
	// 验证请求参数
	if err := s.validateSendSMSRequest(req); err != nil {
		return err
	}

	// 创建短信日志
	smsLog := &model.SMSLog{
		Phone:   req.Phone,
		Content: req.Content,
		Status:  model.EmailStatusPending, // 使用相同的状态常量
	}

	if err := s.smsLogRepo.Create(ctx, smsLog); err != nil {
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "创建短信日志失败")
	}

	// TODO: 实际发送短信的逻辑
	// 这里应该调用短信服务提供商的API

	// 模拟发送成功
	smsLog.Status = model.EmailStatusSuccess
	smsLog.SentAt = time.Now()
	smsLog.Provider = "aliyun"
	smsLog.MessageID = s.generateMessageID()

	if err := s.smsLogRepo.Update(ctx, smsLog); err != nil {
		logger.Error(ctx, "更新短信日志失败", zap.Error(err))
	}

	return nil
}

// SendSMSByTemplate 根据模板发送短信
func (s *notificationService) SendSMSByTemplate(ctx context.Context, templateCode string, phone string, variables map[string]interface{}) error {
	// 获取短信模板
	template, err := s.smsTemplateRepo.GetByCode(ctx, templateCode)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return interfaces.NewBusinessError(interfaces.ErrCodeNotFound, "短信模板不存在")
		}
		return interfaces.NewBusinessError(interfaces.ErrCodeDatabase, "获取短信模板失败")
	}

	// 替换模板变量
	content := s.replaceTemplateVariables(template.Content, variables)

	// 构建发送请求
	req := &interfaces.SendSMSRequest{
		Phone:   phone,
		Content: content,
		Data:    variables,
	}

	return s.SendSMS(ctx, req)
}

// sendNotification 发送通知
func (s *notificationService) sendNotification(ctx context.Context, queue *model.NotificationQueue) error {
	startTime := time.Now()

	// 创建通知日志
	log := &model.NotificationLog{
		NotifyID:    queue.NotifyID,
		MerchantID:  queue.MerchantID,
		AppID:       queue.AppID,
		OrderNo:     queue.OrderNo,
		NotifyType:  queue.NotifyType,
		NotifyURL:   queue.NotifyURL,
		RequestData: queue.NotifyData,
		RetryCount:  queue.RetryCount,
		IP:          "127.0.0.1", // TODO: 获取实际IP
	}

	// TODO: 实际发送HTTP通知的逻辑
	// 这里应该向商户的回调地址发送POST请求

	// 模拟发送成功
	duration := int(time.Since(startTime).Milliseconds())
	log.Duration = duration
	log.Status = 1 // 成功
	log.ResponseCode = 200
	log.ResponseData = "success"

	// 保存通知日志
	if err := s.notificationLogRepo.Create(ctx, log); err != nil {
		logger.Error(ctx, "创建通知日志失败", zap.Error(err))
	}

	// 更新通知队列状态
	if err := s.notificationQueueRepo.UpdateSuccessInfo(ctx, queue.NotifyID, 200, "success"); err != nil {
		logger.Error(ctx, "更新通知队列状态失败", zap.Error(err))
	}

	return nil
}

// generateNotifyID 生成通知ID
func (s *notificationService) generateNotifyID(orderNo string, notifyType int) string {
	data := fmt.Sprintf("%s_%d_%d", orderNo, notifyType, time.Now().UnixNano())
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("%x", hash)
}

// generateSign 生成签名
func (s *notificationService) generateSign(data, key string) string {
	signStr := data + key
	hash := md5.Sum([]byte(signStr))
	return fmt.Sprintf("%x", hash)
}

// generateMessageID 生成消息ID
func (s *notificationService) generateMessageID() string {
	hash := md5.Sum([]byte(fmt.Sprintf("%d", time.Now().UnixNano())))
	return fmt.Sprintf("%x", hash)
}

// replaceTemplateVariables 替换模板变量
func (s *notificationService) replaceTemplateVariables(template string, variables map[string]interface{}) string {
	result := template
	for key, value := range variables {
		placeholder := fmt.Sprintf("{{%s}}", key)
		result = strings.ReplaceAll(result, placeholder, fmt.Sprintf("%v", value))
	}
	return result
}

// validateSendEmailRequest 验证发送邮件请求
func (s *notificationService) validateSendEmailRequest(req *interfaces.SendEmailRequest) error {
	v := validator.New()

	v.Required("to_email", req.ToEmail).Email("to_email", req.ToEmail).
		Required("subject", req.Subject).MaxLength("subject", req.Subject, 200).
		Required("content", req.Content)

	if v.HasErrors() {
		return v.ToBusinessError()
	}

	return nil
}

// validateSendSMSRequest 验证发送短信请求
func (s *notificationService) validateSendSMSRequest(req *interfaces.SendSMSRequest) error {
	v := validator.New()

	v.Required("phone", req.Phone).Phone("phone", req.Phone).
		Required("content", req.Content).MaxLength("content", req.Content, 500)

	if v.HasErrors() {
		return v.ToBusinessError()
	}

	return nil
}

// CreateEmailTemplate 创建邮件模板
func (s *notificationService) CreateEmailTemplate(ctx context.Context, req *interfaces.CreateEmailTemplateRequest) (*interfaces.EmailTemplateResponse, error) {
	// 验证请求参数
	v := validator.New()
	v.Required("name", req.Name).
		Required("subject", req.Subject).
		Required("content", req.Content).
		MaxLength("name", req.Name, 100).
		MaxLength("subject", req.Subject, 200).
		MaxLength("category", req.Category, 50).
		MaxLength("description", req.Description, 500)

	if v.HasErrors() {
		return nil, v.ToBusinessError()
	}

	// 创建邮件模板
	template := &model.EmailTemplate{
		TemplateCode: utils.GenerateID("TPL"),
		TemplateName: req.Name,
		Subject:      req.Subject,
		Content:      req.Content,
		Category:     req.Category,
		Description:  req.Description,
		Status:       1, // 默认启用
	}

	if err := s.emailTemplateRepo.Create(ctx, template); err != nil {
		logger.Error(ctx, "创建邮件模板失败", zap.Error(err))
		return nil, errors.NewDatabaseError("创建邮件模板失败")
	}

	// 返回响应
	return &interfaces.EmailTemplateResponse{
		ID:          template.ID,
		Name:        template.TemplateName,
		Subject:     template.Subject,
		Content:     template.Content,
		Category:    template.Category,
		Description: template.Description,
		Status:      template.Status,
		CreatedAt:   template.CreatedAt,
	}, nil
}

// CreateSMSTemplate 创建短信模板
func (s *notificationService) CreateSMSTemplate(ctx context.Context, req *interfaces.CreateSMSTemplateRequest) (*interfaces.SMSTemplateResponse, error) {
	// 验证请求参数
	v := validator.New()
	v.Required("name", req.Name)
	v.Required("content", req.Content)
	v.MaxLength("name", req.Name, 100)
	v.MaxLength("content", req.Content, 500)
	v.MaxLength("category", req.Category, 50)
	v.MaxLength("description", req.Description, 500)

	if v.HasErrors() {
		return nil, v.ToBusinessError()
	}

	// 创建短信模板
	template := &model.SMSTemplate{
		TemplateCode: utils.GenerateID("SMS"),
		TemplateName: req.Name,
		Content:      req.Content,
		Category:     req.Category,
		Description:  req.Description,
		Status:       1, // 默认启用
	}

	if err := s.smsTemplateRepo.Create(ctx, template); err != nil {
		logger.Error(ctx, "创建短信模板失败", zap.Error(err))
		return nil, errors.NewDatabaseError("创建短信模板失败")
	}

	// 返回响应
	return &interfaces.SMSTemplateResponse{
		ID:          template.ID,
		Name:        template.TemplateName,
		Content:     template.Content,
		Category:    template.Category,
		Description: template.Description,
		Status:      template.Status,
		CreatedAt:   template.CreatedAt,
	}, nil
}
