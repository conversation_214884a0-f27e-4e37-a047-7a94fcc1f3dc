<template>
  <div class="payment-orders">
    <h2>支付订单</h2>
    
    <!-- 搜索表单 -->
    <a-card class="search-card">
      <a-form :model="searchForm" layout="inline">
        <a-form-item label="订单号">
          <a-input v-model="searchForm.orderNo" placeholder="请输入订单号" />
        </a-form-item>
        <a-form-item label="支付方式">
          <a-select v-model="searchForm.payMethod" placeholder="请选择支付方式" style="width: 120px">
            <a-option value="">全部</a-option>
            <a-option value="wechat">微信支付</a-option>
            <a-option value="alipay">支付宝</a-option>
            <a-option value="unionpay">银联支付</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态">
          <a-select v-model="searchForm.status" placeholder="请选择状态" style="width: 100px">
            <a-option value="">全部</a-option>
            <a-option value="pending">待支付</a-option>
            <a-option value="success">已支付</a-option>
            <a-option value="failed">支付失败</a-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">搜索</a-button>
          <a-button @click="handleReset" style="margin-left: 8px">重置</a-button>
        </a-form-item>
      </a-form>
    </a-card>
    
    <!-- 订单表格 -->
    <a-card class="table-card">
      <a-table 
        :columns="columns" 
        :data="orders" 
        :pagination="pagination"
        :loading="loading"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template #amount="{ record }">
          ¥{{ record.amount }}
        </template>
        <template #actions="{ record }">
          <a-button type="text" size="small" @click="viewDetail(record)">
            查看详情
          </a-button>
          <a-button 
            v-if="record.status === 'pending'" 
            type="text" 
            size="small" 
            @click="cancelOrder(record)"
          >
            取消订单
          </a-button>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'

// 搜索表单
const searchForm = reactive({
  orderNo: '',
  payMethod: '',
  status: ''
})

// 表格列定义
const columns = [
  {
    title: '订单号',
    dataIndex: 'orderNo',
    width: 200
  },
  {
    title: '金额',
    dataIndex: 'amount',
    slotName: 'amount',
    width: 120
  },
  {
    title: '支付方式',
    dataIndex: 'payMethod',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180
  },
  {
    title: '支付时间',
    dataIndex: 'payTime',
    width: 180
  },
  {
    title: '操作',
    slotName: 'actions',
    width: 150
  }
]

// 订单数据
const orders = ref([
  {
    id: 1,
    orderNo: 'PAY202401010001',
    amount: 299.00,
    payMethod: '微信支付',
    status: 'success',
    createTime: '2024-01-01 10:30:00',
    payTime: '2024-01-01 10:31:25'
  },
  {
    id: 2,
    orderNo: 'PAY202401010002',
    amount: 158.50,
    payMethod: '支付宝',
    status: 'success',
    createTime: '2024-01-01 10:25:00',
    payTime: '2024-01-01 10:26:10'
  },
  {
    id: 3,
    orderNo: 'PAY202401010003',
    amount: 88.00,
    payMethod: '微信支付',
    status: 'pending',
    createTime: '2024-01-01 10:20:00',
    payTime: ''
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 100,
  showTotal: true,
  showPageSize: true
})

// 加载状态
const loading = ref(false)

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    success: 'green',
    pending: 'orange',
    failed: 'red'
  }
  return colorMap[status] || 'gray'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    success: '已支付',
    pending: '待支付',
    failed: '支付失败'
  }
  return textMap[status] || '未知'
}

// 搜索
const handleSearch = () => {
  console.log('搜索订单:', searchForm)
  // TODO: 调用API搜索订单
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    orderNo: '',
    payMethod: '',
    status: ''
  })
  handleSearch()
}

// 分页变化
const handlePageChange = (page: number) => {
  pagination.current = page
  // TODO: 重新加载数据
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  // TODO: 重新加载数据
}

// 查看详情
const viewDetail = (record: any) => {
  console.log('查看订单详情:', record)
  // TODO: 打开订单详情弹窗或跳转详情页
}

// 取消订单
const cancelOrder = (record: any) => {
  console.log('取消订单:', record)
  // TODO: 调用API取消订单
}

// 页面加载时获取数据
onMounted(() => {
  // TODO: 从API获取订单数据
  console.log('加载支付订单数据')
})
</script>

<style scoped>
.payment-orders {
  padding: 0;
}

.search-card {
  margin-bottom: 16px;
}

.table-card {
  margin-bottom: 16px;
}
</style>
