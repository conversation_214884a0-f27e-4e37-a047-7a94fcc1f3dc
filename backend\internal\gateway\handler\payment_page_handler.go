package handler

import (
	"fmt"
	"html/template"
	"net/http"
	"path/filepath"
	"time"

	"github.com/gin-gonic/gin"
	"payment-gateway/internal/gateway/service"
	"payment-gateway/internal/shared/response"
)

// PaymentPageHandler 支付页面处理器
type PaymentPageHandler struct {
	aggregateCodeService service.AggregateCodeService
}

// NewPaymentPageHandler 创建支付页面处理器实例
func NewPaymentPageHandler(aggregateCodeService service.AggregateCodeService) *PaymentPageHandler {
	return &PaymentPageHandler{
		aggregateCodeService: aggregateCodeService,
	}
}

// ShowPaymentPage 显示支付页面
// @Summary 显示支付页面
// @Description 根据聚合码ID显示支付页面
// @Tags 支付页面
// @Accept html
// @Produce html
// @Param code_id path string true "聚合码ID"
// @Success 200 {string} string "支付页面HTML"
// @Failure 404 {object} response.Response "聚合码不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /pay/{code_id} [get]
func (h *PaymentPageHandler) ShowPaymentPage(c *gin.Context) {
	codeID := c.Param("code_id")
	if codeID == "" {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"Title":   "参数错误",
			"Message": "聚合码ID不能为空",
		})
		return
	}
	
	// 获取User-Agent用于智能路由
	userAgent := c.GetHeader("User-Agent")
	
	// 更新扫码次数
	h.aggregateCodeService.UpdateScanCount(c.Request.Context(), codeID)
	
	// 获取支付页面数据
	pageData, err := h.aggregateCodeService.GetPaymentPageData(c.Request.Context(), codeID, userAgent)
	if err != nil {
		// 根据错误类型显示不同页面
		c.HTML(http.StatusNotFound, "error.html", gin.H{
			"Title":   "支付码无效",
			"Message": err.Error(),
		})
		return
	}
	
	// 格式化时间显示
	templateData := gin.H{
		"CodeID":             pageData.CodeID,
		"Subject":            pageData.Subject,
		"Body":               pageData.Body,
		"Amount":             pageData.Amount,
		"AmountYuan":         pageData.AmountYuan,
		"Currency":           pageData.Currency,
		"AvailableChannels":  pageData.AvailableChannels,
		"RecommendedChannel": pageData.RecommendedChannel,
		"BrandConfig":        pageData.BrandConfig,
		"ExpireTime":         pageData.ExpireTime.Format("2006-01-02 15:04:05"),
		"CreatedAt":          time.Now().Format("2006-01-02 15:04:05"),
		"IsExpired":          pageData.IsExpired,
	}
	
	// 设置默认品牌配置
	if pageData.BrandConfig == nil {
		templateData["BrandConfig"] = gin.H{
			"Title":           "聚合支付",
			"ThemeColor":      "#1890ff",
			"BackgroundColor": "#f5f5f5",
		}
	}
	
	// 渲染支付页面
	c.HTML(http.StatusOK, "payment_page.html", templateData)
}

// ShowQRCode 显示二维码页面
// @Summary 显示二维码页面
// @Description 生成并显示支付二维码
// @Tags 支付页面
// @Accept html
// @Produce html
// @Param url query string true "支付URL"
// @Success 200 {string} string "二维码页面HTML"
// @Router /qrcode [get]
func (h *PaymentPageHandler) ShowQRCode(c *gin.Context) {
	payURL := c.Query("url")
	if payURL == "" {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"Title":   "参数错误",
			"Message": "支付URL不能为空",
		})
		return
	}
	
	// 这里应该集成二维码生成库，例如：
	// qrCode, err := qrcode.GenerateQRCode(payURL, qrcode.Medium, 256)
	// 目前使用占位符
	
	templateData := gin.H{
		"PayURL":    payURL,
		"QRCodeURL": fmt.Sprintf("https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=%s", payURL),
		"Title":     "扫码支付",
	}
	
	c.HTML(http.StatusOK, "qrcode.html", templateData)
}

// RegisterPageRoutes 注册页面路由
func (h *PaymentPageHandler) RegisterPageRoutes(r *gin.Engine) {
	// 加载HTML模板
	r.LoadHTMLGlob("templates/*")
	
	// 设置静态文件路径
	r.Static("/static", "./static")
	
	// 支付页面路由
	r.GET("/pay/:code_id", h.ShowPaymentPage)
	r.GET("/qrcode", h.ShowQRCode)
}

// 模板函数
func templateFuncs() template.FuncMap {
	return template.FuncMap{
		"default": func(defaultValue, value interface{}) interface{} {
			if value == nil || value == "" {
				return defaultValue
			}
			return value
		},
		"formatTime": func(t time.Time) string {
			return t.Format("2006-01-02 15:04:05")
		},
		"formatAmount": func(amount int64) string {
			return fmt.Sprintf("%.2f", float64(amount)/100)
		},
	}
}

// InitTemplates 初始化模板引擎
func InitTemplates(r *gin.Engine) {
	// 设置模板函数
	r.SetFuncMap(templateFuncs())
	
	// 加载模板文件
	templatePath := filepath.Join("templates", "*.html")
	r.LoadHTMLGlob(templatePath)
}
