package handler

import (
	"net/http"
	"payment-gateway/internal/shared/interfaces"
	"payment-gateway/internal/shared/middleware"
	"payment-gateway/internal/shared/utils/response"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// SettlementHandler 结算处理器
type SettlementHandler struct {
	settlementService interfaces.SettlementService
}

// NewSettlementHandler 创建结算处理器
func NewSettlementHandler(settlementService interfaces.SettlementService) *SettlementHandler {
	return &SettlementHandler{
		settlementService: settlementService,
	}
}

// RegisterRoutes 注册路由
func (h *SettlementHandler) RegisterRoutes(r *gin.RouterGroup) {
	// 商户路由组
	merchant := r.Group("/merchant")
	merchant.Use(middleware.Auth())
	{
		// 结算相关
		merchant.POST("/settlement", h.CreateSettlement)
		merchant.GET("/settlement/:settlement_no", h.GetSettlement)
		merchant.GET("/settlements", h.ListSettlements)
		merchant.GET("/settlement/stats", h.GetSettlementStats)
		
		// 提现相关
		merchant.POST("/withdraw", h.CreateWithdraw)
		merchant.GET("/withdraw/:withdraw_no", h.GetWithdraw)
		merchant.GET("/withdraws", h.ListWithdraws)
		merchant.GET("/withdraw/stats", h.GetWithdrawStats)
		
		// 对账相关
		merchant.GET("/reconciliation/:reconciliation_no", h.GetReconciliation)
		merchant.GET("/reconciliations", h.ListReconciliations)
		merchant.GET("/reconciliation/stats", h.GetReconciliationStats)
	}
	
	// 管理员路由组
	admin := r.Group("/admin")
	admin.Use(middleware.AdminAuth())
	{
		// 结算管理
		admin.GET("/settlements", h.AdminListSettlements)
		admin.PUT("/settlement/:settlement_no/process", h.ProcessSettlement)
		admin.GET("/settlement/stats", h.AdminGetSettlementStats)
		
		// 提现管理
		admin.GET("/withdraws", h.AdminListWithdraws)
		admin.PUT("/withdraw/:withdraw_no/process", h.ProcessWithdraw)
		admin.GET("/withdraw/stats", h.AdminGetWithdrawStats)
		
		// 对账管理
		admin.POST("/reconciliation", h.CreateReconciliation)
		admin.GET("/reconciliations", h.AdminListReconciliations)
		admin.PUT("/reconciliation/:reconciliation_no/process", h.ProcessReconciliation)
		admin.GET("/reconciliation/stats", h.AdminGetReconciliationStats)
	}
}

// CreateSettlement 创建结算订单
func (h *SettlementHandler) CreateSettlement(c *gin.Context) {
	var req interfaces.CreateSettlementRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}
	
	// 从上下文获取商户ID
	merchantID, exists := c.Get("merchant_id")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "未授权", "无法获取商户信息")
		return
	}
	req.MerchantID = merchantID.(uint)
	
	result, err := h.settlementService.CreateSettlement(c.Request.Context(), &req)
	if err != nil {
		if bizErr, ok := err.(*interfaces.BusinessError); ok {
			response.Error(c, http.StatusBadRequest, bizErr.Message, bizErr.Details)
			return
		}
		response.Error(c, http.StatusInternalServerError, "创建结算订单失败", err.Error())
		return
	}
	
	response.Success(c, result)
}

// GetSettlement 获取结算订单详情
func (h *SettlementHandler) GetSettlement(c *gin.Context) {
	settlementNo := c.Param("settlement_no")
	if settlementNo == "" {
		response.Error(c, http.StatusBadRequest, "参数错误", "结算单号不能为空")
		return
	}
	
	// TODO: 实现获取结算订单详情
	response.Success(c, gin.H{"settlement_no": settlementNo})
}

// ListSettlements 获取结算订单列表
func (h *SettlementHandler) ListSettlements(c *gin.Context) {
	// 从上下文获取商户ID
	merchantID, exists := c.Get("merchant_id")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "未授权", "无法获取商户信息")
		return
	}
	
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	status, _ := strconv.Atoi(c.DefaultQuery("status", "-1"))
	
	// TODO: 实现获取结算订单列表
	response.Success(c, gin.H{
		"merchant_id": merchantID,
		"page":        page,
		"page_size":   pageSize,
		"status":      status,
	})
}

// GetSettlementStats 获取结算统计
func (h *SettlementHandler) GetSettlementStats(c *gin.Context) {
	// 从上下文获取商户ID
	merchantID, exists := c.Get("merchant_id")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "未授权", "无法获取商户信息")
		return
	}
	
	// 解析时间参数
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")
	
	var startDate, endDate time.Time
	var err error
	
	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "开始日期格式错误")
			return
		}
	}
	
	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "结束日期格式错误")
			return
		}
	}
	
	result, err := h.settlementService.GetSettlementStats(c.Request.Context(), merchantID.(uint), startDate, endDate)
	if err != nil {
		if bizErr, ok := err.(*interfaces.BusinessError); ok {
			response.Error(c, http.StatusBadRequest, bizErr.Message, bizErr.Details)
			return
		}
		response.Error(c, http.StatusInternalServerError, "获取结算统计失败", err.Error())
		return
	}
	
	response.Success(c, result)
}

// CreateWithdraw 创建提现申请
func (h *SettlementHandler) CreateWithdraw(c *gin.Context) {
	var req interfaces.CreateWithdrawRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}
	
	// 从上下文获取商户ID
	merchantID, exists := c.Get("merchant_id")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "未授权", "无法获取商户信息")
		return
	}
	req.MerchantID = merchantID.(uint)
	
	result, err := h.settlementService.CreateWithdraw(c.Request.Context(), &req)
	if err != nil {
		if bizErr, ok := err.(*interfaces.BusinessError); ok {
			response.Error(c, http.StatusBadRequest, bizErr.Message, bizErr.Details)
			return
		}
		response.Error(c, http.StatusInternalServerError, "创建提现申请失败", err.Error())
		return
	}
	
	response.Success(c, result)
}

// GetWithdraw 获取提现记录详情
func (h *SettlementHandler) GetWithdraw(c *gin.Context) {
	withdrawNo := c.Param("withdraw_no")
	if withdrawNo == "" {
		response.Error(c, http.StatusBadRequest, "参数错误", "提现单号不能为空")
		return
	}
	
	// TODO: 实现获取提现记录详情
	response.Success(c, gin.H{"withdraw_no": withdrawNo})
}

// ListWithdraws 获取提现记录列表
func (h *SettlementHandler) ListWithdraws(c *gin.Context) {
	// 从上下文获取商户ID
	merchantID, exists := c.Get("merchant_id")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "未授权", "无法获取商户信息")
		return
	}
	
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	status, _ := strconv.Atoi(c.DefaultQuery("status", "-1"))
	
	// TODO: 实现获取提现记录列表
	response.Success(c, gin.H{
		"merchant_id": merchantID,
		"page":        page,
		"page_size":   pageSize,
		"status":      status,
	})
}

// GetWithdrawStats 获取提现统计
func (h *SettlementHandler) GetWithdrawStats(c *gin.Context) {
	// 从上下文获取商户ID
	merchantID, exists := c.Get("merchant_id")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "未授权", "无法获取商户信息")
		return
	}
	
	// 解析时间参数
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")
	
	var startDate, endDate time.Time
	var err error
	
	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "开始日期格式错误")
			return
		}
	}
	
	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "结束日期格式错误")
			return
		}
	}
	
	result, err := h.settlementService.GetWithdrawStats(c.Request.Context(), merchantID.(uint), startDate, endDate)
	if err != nil {
		if bizErr, ok := err.(*interfaces.BusinessError); ok {
			response.Error(c, http.StatusBadRequest, bizErr.Message, bizErr.Details)
			return
		}
		response.Error(c, http.StatusInternalServerError, "获取提现统计失败", err.Error())
		return
	}
	
	response.Success(c, result)
}

// GetReconciliation 获取对账记录详情
func (h *SettlementHandler) GetReconciliation(c *gin.Context) {
	reconciliationNo := c.Param("reconciliation_no")
	if reconciliationNo == "" {
		response.Error(c, http.StatusBadRequest, "参数错误", "对账单号不能为空")
		return
	}
	
	// TODO: 实现获取对账记录详情
	response.Success(c, gin.H{"reconciliation_no": reconciliationNo})
}

// ListReconciliations 获取对账记录列表
func (h *SettlementHandler) ListReconciliations(c *gin.Context) {
	// 从上下文获取商户ID
	merchantID, exists := c.Get("merchant_id")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "未授权", "无法获取商户信息")
		return
	}
	
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	channel := c.Query("channel")
	
	// TODO: 实现获取对账记录列表
	response.Success(c, gin.H{
		"merchant_id": merchantID,
		"page":        page,
		"page_size":   pageSize,
		"channel":     channel,
	})
}

// GetReconciliationStats 获取对账统计
func (h *SettlementHandler) GetReconciliationStats(c *gin.Context) {
	// 从上下文获取商户ID
	merchantID, exists := c.Get("merchant_id")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "未授权", "无法获取商户信息")
		return
	}
	
	// 解析时间参数
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")
	
	var startDate, endDate time.Time
	var err error
	
	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "开始日期格式错误")
			return
		}
	}
	
	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "结束日期格式错误")
			return
		}
	}
	
	result, err := h.settlementService.GetReconciliationStats(c.Request.Context(), merchantID.(uint), startDate, endDate)
	if err != nil {
		if bizErr, ok := err.(*interfaces.BusinessError); ok {
			response.Error(c, http.StatusBadRequest, bizErr.Message, bizErr.Details)
			return
		}
		response.Error(c, http.StatusInternalServerError, "获取对账统计失败", err.Error())
		return
	}
	
	response.Success(c, result)
}

// AdminListSettlements 管理员获取结算订单列表
func (h *SettlementHandler) AdminListSettlements(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	status, _ := strconv.Atoi(c.DefaultQuery("status", "-1"))
	merchantIDStr := c.Query("merchant_id")

	var merchantID uint
	if merchantIDStr != "" {
		id, err := strconv.ParseUint(merchantIDStr, 10, 32)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "商户ID格式错误")
			return
		}
		merchantID = uint(id)
	}

	// TODO: 实现管理员获取结算订单列表
	response.Success(c, gin.H{
		"merchant_id": merchantID,
		"page":        page,
		"page_size":   pageSize,
		"status":      status,
	})
}

// ProcessSettlement 处理结算订单
func (h *SettlementHandler) ProcessSettlement(c *gin.Context) {
	settlementNo := c.Param("settlement_no")
	if settlementNo == "" {
		response.Error(c, http.StatusBadRequest, "参数错误", "结算单号不能为空")
		return
	}

	err := h.settlementService.ProcessSettlement(c.Request.Context(), settlementNo)
	if err != nil {
		if bizErr, ok := err.(*interfaces.BusinessError); ok {
			response.Error(c, http.StatusBadRequest, bizErr.Message, bizErr.Details)
			return
		}
		response.Error(c, http.StatusInternalServerError, "处理结算订单失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "处理成功"})
}

// AdminGetSettlementStats 管理员获取结算统计
func (h *SettlementHandler) AdminGetSettlementStats(c *gin.Context) {
	// 解析查询参数
	merchantIDStr := c.Query("merchant_id")
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	var merchantID uint
	if merchantIDStr != "" {
		id, err := strconv.ParseUint(merchantIDStr, 10, 32)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "商户ID格式错误")
			return
		}
		merchantID = uint(id)
	}

	var startDate, endDate time.Time
	var err error

	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "开始日期格式错误")
			return
		}
	}

	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "结束日期格式错误")
			return
		}
	}

	result, err := h.settlementService.GetSettlementStats(c.Request.Context(), merchantID, startDate, endDate)
	if err != nil {
		if bizErr, ok := err.(*interfaces.BusinessError); ok {
			response.Error(c, http.StatusBadRequest, bizErr.Message, bizErr.Details)
			return
		}
		response.Error(c, http.StatusInternalServerError, "获取结算统计失败", err.Error())
		return
	}

	response.Success(c, result)
}

// AdminListWithdraws 管理员获取提现记录列表
func (h *SettlementHandler) AdminListWithdraws(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	status, _ := strconv.Atoi(c.DefaultQuery("status", "-1"))
	merchantIDStr := c.Query("merchant_id")

	var merchantID uint
	if merchantIDStr != "" {
		id, err := strconv.ParseUint(merchantIDStr, 10, 32)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "商户ID格式错误")
			return
		}
		merchantID = uint(id)
	}

	// TODO: 实现管理员获取提现记录列表
	response.Success(c, gin.H{
		"merchant_id": merchantID,
		"page":        page,
		"page_size":   pageSize,
		"status":      status,
	})
}

// ProcessWithdraw 处理提现申请
func (h *SettlementHandler) ProcessWithdraw(c *gin.Context) {
	withdrawNo := c.Param("withdraw_no")
	if withdrawNo == "" {
		response.Error(c, http.StatusBadRequest, "参数错误", "提现单号不能为空")
		return
	}

	err := h.settlementService.ProcessWithdraw(c.Request.Context(), withdrawNo)
	if err != nil {
		if bizErr, ok := err.(*interfaces.BusinessError); ok {
			response.Error(c, http.StatusBadRequest, bizErr.Message, bizErr.Details)
			return
		}
		response.Error(c, http.StatusInternalServerError, "处理提现申请失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "处理成功"})
}

// AdminGetWithdrawStats 管理员获取提现统计
func (h *SettlementHandler) AdminGetWithdrawStats(c *gin.Context) {
	// 解析查询参数
	merchantIDStr := c.Query("merchant_id")
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	var merchantID uint
	if merchantIDStr != "" {
		id, err := strconv.ParseUint(merchantIDStr, 10, 32)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "商户ID格式错误")
			return
		}
		merchantID = uint(id)
	}

	var startDate, endDate time.Time
	var err error

	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "开始日期格式错误")
			return
		}
	}

	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "结束日期格式错误")
			return
		}
	}

	result, err := h.settlementService.GetWithdrawStats(c.Request.Context(), merchantID, startDate, endDate)
	if err != nil {
		if bizErr, ok := err.(*interfaces.BusinessError); ok {
			response.Error(c, http.StatusBadRequest, bizErr.Message, bizErr.Details)
			return
		}
		response.Error(c, http.StatusInternalServerError, "获取提现统计失败", err.Error())
		return
	}

	response.Success(c, result)
}

// CreateReconciliation 创建对账记录
func (h *SettlementHandler) CreateReconciliation(c *gin.Context) {
	var req interfaces.CreateReconciliationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	result, err := h.settlementService.CreateReconciliation(c.Request.Context(), &req)
	if err != nil {
		if bizErr, ok := err.(*interfaces.BusinessError); ok {
			response.Error(c, http.StatusBadRequest, bizErr.Message, bizErr.Details)
			return
		}
		response.Error(c, http.StatusInternalServerError, "创建对账记录失败", err.Error())
		return
	}

	response.Success(c, result)
}

// AdminListReconciliations 管理员获取对账记录列表
func (h *SettlementHandler) AdminListReconciliations(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	status, _ := strconv.Atoi(c.DefaultQuery("status", "-1"))
	merchantIDStr := c.Query("merchant_id")
	channel := c.Query("channel")

	var merchantID uint
	if merchantIDStr != "" {
		id, err := strconv.ParseUint(merchantIDStr, 10, 32)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "商户ID格式错误")
			return
		}
		merchantID = uint(id)
	}

	// TODO: 实现管理员获取对账记录列表
	response.Success(c, gin.H{
		"merchant_id": merchantID,
		"page":        page,
		"page_size":   pageSize,
		"status":      status,
		"channel":     channel,
	})
}

// ProcessReconciliation 处理对账记录
func (h *SettlementHandler) ProcessReconciliation(c *gin.Context) {
	reconciliationNo := c.Param("reconciliation_no")
	if reconciliationNo == "" {
		response.Error(c, http.StatusBadRequest, "参数错误", "对账单号不能为空")
		return
	}

	err := h.settlementService.ProcessReconciliation(c.Request.Context(), reconciliationNo)
	if err != nil {
		if bizErr, ok := err.(*interfaces.BusinessError); ok {
			response.Error(c, http.StatusBadRequest, bizErr.Message, bizErr.Details)
			return
		}
		response.Error(c, http.StatusInternalServerError, "处理对账记录失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "处理成功"})
}

// AdminGetReconciliationStats 管理员获取对账统计
func (h *SettlementHandler) AdminGetReconciliationStats(c *gin.Context) {
	// 解析查询参数
	merchantIDStr := c.Query("merchant_id")
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	var merchantID uint
	if merchantIDStr != "" {
		id, err := strconv.ParseUint(merchantIDStr, 10, 32)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "商户ID格式错误")
			return
		}
		merchantID = uint(id)
	}

	var startDate, endDate time.Time
	var err error

	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "开始日期格式错误")
			return
		}
	}

	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "参数错误", "结束日期格式错误")
			return
		}
	}

	result, err := h.settlementService.GetReconciliationStats(c.Request.Context(), merchantID, startDate, endDate)
	if err != nil {
		if bizErr, ok := err.(*interfaces.BusinessError); ok {
			response.Error(c, http.StatusBadRequest, bizErr.Message, bizErr.Details)
			return
		}
		response.Error(c, http.StatusInternalServerError, "获取对账统计失败", err.Error())
		return
	}

	response.Success(c, result)
}
