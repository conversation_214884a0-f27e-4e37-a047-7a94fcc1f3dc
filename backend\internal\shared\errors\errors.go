package errors

import (
	"fmt"
	"payment-gateway/internal/shared/constants"
)

// BusinessError 业务错误结构
type BusinessError struct {
	Code    int    `json:"code"`    // 错误码
	Message string `json:"message"` // 错误信息
	Details string `json:"details"` // 详细信息
}

// Error 实现error接口
func (e *BusinessError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("code: %d, message: %s, details: %s", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("code: %d, message: %s", e.Code, e.Message)
}

// NewBusinessError 创建业务错误
func NewBusinessError(code int, message string, details ...string) *BusinessError {
	err := &BusinessError{
		Code:    code,
		Message: message,
	}
	if len(details) > 0 {
		err.Details = details[0]
	}
	return err
}

// NewDatabaseError 创建数据库错误
func NewDatabaseError(message string, details ...string) *BusinessError {
	return NewBusinessError(constants.ErrCodeInternalError, message, details...)
}

// NewNotFoundError 创建资源不存在错误
func NewNotFoundError(message string, details ...string) *BusinessError {
	return NewBusinessError(constants.ErrCodeNotFound, message, details...)
}

// NewValidationError 创建验证错误
func NewValidationError(message string, details ...string) *BusinessError {
	return NewBusinessError(constants.ErrCodeInvalidParams, message, details...)
}

// NewUnauthorizedError 创建未授权错误
func NewUnauthorizedError(message string, details ...string) *BusinessError {
	return NewBusinessError(constants.ErrCodeUnauthorized, message, details...)
}

// NewForbiddenError 创建禁止访问错误
func NewForbiddenError(message string, details ...string) *BusinessError {
	return NewBusinessError(constants.ErrCodeForbidden, message, details...)
}

// NewConflictError 创建冲突错误
func NewConflictError(message string, details ...string) *BusinessError {
	return NewBusinessError(constants.ErrCodeConflict, message, details...)
}

// NewInternalError 创建内部错误
func NewInternalError(message string, details ...string) *BusinessError {
	return NewBusinessError(constants.ErrCodeInternalError, message, details...)
}

// 预定义的业务错误
var (
	// 通用错误
	ErrInvalidParams      = NewBusinessError(constants.ErrCodeInvalidParams, "参数错误")
	ErrUnauthorized       = NewBusinessError(constants.ErrCodeUnauthorized, "未授权")
	ErrForbidden          = NewBusinessError(constants.ErrCodeForbidden, "禁止访问")
	ErrNotFound           = NewBusinessError(constants.ErrCodeNotFound, "资源不存在")
	ErrInternalError      = NewBusinessError(constants.ErrCodeInternalError, "内部错误")
	ErrServiceUnavailable = NewBusinessError(constants.ErrCodeServiceUnavailable, "服务不可用")
	ErrRateLimitExceeded  = NewBusinessError(constants.ErrCodeRateLimitExceeded, "请求频率超限")
	ErrSignatureInvalid   = NewBusinessError(constants.ErrCodeSignatureInvalid, "签名验证失败")
	ErrTimestampExpired   = NewBusinessError(constants.ErrCodeTimestampExpired, "时间戳过期")

	// 用户相关错误
	ErrUserNotFound      = NewBusinessError(constants.ErrCodeUserNotFound, "用户不存在")
	ErrUserExists        = NewBusinessError(constants.ErrCodeUserExists, "用户已存在")
	ErrPasswordIncorrect = NewBusinessError(constants.ErrCodePasswordIncorrect, "密码错误")
	ErrUserDisabled      = NewBusinessError(constants.ErrCodeUserDisabled, "用户已禁用")
	ErrUserNotVerified   = NewBusinessError(constants.ErrCodeUserNotVerified, "用户未验证")
	ErrTokenExpired      = NewBusinessError(constants.ErrCodeTokenExpired, "Token已过期")
	ErrTokenInvalid      = NewBusinessError(constants.ErrCodeTokenInvalid, "Token无效")
	ErrPermissionDenied  = NewBusinessError(constants.ErrCodePermissionDenied, "权限不足")

	// 商户相关错误
	ErrMerchantNotFound    = NewBusinessError(constants.ErrCodeMerchantNotFound, "商户不存在")
	ErrMerchantExists      = NewBusinessError(constants.ErrCodeMerchantExists, "商户已存在")
	ErrMerchantDisabled    = NewBusinessError(constants.ErrCodeMerchantDisabled, "商户已禁用")
	ErrMerchantNotApproved = NewBusinessError(constants.ErrCodeMerchantNotApproved, "商户未审核通过")
	ErrAppNotFound         = NewBusinessError(constants.ErrCodeAppNotFound, "应用不存在")
	ErrAppDisabled         = NewBusinessError(constants.ErrCodeAppDisabled, "应用已禁用")
	ErrBalanceInsufficient = NewBusinessError(constants.ErrCodeBalanceInsufficient, "余额不足")
	ErrBalanceError        = NewBusinessError(constants.ErrCodeBalanceError, "余额操作失败")

	// 支付相关错误
	ErrOrderNotFound       = NewBusinessError(constants.ErrCodeOrderNotFound, "订单不存在")
	ErrOrderExists         = NewBusinessError(constants.ErrCodeOrderExists, "订单已存在")
	ErrOrderStatusInvalid  = NewBusinessError(constants.ErrCodeOrderStatusInvalid, "订单状态无效")
	ErrOrderExpired        = NewBusinessError(constants.ErrCodeOrderExpired, "订单已过期")
	ErrOrderCancelled      = NewBusinessError(constants.ErrCodeOrderCancelled, "订单已取消")
	ErrOrderPaid           = NewBusinessError(constants.ErrCodeOrderPaid, "订单已支付")
	ErrAmountInvalid       = NewBusinessError(constants.ErrCodeAmountInvalid, "金额无效")
	ErrChannelNotSupported = NewBusinessError(constants.ErrCodeChannelNotSupported, "支付渠道不支持")
	ErrChannelDisabled     = NewBusinessError(constants.ErrCodeChannelDisabled, "支付渠道已禁用")
	ErrPaymentFailed       = NewBusinessError(constants.ErrCodePaymentFailed, "支付失败")
	ErrRefundFailed        = NewBusinessError(constants.ErrCodeRefundFailed, "退款失败")
	ErrRefundNotAllowed    = NewBusinessError(constants.ErrCodeRefundNotAllowed, "不允许退款")

	// 通知相关错误
	ErrNotificationFailed = NewBusinessError(constants.ErrCodeNotificationFailed, "通知发送失败")
	ErrEmailFailed        = NewBusinessError(constants.ErrCodeEmailFailed, "邮件发送失败")
	ErrSMSFailed          = NewBusinessError(constants.ErrCodeSMSFailed, "短信发送失败")
	ErrTemplateNotFound   = NewBusinessError(constants.ErrCodeTemplateNotFound, "模板不存在")
	ErrTemplateInvalid    = NewBusinessError(constants.ErrCodeTemplateInvalid, "模板无效")

	// 结算相关错误
	ErrSettlementFailed     = NewBusinessError(constants.ErrCodeSettlementFailed, "结算失败")
	ErrWithdrawFailed       = NewBusinessError(constants.ErrCodeWithdrawFailed, "提现失败")
	ErrWithdrawNotAllowed   = NewBusinessError(constants.ErrCodeWithdrawNotAllowed, "不允许提现")
	ErrReconciliationFailed = NewBusinessError(constants.ErrCodeReconciliationFailed, "对账失败")
)

// IsBusinessError 判断是否为业务错误
func IsBusinessError(err error) bool {
	_, ok := err.(*BusinessError)
	return ok
}

// GetBusinessError 获取业务错误
func GetBusinessError(err error) *BusinessError {
	if bizErr, ok := err.(*BusinessError); ok {
		return bizErr
	}
	return nil
}

// WrapError 包装错误为业务错误
func WrapError(err error, code int, message string) *BusinessError {
	return NewBusinessError(code, message, err.Error())
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Code      int    `json:"code"`       // 错误码
	Message   string `json:"message"`    // 错误信息
	Details   string `json:"details"`    // 详细信息
	RequestID string `json:"request_id"` // 请求ID
	Timestamp int64  `json:"timestamp"`  // 时间戳
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(err error, requestID string, timestamp int64) *ErrorResponse {
	if bizErr := GetBusinessError(err); bizErr != nil {
		return &ErrorResponse{
			Code:      bizErr.Code,
			Message:   bizErr.Message,
			Details:   bizErr.Details,
			RequestID: requestID,
			Timestamp: timestamp,
		}
	}

	// 非业务错误，返回内部错误
	return &ErrorResponse{
		Code:      constants.ErrCodeInternalError,
		Message:   "内部错误",
		Details:   err.Error(),
		RequestID: requestID,
		Timestamp: timestamp,
	}
}

// ValidationError 验证错误结构
type ValidationError struct {
	Field   string      `json:"field"`   // 字段名
	Message string      `json:"message"` // 错误信息
	Value   interface{} `json:"value"`   // 字段值
}

// ValidationErrors 验证错误列表
type ValidationErrors []ValidationError

// Error 实现error接口
func (ve ValidationErrors) Error() string {
	if len(ve) == 0 {
		return "validation failed"
	}
	return fmt.Sprintf("validation failed: %s", ve[0].Message)
}

// NewValidationFieldError 创建字段验证错误
func NewValidationFieldError(field, message string, value interface{}) ValidationError {
	return ValidationError{
		Field:   field,
		Message: message,
		Value:   value,
	}
}

// AddValidationError 添加验证错误
func (ve *ValidationErrors) Add(field, message string, value interface{}) {
	*ve = append(*ve, NewValidationFieldError(field, message, value))
}

// HasErrors 是否有验证错误
func (ve ValidationErrors) HasErrors() bool {
	return len(ve) > 0
}

// ToBusinessError 转换为业务错误
func (ve ValidationErrors) ToBusinessError() *BusinessError {
	if !ve.HasErrors() {
		return nil
	}

	details := ""
	for i, err := range ve {
		if i > 0 {
			details += "; "
		}
		details += fmt.Sprintf("%s: %s", err.Field, err.Message)
	}

	return NewBusinessError(constants.ErrCodeInvalidParams, "参数验证失败", details)
}

// ErrorCode 错误码映射
var ErrorCodeMap = map[int]string{
	constants.ErrCodeSuccess:              "成功",
	constants.ErrCodeInvalidParams:        "参数错误",
	constants.ErrCodeUnauthorized:         "未授权",
	constants.ErrCodeForbidden:            "禁止访问",
	constants.ErrCodeNotFound:             "资源不存在",
	constants.ErrCodeInternalError:        "内部错误",
	constants.ErrCodeServiceUnavailable:   "服务不可用",
	constants.ErrCodeRateLimitExceeded:    "请求频率超限",
	constants.ErrCodeSignatureInvalid:     "签名验证失败",
	constants.ErrCodeTimestampExpired:     "时间戳过期",
	constants.ErrCodeUserNotFound:         "用户不存在",
	constants.ErrCodeUserExists:           "用户已存在",
	constants.ErrCodePasswordIncorrect:    "密码错误",
	constants.ErrCodeUserDisabled:         "用户已禁用",
	constants.ErrCodeUserNotVerified:      "用户未验证",
	constants.ErrCodeTokenExpired:         "Token已过期",
	constants.ErrCodeTokenInvalid:         "Token无效",
	constants.ErrCodePermissionDenied:     "权限不足",
	constants.ErrCodeMerchantNotFound:     "商户不存在",
	constants.ErrCodeMerchantExists:       "商户已存在",
	constants.ErrCodeMerchantDisabled:     "商户已禁用",
	constants.ErrCodeMerchantFrozen:       "商户已冻结",
	constants.ErrCodeMerchantNotApproved:  "商户未审核通过",
	constants.ErrCodeAppNotFound:          "应用不存在",
	constants.ErrCodeAppDisabled:          "应用已禁用",
	constants.ErrCodeBalanceInsufficient:  "余额不足",
	constants.ErrCodeBalanceError:         "余额操作失败",
	constants.ErrCodeOrderNotFound:        "订单不存在",
	constants.ErrCodeOrderExists:          "订单已存在",
	constants.ErrCodeOrderStatusInvalid:   "订单状态无效",
	constants.ErrCodeOrderExpired:         "订单已过期",
	constants.ErrCodeOrderCancelled:       "订单已取消",
	constants.ErrCodeOrderPaid:            "订单已支付",
	constants.ErrCodeAmountInvalid:        "金额无效",
	constants.ErrCodeChannelNotSupported:  "支付渠道不支持",
	constants.ErrCodeChannelDisabled:      "支付渠道已禁用",
	constants.ErrCodePaymentFailed:        "支付失败",
	constants.ErrCodeRefundFailed:         "退款失败",
	constants.ErrCodeRefundNotAllowed:     "不允许退款",
	constants.ErrCodeNotificationFailed:   "通知发送失败",
	constants.ErrCodeEmailFailed:          "邮件发送失败",
	constants.ErrCodeSMSFailed:            "短信发送失败",
	constants.ErrCodeTemplateNotFound:     "模板不存在",
	constants.ErrCodeTemplateInvalid:      "模板无效",
	constants.ErrCodeSettlementFailed:     "结算失败",
	constants.ErrCodeWithdrawFailed:       "提现失败",
	constants.ErrCodeWithdrawNotAllowed:   "不允许提现",
	constants.ErrCodeReconciliationFailed: "对账失败",
}

// GetErrorMessage 根据错误码获取错误信息
func GetErrorMessage(code int) string {
	if msg, ok := ErrorCodeMap[code]; ok {
		return msg
	}
	return "未知错误"
}
