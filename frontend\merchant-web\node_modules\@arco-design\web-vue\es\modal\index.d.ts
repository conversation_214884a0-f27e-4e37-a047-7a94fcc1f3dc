import type { App, AppContext } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import type { ModalConfig } from './interface';
declare const Modal: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
        visible: {
            type: BooleanConstructor;
            default: undefined;
        };
        defaultVisible: {
            type: BooleanConstructor;
            default: boolean;
        };
        width: {
            type: (StringConstructor | NumberConstructor)[];
        };
        top: {
            type: (StringConstructor | NumberConstructor)[];
        };
        mask: {
            type: BooleanConstructor;
            default: boolean;
        };
        title: {
            type: StringConstructor;
        };
        titleAlign: {
            type: import("vue").PropType<"center" | "start">;
            default: string;
        };
        alignCenter: {
            type: BooleanConstructor;
            default: boolean;
        };
        unmountOnClose: BooleanConstructor;
        maskClosable: {
            type: BooleanConstructor;
            default: boolean;
        };
        hideCancel: {
            type: BooleanConstructor;
            default: boolean;
        };
        simple: {
            type: BooleanConstructor;
            default: (props: any) => any;
        };
        closable: {
            type: BooleanConstructor;
            default: boolean;
        };
        okText: StringConstructor;
        cancelText: StringConstructor;
        okLoading: {
            type: BooleanConstructor;
            default: boolean;
        };
        okButtonProps: {
            type: import("vue").PropType<import("..").ButtonProps>;
        };
        cancelButtonProps: {
            type: import("vue").PropType<import("..").ButtonProps>;
        };
        footer: {
            type: BooleanConstructor;
            default: boolean;
        };
        renderToBody: {
            type: BooleanConstructor;
            default: boolean;
        };
        popupContainer: {
            type: import("vue").PropType<string | HTMLElement>;
            default: string;
        };
        maskStyle: {
            type: import("vue").PropType<import("vue").CSSProperties>;
        };
        modalClass: {
            type: import("vue").PropType<string | any[]>;
        };
        modalStyle: {
            type: import("vue").PropType<import("vue").CSSProperties>;
        };
        onBeforeOk: {
            type: import("vue").PropType<(done: (closed: boolean) => void) => boolean | void | Promise<boolean | void>>;
        };
        onBeforeCancel: {
            type: import("vue").PropType<() => boolean>;
        };
        escToClose: {
            type: BooleanConstructor;
            default: boolean;
        };
        draggable: {
            type: BooleanConstructor;
            default: boolean;
        };
        fullscreen: {
            type: BooleanConstructor;
            default: boolean;
        };
        maskAnimationName: {
            type: StringConstructor;
            default: (props: Record<string, any>) => "fade-in-standard" | "fade-modal";
        };
        modalAnimationName: {
            type: StringConstructor;
            default: (props: Record<string, any>) => "zoom-in" | "zoom-modal";
        };
        bodyClass: {
            type: import("vue").PropType<string | any[]>;
        };
        bodyStyle: {
            type: import("vue").PropType<import("vue").StyleValue>;
        };
        messageType: {
            type: import("vue").PropType<"error" | "success" | "warning" | "info">;
        };
        hideTitle: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{
        onOpen?: (() => any) | undefined;
        onCancel?: ((e: Event) => any) | undefined;
        onClose?: (() => any) | undefined;
        "onUpdate:visible"?: ((visible: boolean) => any) | undefined;
        onOk?: ((e: Event) => any) | undefined;
        onBeforeOpen?: (() => any) | undefined;
        onBeforeClose?: (() => any) | undefined;
    }>, {
        prefixCls: string;
        mounted: import("vue").Ref<boolean, boolean>;
        computedVisible: import("vue").ComputedRef<boolean>;
        containerRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
        wrapperRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
        mergedModalStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
        okDisplayText: import("vue").ComputedRef<string>;
        cancelDisplayText: import("vue").ComputedRef<string>;
        zIndex: Readonly<import("vue").Ref<number, number>>;
        handleOk: (e: Event) => Promise<void>;
        handleCancel: (e: Event) => void;
        handleMaskClick: (e: Event) => void;
        handleMaskMouseDown: (ev: Event) => void;
        handleOpen: () => void;
        handleClose: () => void;
        mergedOkLoading: import("vue").ComputedRef<boolean>;
        modalRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
        wrapperCls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        modalCls: import("vue").ComputedRef<(string | any[] | {
            [x: string]: boolean;
        } | undefined)[]>;
        teleportContainer: import("vue").Ref<string | HTMLElement | undefined, string | HTMLElement | undefined>;
        handleMoveDown: (ev: MouseEvent) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        'update:visible': (visible: boolean) => true;
        ok: (e: Event) => true;
        cancel: (e: Event) => true;
        open: () => true;
        close: () => true;
        beforeOpen: () => true;
        beforeClose: () => true;
    }, import("vue").PublicProps, {
        unmountOnClose: boolean;
        popupContainer: string | HTMLElement;
        renderToBody: boolean;
        visible: boolean;
        closable: boolean;
        footer: boolean;
        mask: boolean;
        defaultVisible: boolean;
        maskClosable: boolean;
        escToClose: boolean;
        draggable: boolean;
        simple: boolean;
        okLoading: boolean;
        hideCancel: boolean;
        titleAlign: "center" | "start";
        alignCenter: boolean;
        fullscreen: boolean;
        maskAnimationName: string;
        modalAnimationName: string;
        hideTitle: boolean;
    }, true, {}, {}, {
        ClientOnly: import("vue").DefineComponent<{}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
            [key: string]: any;
        }>[] | null | undefined, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        ArcoButton: {
            new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
                type: {
                    type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                };
                shape: {
                    type: import("vue").PropType<"round" | "circle" | "square">;
                };
                status: {
                    type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
                };
                size: {
                    type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
                };
                long: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                loading: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                disabled: {
                    type: BooleanConstructor;
                };
                htmlType: {
                    type: StringConstructor;
                    default: string;
                };
                autofocus: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                href: StringConstructor;
            }>> & Readonly<{
                onClick?: ((ev: MouseEvent) => any) | undefined;
            }>, {
                prefixCls: string;
                cls: import("vue").ComputedRef<(string | {
                    [x: string]: boolean;
                })[]>;
                mergedDisabled: import("vue").ComputedRef<boolean>;
                handleClick: (ev: MouseEvent) => void;
            }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
                click: (ev: MouseEvent) => true;
            }, import("vue").PublicProps, {
                disabled: boolean;
                autofocus: boolean;
                loading: boolean;
                long: boolean;
                htmlType: string;
            }, true, {}, {}, {
                IconLoading: any;
            } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
                P: {};
                B: {};
                D: {};
                C: {};
                M: {};
                Defaults: {};
            }, Readonly<import("vue").ExtractPropTypes<{
                type: {
                    type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                };
                shape: {
                    type: import("vue").PropType<"round" | "circle" | "square">;
                };
                status: {
                    type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
                };
                size: {
                    type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
                };
                long: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                loading: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                disabled: {
                    type: BooleanConstructor;
                };
                htmlType: {
                    type: StringConstructor;
                    default: string;
                };
                autofocus: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                href: StringConstructor;
            }>> & Readonly<{
                onClick?: ((ev: MouseEvent) => any) | undefined;
            }>, {
                prefixCls: string;
                cls: import("vue").ComputedRef<(string | {
                    [x: string]: boolean;
                })[]>;
                mergedDisabled: import("vue").ComputedRef<boolean>;
                handleClick: (ev: MouseEvent) => void;
            }, {}, {}, {}, {
                disabled: boolean;
                autofocus: boolean;
                loading: boolean;
                long: boolean;
                htmlType: string;
            }>;
            __isFragment?: undefined;
            __isTeleport?: undefined;
            __isSuspense?: undefined;
        } & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            shape: {
                type: import("vue").PropType<"round" | "circle" | "square">;
            };
            status: {
                type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            };
            long: {
                type: BooleanConstructor;
                default: boolean;
            };
            loading: {
                type: BooleanConstructor;
                default: boolean;
            };
            disabled: {
                type: BooleanConstructor;
            };
            htmlType: {
                type: StringConstructor;
                default: string;
            };
            autofocus: {
                type: BooleanConstructor;
                default: boolean;
            };
            href: StringConstructor;
        }>> & Readonly<{
            onClick?: ((ev: MouseEvent) => any) | undefined;
        }>, {
            prefixCls: string;
            cls: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            mergedDisabled: import("vue").ComputedRef<boolean>;
            handleClick: (ev: MouseEvent) => void;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            click: (ev: MouseEvent) => true;
        }, string, {
            disabled: boolean;
            autofocus: boolean;
            loading: boolean;
            long: boolean;
            htmlType: string;
        }, {}, string, {}, {
            IconLoading: any;
        } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
            Group: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
                type: {
                    type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                };
                status: {
                    type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
                };
                shape: {
                    type: import("vue").PropType<"round" | "circle" | "square">;
                };
                size: {
                    type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
                };
                disabled: {
                    type: BooleanConstructor;
                };
            }>, {
                prefixCls: string;
            }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
                type: {
                    type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
                };
                status: {
                    type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
                };
                shape: {
                    type: import("vue").PropType<"round" | "circle" | "square">;
                };
                size: {
                    type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
                };
                disabled: {
                    type: BooleanConstructor;
                };
            }>> & Readonly<{}>, {
                disabled: boolean;
            }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
            install: (app: App<any>, options?: ArcoOptions | undefined) => void;
        };
        IconHover: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            prefix: {
                type: StringConstructor;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
                default: string;
            };
            disabled: {
                type: BooleanConstructor;
                default: boolean;
            };
        }>, {
            prefixCls: string;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            prefix: {
                type: StringConstructor;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
                default: string;
            };
            disabled: {
                type: BooleanConstructor;
                default: boolean;
            };
        }>> & Readonly<{}>, {
            disabled: boolean;
            size: "mini" | "medium" | "large" | "small";
        }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        IconClose: any;
        IconInfoCircleFill: any;
        IconCheckCircleFill: any;
        IconExclamationCircleFill: any;
        IconCloseCircleFill: any;
    } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        visible: {
            type: BooleanConstructor;
            default: undefined;
        };
        defaultVisible: {
            type: BooleanConstructor;
            default: boolean;
        };
        width: {
            type: (StringConstructor | NumberConstructor)[];
        };
        top: {
            type: (StringConstructor | NumberConstructor)[];
        };
        mask: {
            type: BooleanConstructor;
            default: boolean;
        };
        title: {
            type: StringConstructor;
        };
        titleAlign: {
            type: import("vue").PropType<"center" | "start">;
            default: string;
        };
        alignCenter: {
            type: BooleanConstructor;
            default: boolean;
        };
        unmountOnClose: BooleanConstructor;
        maskClosable: {
            type: BooleanConstructor;
            default: boolean;
        };
        hideCancel: {
            type: BooleanConstructor;
            default: boolean;
        };
        simple: {
            type: BooleanConstructor;
            default: (props: any) => any;
        };
        closable: {
            type: BooleanConstructor;
            default: boolean;
        };
        okText: StringConstructor;
        cancelText: StringConstructor;
        okLoading: {
            type: BooleanConstructor;
            default: boolean;
        };
        okButtonProps: {
            type: import("vue").PropType<import("..").ButtonProps>;
        };
        cancelButtonProps: {
            type: import("vue").PropType<import("..").ButtonProps>;
        };
        footer: {
            type: BooleanConstructor;
            default: boolean;
        };
        renderToBody: {
            type: BooleanConstructor;
            default: boolean;
        };
        popupContainer: {
            type: import("vue").PropType<string | HTMLElement>;
            default: string;
        };
        maskStyle: {
            type: import("vue").PropType<import("vue").CSSProperties>;
        };
        modalClass: {
            type: import("vue").PropType<string | any[]>;
        };
        modalStyle: {
            type: import("vue").PropType<import("vue").CSSProperties>;
        };
        onBeforeOk: {
            type: import("vue").PropType<(done: (closed: boolean) => void) => boolean | void | Promise<boolean | void>>;
        };
        onBeforeCancel: {
            type: import("vue").PropType<() => boolean>;
        };
        escToClose: {
            type: BooleanConstructor;
            default: boolean;
        };
        draggable: {
            type: BooleanConstructor;
            default: boolean;
        };
        fullscreen: {
            type: BooleanConstructor;
            default: boolean;
        };
        maskAnimationName: {
            type: StringConstructor;
            default: (props: Record<string, any>) => "fade-in-standard" | "fade-modal";
        };
        modalAnimationName: {
            type: StringConstructor;
            default: (props: Record<string, any>) => "zoom-in" | "zoom-modal";
        };
        bodyClass: {
            type: import("vue").PropType<string | any[]>;
        };
        bodyStyle: {
            type: import("vue").PropType<import("vue").StyleValue>;
        };
        messageType: {
            type: import("vue").PropType<"error" | "success" | "warning" | "info">;
        };
        hideTitle: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{
        onOpen?: (() => any) | undefined;
        onCancel?: ((e: Event) => any) | undefined;
        onClose?: (() => any) | undefined;
        "onUpdate:visible"?: ((visible: boolean) => any) | undefined;
        onOk?: ((e: Event) => any) | undefined;
        onBeforeOpen?: (() => any) | undefined;
        onBeforeClose?: (() => any) | undefined;
    }>, {
        prefixCls: string;
        mounted: import("vue").Ref<boolean, boolean>;
        computedVisible: import("vue").ComputedRef<boolean>;
        containerRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
        wrapperRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
        mergedModalStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
        okDisplayText: import("vue").ComputedRef<string>;
        cancelDisplayText: import("vue").ComputedRef<string>;
        zIndex: Readonly<import("vue").Ref<number, number>>;
        handleOk: (e: Event) => Promise<void>;
        handleCancel: (e: Event) => void;
        handleMaskClick: (e: Event) => void;
        handleMaskMouseDown: (ev: Event) => void;
        handleOpen: () => void;
        handleClose: () => void;
        mergedOkLoading: import("vue").ComputedRef<boolean>;
        modalRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
        wrapperCls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        modalCls: import("vue").ComputedRef<(string | any[] | {
            [x: string]: boolean;
        } | undefined)[]>;
        teleportContainer: import("vue").Ref<string | HTMLElement | undefined, string | HTMLElement | undefined>;
        handleMoveDown: (ev: MouseEvent) => void;
    }, {}, {}, {}, {
        unmountOnClose: boolean;
        popupContainer: string | HTMLElement;
        renderToBody: boolean;
        visible: boolean;
        closable: boolean;
        footer: boolean;
        mask: boolean;
        defaultVisible: boolean;
        maskClosable: boolean;
        escToClose: boolean;
        draggable: boolean;
        simple: boolean;
        okLoading: boolean;
        hideCancel: boolean;
        titleAlign: "center" | "start";
        alignCenter: boolean;
        fullscreen: boolean;
        maskAnimationName: string;
        modalAnimationName: string;
        hideTitle: boolean;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    visible: {
        type: BooleanConstructor;
        default: undefined;
    };
    defaultVisible: {
        type: BooleanConstructor;
        default: boolean;
    };
    width: {
        type: (StringConstructor | NumberConstructor)[];
    };
    top: {
        type: (StringConstructor | NumberConstructor)[];
    };
    mask: {
        type: BooleanConstructor;
        default: boolean;
    };
    title: {
        type: StringConstructor;
    };
    titleAlign: {
        type: import("vue").PropType<"center" | "start">;
        default: string;
    };
    alignCenter: {
        type: BooleanConstructor;
        default: boolean;
    };
    unmountOnClose: BooleanConstructor;
    maskClosable: {
        type: BooleanConstructor;
        default: boolean;
    };
    hideCancel: {
        type: BooleanConstructor;
        default: boolean;
    };
    simple: {
        type: BooleanConstructor;
        default: (props: any) => any;
    };
    closable: {
        type: BooleanConstructor;
        default: boolean;
    };
    okText: StringConstructor;
    cancelText: StringConstructor;
    okLoading: {
        type: BooleanConstructor;
        default: boolean;
    };
    okButtonProps: {
        type: import("vue").PropType<import("..").ButtonProps>;
    };
    cancelButtonProps: {
        type: import("vue").PropType<import("..").ButtonProps>;
    };
    footer: {
        type: BooleanConstructor;
        default: boolean;
    };
    renderToBody: {
        type: BooleanConstructor;
        default: boolean;
    };
    popupContainer: {
        type: import("vue").PropType<string | HTMLElement>;
        default: string;
    };
    maskStyle: {
        type: import("vue").PropType<import("vue").CSSProperties>;
    };
    modalClass: {
        type: import("vue").PropType<string | any[]>;
    };
    modalStyle: {
        type: import("vue").PropType<import("vue").CSSProperties>;
    };
    onBeforeOk: {
        type: import("vue").PropType<(done: (closed: boolean) => void) => boolean | void | Promise<boolean | void>>;
    };
    onBeforeCancel: {
        type: import("vue").PropType<() => boolean>;
    };
    escToClose: {
        type: BooleanConstructor;
        default: boolean;
    };
    draggable: {
        type: BooleanConstructor;
        default: boolean;
    };
    fullscreen: {
        type: BooleanConstructor;
        default: boolean;
    };
    maskAnimationName: {
        type: StringConstructor;
        default: (props: Record<string, any>) => "fade-in-standard" | "fade-modal";
    };
    modalAnimationName: {
        type: StringConstructor;
        default: (props: Record<string, any>) => "zoom-in" | "zoom-modal";
    };
    bodyClass: {
        type: import("vue").PropType<string | any[]>;
    };
    bodyStyle: {
        type: import("vue").PropType<import("vue").StyleValue>;
    };
    messageType: {
        type: import("vue").PropType<"error" | "success" | "warning" | "info">;
    };
    hideTitle: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onOpen?: (() => any) | undefined;
    onCancel?: ((e: Event) => any) | undefined;
    onClose?: (() => any) | undefined;
    "onUpdate:visible"?: ((visible: boolean) => any) | undefined;
    onOk?: ((e: Event) => any) | undefined;
    onBeforeOpen?: (() => any) | undefined;
    onBeforeClose?: (() => any) | undefined;
}>, {
    prefixCls: string;
    mounted: import("vue").Ref<boolean, boolean>;
    computedVisible: import("vue").ComputedRef<boolean>;
    containerRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
    wrapperRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
    mergedModalStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
    okDisplayText: import("vue").ComputedRef<string>;
    cancelDisplayText: import("vue").ComputedRef<string>;
    zIndex: Readonly<import("vue").Ref<number, number>>;
    handleOk: (e: Event) => Promise<void>;
    handleCancel: (e: Event) => void;
    handleMaskClick: (e: Event) => void;
    handleMaskMouseDown: (ev: Event) => void;
    handleOpen: () => void;
    handleClose: () => void;
    mergedOkLoading: import("vue").ComputedRef<boolean>;
    modalRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
    wrapperCls: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    modalCls: import("vue").ComputedRef<(string | any[] | {
        [x: string]: boolean;
    } | undefined)[]>;
    teleportContainer: import("vue").Ref<string | HTMLElement | undefined, string | HTMLElement | undefined>;
    handleMoveDown: (ev: MouseEvent) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    'update:visible': (visible: boolean) => true;
    ok: (e: Event) => true;
    cancel: (e: Event) => true;
    open: () => true;
    close: () => true;
    beforeOpen: () => true;
    beforeClose: () => true;
}, string, {
    unmountOnClose: boolean;
    popupContainer: string | HTMLElement;
    renderToBody: boolean;
    visible: boolean;
    closable: boolean;
    footer: boolean;
    mask: boolean;
    defaultVisible: boolean;
    maskClosable: boolean;
    escToClose: boolean;
    draggable: boolean;
    simple: boolean;
    okLoading: boolean;
    hideCancel: boolean;
    titleAlign: "center" | "start";
    alignCenter: boolean;
    fullscreen: boolean;
    maskAnimationName: string;
    modalAnimationName: string;
    hideTitle: boolean;
}, {}, string, {}, {
    ClientOnly: import("vue").DefineComponent<{}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>[] | null | undefined, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    ArcoButton: {
        new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            shape: {
                type: import("vue").PropType<"round" | "circle" | "square">;
            };
            status: {
                type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            };
            long: {
                type: BooleanConstructor;
                default: boolean;
            };
            loading: {
                type: BooleanConstructor;
                default: boolean;
            };
            disabled: {
                type: BooleanConstructor;
            };
            htmlType: {
                type: StringConstructor;
                default: string;
            };
            autofocus: {
                type: BooleanConstructor;
                default: boolean;
            };
            href: StringConstructor;
        }>> & Readonly<{
            onClick?: ((ev: MouseEvent) => any) | undefined;
        }>, {
            prefixCls: string;
            cls: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            mergedDisabled: import("vue").ComputedRef<boolean>;
            handleClick: (ev: MouseEvent) => void;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            click: (ev: MouseEvent) => true;
        }, import("vue").PublicProps, {
            disabled: boolean;
            autofocus: boolean;
            loading: boolean;
            long: boolean;
            htmlType: string;
        }, true, {}, {}, {
            IconLoading: any;
        } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
            P: {};
            B: {};
            D: {};
            C: {};
            M: {};
            Defaults: {};
        }, Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            shape: {
                type: import("vue").PropType<"round" | "circle" | "square">;
            };
            status: {
                type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            };
            long: {
                type: BooleanConstructor;
                default: boolean;
            };
            loading: {
                type: BooleanConstructor;
                default: boolean;
            };
            disabled: {
                type: BooleanConstructor;
            };
            htmlType: {
                type: StringConstructor;
                default: string;
            };
            autofocus: {
                type: BooleanConstructor;
                default: boolean;
            };
            href: StringConstructor;
        }>> & Readonly<{
            onClick?: ((ev: MouseEvent) => any) | undefined;
        }>, {
            prefixCls: string;
            cls: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            mergedDisabled: import("vue").ComputedRef<boolean>;
            handleClick: (ev: MouseEvent) => void;
        }, {}, {}, {}, {
            disabled: boolean;
            autofocus: boolean;
            loading: boolean;
            long: boolean;
            htmlType: string;
        }>;
        __isFragment?: undefined;
        __isTeleport?: undefined;
        __isSuspense?: undefined;
    } & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
        type: {
            type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
        };
        shape: {
            type: import("vue").PropType<"round" | "circle" | "square">;
        };
        status: {
            type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
        };
        long: {
            type: BooleanConstructor;
            default: boolean;
        };
        loading: {
            type: BooleanConstructor;
            default: boolean;
        };
        disabled: {
            type: BooleanConstructor;
        };
        htmlType: {
            type: StringConstructor;
            default: string;
        };
        autofocus: {
            type: BooleanConstructor;
            default: boolean;
        };
        href: StringConstructor;
    }>> & Readonly<{
        onClick?: ((ev: MouseEvent) => any) | undefined;
    }>, {
        prefixCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        mergedDisabled: import("vue").ComputedRef<boolean>;
        handleClick: (ev: MouseEvent) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        click: (ev: MouseEvent) => true;
    }, string, {
        disabled: boolean;
        autofocus: boolean;
        loading: boolean;
        long: boolean;
        htmlType: string;
    }, {}, string, {}, {
        IconLoading: any;
    } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
        Group: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            type: {
                type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            status: {
                type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
            };
            shape: {
                type: import("vue").PropType<"round" | "circle" | "square">;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            };
            disabled: {
                type: BooleanConstructor;
            };
        }>, {
            prefixCls: string;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            status: {
                type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
            };
            shape: {
                type: import("vue").PropType<"round" | "circle" | "square">;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            };
            disabled: {
                type: BooleanConstructor;
            };
        }>> & Readonly<{}>, {
            disabled: boolean;
        }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        install: (app: App<any>, options?: ArcoOptions | undefined) => void;
    };
    IconHover: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>, {
        prefixCls: string;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{}>, {
        disabled: boolean;
        size: "mini" | "medium" | "large" | "small";
    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    IconClose: any;
    IconInfoCircleFill: any;
    IconCheckCircleFill: any;
    IconExclamationCircleFill: any;
    IconCloseCircleFill: any;
} & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    install: (app: App, options?: ArcoOptions | undefined) => void;
    _context: AppContext | null;
    open: (config: ModalConfig, appContext?: AppContext | undefined) => import("./interface").ModalReturn;
    confirm: (config: ModalConfig, appContext?: AppContext | undefined) => import("./interface").ModalReturn;
    info: (config: ModalConfig, appContext?: AppContext | undefined) => import("./interface").ModalReturn;
    success: (config: ModalConfig, appContext?: AppContext | undefined) => import("./interface").ModalReturn;
    warning: (config: ModalConfig, appContext?: AppContext | undefined) => import("./interface").ModalReturn;
    error: (config: ModalConfig, appContext?: AppContext | undefined) => import("./interface").ModalReturn;
};
export type { ModalMethod, ModalConfig, ModalReturn } from './interface';
export default Modal;
