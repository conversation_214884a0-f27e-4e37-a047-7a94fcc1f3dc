import { TreeN<PERSON><PERSON><PERSON>, Key2TreeNode } from '../interface';
export default function useCheckedState(props: {
    defaultCheckedKeys: TreeNodeKey[] | undefined;
    checkedKeys: TreeNodeKey[] | undefined;
    halfCheckedKeys: TreeNodeKey[] | undefined;
    key2TreeNode: Key2TreeNode;
    checkStrictly: boolean;
    onlyCheckLeaf: boolean;
}): {
    checkedKeys: import("vue").ComputedRef<TreeNodeKey[]>;
    indeterminateKeys: import("vue").ComputedRef<TreeNodeKey[]>;
    setCheckedState(newCheckedKeys: <PERSON>NodeKey[], newIndeterminateKeys: <PERSON>NodeKey[], reinitialize?: boolean): TreeNodeKey[][];
};
