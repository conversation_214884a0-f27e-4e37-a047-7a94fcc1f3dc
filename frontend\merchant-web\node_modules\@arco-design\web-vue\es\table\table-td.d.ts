import { PropType, VNode } from 'vue';
import { TableColumnData, TableDataWithRaw, TableOperationColumn } from './interface';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    rowIndex: NumberConstructor;
    record: {
        type: PropType<TableDataWithRaw>;
        default: () => {};
    };
    column: {
        type: PropType<TableColumnData>;
        default: () => {};
    };
    type: {
        type: PropType<"normal" | "checkbox" | "radio" | "expand" | "operation">;
        default: string;
    };
    operations: {
        type: PropType<TableOperationColumn[]>;
        default: () => never[];
    };
    dataColumns: {
        type: PropType<TableColumnData[]>;
        default: () => never[];
    };
    colSpan: {
        type: NumberConstructor;
        default: number;
    };
    rowSpan: {
        type: NumberConstructor;
        default: number;
    };
    isFixedExpand: {
        type: BooleanConstructor;
        default: boolean;
    };
    containerWidth: {
        type: NumberConstructor;
    };
    showExpandBtn: {
        type: BooleanConstructor;
        default: boolean;
    };
    indentSize: {
        type: NumberConstructor;
        default: number;
    };
    renderExpandBtn: {
        type: PropType<(record: TableDataWithRaw, stopPropagation?: boolean | undefined) => VNode>;
    };
    summary: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, () => VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    rowIndex: NumberConstructor;
    record: {
        type: PropType<TableDataWithRaw>;
        default: () => {};
    };
    column: {
        type: PropType<TableColumnData>;
        default: () => {};
    };
    type: {
        type: PropType<"normal" | "checkbox" | "radio" | "expand" | "operation">;
        default: string;
    };
    operations: {
        type: PropType<TableOperationColumn[]>;
        default: () => never[];
    };
    dataColumns: {
        type: PropType<TableColumnData[]>;
        default: () => never[];
    };
    colSpan: {
        type: NumberConstructor;
        default: number;
    };
    rowSpan: {
        type: NumberConstructor;
        default: number;
    };
    isFixedExpand: {
        type: BooleanConstructor;
        default: boolean;
    };
    containerWidth: {
        type: NumberConstructor;
    };
    showExpandBtn: {
        type: BooleanConstructor;
        default: boolean;
    };
    indentSize: {
        type: NumberConstructor;
        default: number;
    };
    renderExpandBtn: {
        type: PropType<(record: TableDataWithRaw, stopPropagation?: boolean | undefined) => VNode>;
    };
    summary: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{}>, {
    column: TableColumnData;
    type: "normal" | "checkbox" | "radio" | "expand" | "operation";
    summary: boolean;
    indentSize: number;
    record: TableDataWithRaw;
    operations: TableOperationColumn[];
    dataColumns: TableColumnData[];
    colSpan: number;
    rowSpan: number;
    isFixedExpand: boolean;
    showExpandBtn: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
